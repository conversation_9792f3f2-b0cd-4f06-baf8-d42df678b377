import 'package:broker_settings/broker_settings.dart';
import 'package:campaigns/campaigns.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/fusion.dart';
import 'package:get_it/get_it.dart';
import 'package:host/host.dart';
import 'package:injectable/injectable.dart';
import 'package:login/login.dart';
import 'package:monitoring/monitoring.dart';
import 'package:onboarding/onboarding.dart';
import 'package:payment/payments.dart';
import 'package:user_account/user_account.dart';

import 'di_initializer.config.dart';

final diContainer = GetIt.instance;

@InjectableInit(
  externalPackageModulesBefore: [ExternalModule(MonitoringPackageModule)],
  externalPackageModulesAfter: [
    ExternalModule(UserAccountPackageModule),
    ExternalModule(BrokerSettingsPackageModule),
    ExternalModule(HostPackageModule),
    ExternalModule(ETraderPackageModule),
    ExternalModule(LoginPackageModule),
    ExternalModule(DuploPackageModule),
    ExternalModule(OnboardingPackageModule),
    ExternalModule(PaymentPackageModule),
    ExternalModule(DomainPackageModule),
    ExternalModule(CampaignsPackageModule),
  ],
)
Future<void> configureDependencies({String? env}) =>
    diContainer.init(environment: env);
