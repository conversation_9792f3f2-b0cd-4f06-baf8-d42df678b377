import 'package:api_client/api_client.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/fusion.dart';
import 'package:host/host.dart';
import 'package:trader/di/di_initializer.dart';
import 'package:flutter/material.dart';

class _WalletDetails extends StatelessWidget {
  const _WalletDetails({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TradingAccountModel account = TradingAccountModel(
      recordId: '1e3b0952-4e85-8bf8-7072-64ec5e74624d',
      accountNumber: '*********-001',
      accountIdLong: 0,
      dateCreated: '2023-08-28T08:43:59',
      platformAccountType: PlatformAccountType.standard,
      homeCurrency: 'USD',
      leverage: 0,
      currentBalance: 1130.01,
      credit: 0.0,
      equity: 1130.01,
      margin: 0.0,
      accountType: AccountType.landingWallet,
      brokerId: '927fb61b-bc1b-c1d6-5067-58c7ff9a1558',
      name: 'Mr <PERSON><PERSON>',
      platformType: PlatformType.equiti,
      clientId: '27f1c08a-2847-d557-56cd-64ec5ed54709',
      isDemo: false,
      profit: 0.0,
      accountStatus: 'Active',
      leadSource: '',
      grossProfit: 0.0,
      primaryEmail: '<EMAIL>',
    );
    return Container(
      child: Center(
        child: TextButton(
          onPressed: () {
            walletDetailsBottomSheet(context, account);
          },
          child: Text("Click Here"),
        ),
      ),
    );
  }
}

DisplayableComponent walletDetails() {
  return DisplayableComponent(
    title: 'Wallet Details',
    children: [
      DisplayableComponent(
        title: 'Success',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/wallet_details/success.json',
                ),
              ],
            });

          return Scaffold(
            appBar: AppBar(),
            body: Container(
              padding: EdgeInsets.all(16),
              child: _WalletDetails(),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'empty',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/wallet_details/empty.json',
                ),
              ],
            });

          return Scaffold(
            appBar: AppBar(),
            body: Container(
              padding: EdgeInsets.all(16),
              child: _WalletDetails(),
            ),
          );
        },
      ),
      DisplayableComponent(
        title: 'failure',
        onTap: () {
          diContainer<MockApiInterceptor>()
            ..reset()
            ..reply({
              '/activityapi/account/v2': [
                MockResponse(
                  bodyFilePath: 'resources/mocks/wallet_details/failure.json',
                ),
              ],
            });

          return Scaffold(
            appBar: AppBar(),
            body: Container(
              padding: EdgeInsets.all(16),
              child: _WalletDetails(),
            ),
          );
        },
      ),
    ],
  );
}
