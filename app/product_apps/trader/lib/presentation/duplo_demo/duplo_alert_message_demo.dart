import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';
import 'package:host/host.dart';

DisplayableComponent duploAlertMessageDemo() {
  return DisplayableComponent(
    title: 'Deplo Alert Message',
    onTap: () {
      return Builder(
        builder: (context) {
          return Scaffold(
            appBar: AppBar(
              automaticallyImplyLeading: true,
              title: Text("Duplo Alert Variants"),
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    DuploAlertMessage.info(
                      title:
                          'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
                    ),
                    Sized<PERSON>ox(height: 16),
                    DuploAlertMessage.warning(
                      title:
                          'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
                    ),
                    <PERSON>zed<PERSON><PERSON>(height: 16),
                    DuploAlertMessage.error(
                      title:
                          'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
                    ),

                    <PERSON>zed<PERSON><PERSON>(height: 16),
                    DuploAlertMessage.brand(
                      title:
                          'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.success(
                      title:
                          'This is demo text. Fontweight can be changed. Lorem ipsum dolor sit amet consectetur adipisicing elit.',
                    ),
                    SizedBox(height: 32),
                    DuploAlertMessage.info(
                      title: 'This is demo text.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.warning(
                      title: 'This is demo text. ',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.error(
                      title: 'This is demo text. ',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.brand(
                      title: 'This is demo text. ',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.success(
                      title: 'This is demo text. ',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 32),
                    DuploAlertMessage.info(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.warning(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.error(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.brand(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.success(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 32),
                    DuploAlertMessage.info(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.warning(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.error(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.brand(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.success(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 32),
                    DuploAlertMessage.info(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      hasSecondaryButton: true,

                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.warning(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      hasSecondaryButton: true,

                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.error(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      hasSecondaryButton: true,

                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.brand(
                      title: 'This is demo text. ',
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      hasSecondaryButton: true,

                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                    SizedBox(height: 16),
                    DuploAlertMessage.success(
                      title: 'This is demo text. ',
                      hasSecondaryButton: true,
                      subtitle: 'This is demo subtitle. It is nullable.',
                      primaryAction: 'Dismiss',
                      secondaryAction: 'Do Action',
                      children: [
                        Text(
                          'Lorem ipsum dolor sit amet consectetur adipisicing elit. Aliquid pariatur, ipsum dolor.',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    },
  );
}
