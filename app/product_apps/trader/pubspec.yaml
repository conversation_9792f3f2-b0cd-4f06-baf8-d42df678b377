name: trader
description: Host App for Equiti Trader
version: 1.0.0+1
publish_to: "none"

environment:
  sdk: 3.8.1

dependencies:
  flutter_bloc: 9.1.1
  # Utilities
  monitoring:
    path: ../../../utilities/monitoring
  api_client:
    path: ../../../utilities/api_client
  equiti_router:
    path: ../../../utilities/equiti_router
  socket_client:
    path: ../../../utilities/socket_client
  prelude:
    path: ../../../utilities/prelude
  user_verification:
    path: ../../../utilities/user_verification
  equiti_localization:
    path: ../../../core/equiti_localization
  user_account: 
    path: ../../../core/user_account
  domain:
    path: ../../../core/domain
  campaigns:
    path: ../../../core/campaigns
  preferences:
    path: ../../../utilities/preferences
  equiti_auth:
    path: ../../../core/equiti_auth
  broker_settings:
    path: ../../../core/broker_settings
  locale_manager:
    path: ../../../utilities/locale_manager

  # Features
  e_trader:
    path: ../../../features/e_trader
  payment:
    path: ../../../features/payments

  # Dependency Injection
  get_it: 8.0.3
  injectable: 2.5.0
  clock: 1.1.2

  # Host App utilities
  host:
    path: ../common/packages/host

  # Design system
  duplo:
    path: ../../../core/duplo
  login:
    path: ../../../core/login
  onboarding:
    path: ../../../core/onboarding
  # Flutter
  flutter:
    sdk: flutter
  flutter_svg: 2.0.10+1
  intl: 0.20.2

dev_dependencies:
  build_runner: 2.5.4
  dependency_validator: 5.0.2
  bdd_widget_test: 1.8.1
  equiti_lint:
    path: ../../../utilities/equiti_lint
  injectable_generator: 2.7.0
  flutter_native_splash: 2.4.3
  dart_code_metrics_presets: 2.22.0
  build_verify: 3.1.1
  test: 1.25.15
  
dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ">=3.0.1 <3.1.0"

flutter:
  uses-material-design: true
  assets:
    - resources/mocks/product_detail_info/
    - resources/mocks/holidays/
    - resources/mocks/sessions/
    - resources/mocks/reset_balance/
    - resources/mocks/legal_documents/
    - resources/mocks/withdraw/
    - resources/mocks/change_leverage/get/
    - resources/mocks/change_leverage/post/
    - resources/mocks/categories/
    - resources/mocks/symbols/
    - resources/mocks/get_chart/
    - resources/mocks/watchlisted_indicator/
    - assets/images/
    - resources/mocks/order/
    - resources/mocks/close_trade/
    - resources/mocks/get_countries/
    - resources/mocks/verify_mobile_number/
    - resources/mocks/send_otp/
    - resources/mocks/verify_otp/
    - assets/images/empty_watch_list.svg
    - resources/mocks/switch_account/get_live_accounts/
    - resources/mocks/switch_account/get_demo_accounts/
    - resources/mocks/signup/email/
    - resources/mocks/signup/signup/
    - resources/mocks/morph_ui/
    - resources/mocks/morph_ui/progress_tracker/
    - resources/mocks/morph_ui/next_form/
    - resources/mocks/morph_ui/submit_form/
    - resources/mocks/insights/
    - resources/mocks/insights/get_client/
    - resources/mocks/progress_tracker/
    - resources/mocks/statements/
    - resources/mocks/events/
    - resources/mocks/funding_tab/
    - resources/mocks/trading_tab/
    - resources/mocks/payment_options/
    - resources/mocks/wallet_details/
    - resources/mocks/conversion_rate/
    - resources/mocks/payment_list_of_accounts/
    - resources/mocks/status/
    - resources/mocks/new_transfer_funds/
    - resources/mocks/withdraw_skrill_and_neteller/
    - resources/mocks/client_profile/
    - resources/mocks/withdraw_fees/
    - resources/mocks/withdraw_bank_transfer/
    - resources/mocks/withdraw_cards/
    - resources/mocks/withdraw_options/
    - resources/mocks/get_transfer_type/
    - resources/mocks/change_account_password/
    - resources/mocks/broker_settings/
    - resources/mocks/user_registration/

flutter_gen:
  output: lib/assets/
  assets:
    exclude:
      - resources/mocks/**/*
    outputs:
      package_parameter_enabled: false
  integrations:
    flutter_svg: true
