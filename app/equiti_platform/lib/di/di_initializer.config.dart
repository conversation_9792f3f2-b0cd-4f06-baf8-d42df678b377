// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:api_client/api_client.dart' as _i633;
import 'package:broker_settings/broker_settings.dart' as _i389;
import 'package:campaigns/campaigns.dart' as _i494;
import 'package:clock/clock.dart' as _i454;
import 'package:customer_support_chat/customer_support_chat.dart' as _i857;
import 'package:domain/domain.dart' as _i494;
import 'package:duplo/duplo.dart' as _i323;
import 'package:e_trader/fusion.dart' as _i665;
import 'package:equiti_analytics/equiti_analytics.dart' as _i917;
import 'package:equiti_analytics/src/di/di_initializer.module.dart' as _i319;
import 'package:equiti_auth/equiti_auth.dart' as _i313;
import 'package:equiti_identity/equiti_identity.dart' as _i319;
import 'package:equiti_platform/config/app_config.dart' as _i40;
import 'package:equiti_platform/deep_links/navigation/deep_link_navigation_registry.dart'
    as _i1060;
import 'package:equiti_platform/deep_links/navigation/delegates/hub_navigation_delegate.dart'
    as _i951;
import 'package:equiti_platform/di/analytics_module.dart' as _i629;
import 'package:equiti_platform/di/api_module.dart' as _i26;
import 'package:equiti_platform/di/app_module.dart' as _i153;
import 'package:equiti_platform/di/deep_link_module.dart' as _i17;
import 'package:equiti_platform/di/feature_flags_module.dart' as _i511;
import 'package:equiti_platform/di/navigation_module.dart' as _i959;
import 'package:equiti_platform/network_connectivity/network_connectivity_manager.dart'
    as _i731;
import 'package:equiti_router/equiti_router.dart' as _i955;
import 'package:equiti_secure_storage/equiti_secure_storage.dart' as _i704;
import 'package:feature_flags/feature_flags.dart' as _i13;
import 'package:flutter/material.dart' as _i409;
import 'package:get_it/get_it.dart' as _i174;
import 'package:hub/hub.dart' as _i511;
import 'package:injectable/injectable.dart' as _i526;
import 'package:locale_manager/locale_manager.dart' as _i385;
import 'package:login/login.dart' as _i944;
import 'package:monitoring/monitoring.dart' as _i472;
import 'package:network_logging/network_logging.dart' as _i0;
import 'package:onboarding/onboarding.dart' as _i706;
import 'package:payment/payments.dart' as _i702;
import 'package:preferences/preferences.dart' as _i695;
import 'package:prelude/prelude.dart' as _i813;
import 'package:socket_client/socket_client.dart' as _i688;
import 'package:theme_manager/theme_manager.dart' as _i811;
import 'package:user_account/user_account.dart' as _i43;

extension GetItInjectableX on _i174.GetIt {
  // initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(this, environment, environmentFilter);
    await _i472.MonitoringPackageModule().init(gh);
    final navigationModule = _$NavigationModule();
    final deepLinkModule = _$DeepLinkModule();
    final apiModule = _$ApiModule();
    final appModule = _$AppModule();
    final analyticsModule = _$AnalyticsModule();
    final featureFlagsModule = _$FeatureFlagsModule();
    gh.singleton<_i917.NewRelicNavigationObserver>(
      () => navigationModule.newRelicNavObserver,
    );
    gh.singleton<_i511.HubLocation>(() => navigationModule.hubLocation());
    gh.singleton<_i944.LoginRouteLocation>(
      () => navigationModule.loginRouteLocation(),
    );
    gh.singleton<_i706.OnboardingRouteLocation>(
      () => navigationModule.onboardingRouteLocation(),
    );
    gh.singleton<_i702.PaymentRouteLocation>(
      () => navigationModule.paymentRouteLocation(),
    );
    gh.singleton<_i665.EquitiTraderLocation>(
      () => navigationModule.equitiTraderLocation(),
    );
    gh.singleton<_i813.PerformanceRouteObserver>(
      () => navigationModule.performanceRouteObserver(),
    );
    gh.lazySingleton<_i951.HubNavigationDelegate>(
      () => deepLinkModule.hubNavigationDelegate(),
    );
    gh.lazySingleton<_i313.AuthService>(() => apiModule.authService);
    gh.lazySingleton<_i633.CurlInterceptor>(() => apiModule.curlInterceptor());
    await gh.lazySingletonAsync<_i633.AppInfoInterceptor>(
      () => apiModule.appInfoInterceptor(),
      preResolve: true,
    );
    gh.lazySingleton<_i665.GetOfficeCodeUseCase>(
      () => appModule.getOfficeCodeUseCase,
    );
    gh.lazySingleton<_i454.Clock>(() => appModule.clock);
    await gh.lazySingletonAsync<_i695.EquitiPreferences>(
      () => appModule.sharedPreferences(),
      preResolve: true,
    );
    gh.lazySingleton<_i704.SecureStorage>(
      () => appModule.equitiSecureStorage(),
    );
    gh.lazySingleton<_i944.LoginNavigation>(
      () => navigationModule.loginNavigation(),
    );
    gh.singleton<_i917.AppsFlyerConfig>(
      () => analyticsModule.appsFlyerConfig(gh<_i40.AppConfig>()),
    );
    gh.singleton<_i917.AnalyticsConfig>(
      () => analyticsModule.analyticsConfig(gh<_i40.AppConfig>()),
    );
    gh.lazySingleton<_i313.UAEPassEnvConfigs>(
      () => appModule.uaePassEnvConfigs(gh<_i40.AppConfig>()),
    );
    gh.lazySingleton<_i313.AuthConfig>(
      () => appModule.authConfig(gh<_i40.AppConfig>()),
    );
    gh.lazySingleton<_i857.CustomerChatSupportConfig>(
      () => appModule.ccSupprtConfig(gh<_i40.AppConfig>()),
    );
    await gh.lazySingletonAsync<_i13.FeatureFlagsContext>(
      () => featureFlagsModule.featureFlagsContext(gh<_i472.LoggerBase>()),
      preResolve: true,
    );
    gh.singleton<bool>(
      () => appModule.isLeanInSandbox(gh<_i40.AppConfig>()),
      instanceName: 'isLeanInSandbox',
    );
    gh.lazySingleton<_i511.HubNavigation>(
      () => navigationModule.hubNavigation(),
    );
    gh.singleton<String>(
      () => appModule.environment(gh<_i40.AppConfig>()),
      instanceName: 'environment',
    );
    gh.lazySingleton<_i955.EquitiNavigatorBase>(
      () => navigationModule.equitiPlatformNavigator(
        gh<_i511.HubLocation>(),
        gh<_i944.LoginRouteLocation>(),
        gh<_i665.EquitiTraderLocation>(),
        gh<_i706.OnboardingRouteLocation>(),
        gh<_i702.PaymentRouteLocation>(),
        gh<_i813.PerformanceRouteObserver>(),
        gh<_i917.NewRelicNavigationObserver>(),
      ),
    );
    gh.lazySingleton<_i857.CustomerSupportChat>(
      () =>
          appModule.customerSupportChat(gh<_i857.CustomerChatSupportConfig>()),
    );
    gh.singleton<String>(
      () => apiModule.socketBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'SocketBaseUrl',
    );
    gh.singleton<String>(
      () => apiModule.tradeBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'TradeBaseUrl',
    );
    gh.singleton<String>(
      () => apiModule.demoSocketBaseUrl(gh<_i40.AppConfig>()),
      instanceName: 'SocketBaseDemoUrl',
    );
    gh.lazySingleton<_i702.PaymentNavigation>(
      () => navigationModule.paymentNavigationImpl(),
    );
    gh.singleton<String>(
      () => apiModule.tradeBaseDemoUrl(gh<_i40.AppConfig>()),
      instanceName: 'TradeBaseDemoUrl',
    );
    gh.lazySingleton<_i1060.DeepLinkNavigationRegistry>(
      () => deepLinkModule.deepLinkNavigationRegistry(
        gh<_i951.HubNavigationDelegate>(),
      ),
    );
    gh.lazySingleton<_i665.EquitiTraderNavigation>(
      () => navigationModule.withdrawNavigation(),
    );
    gh.lazySingleton<_i706.OnboardingNavigation>(
      () => navigationModule.onboardingNavigationImpl(),
    );
    gh.lazySingleton<_i409.GlobalKey<_i409.NavigatorState>>(
      () => navigationModule.navigatorKey(gh<_i955.EquitiNavigatorBase>()),
    );
    gh.lazySingleton<_i13.FlagRegistry>(
      () => featureFlagsModule.flagRegistry(gh<_i13.FeatureFlagsContext>()),
    );
    gh.lazySingleton<_i13.FeatureFlagService>(
      () =>
          featureFlagsModule.featureFlagService(gh<_i13.FeatureFlagsContext>()),
    );
    gh.lazySingleton<_i731.NetworkConnectivityManager>(
      () => apiModule.tradeNetworkConnectivityManager(
        gh<_i409.GlobalKey<_i409.NavigatorState>>(),
      ),
    );
    gh.lazySingleton<_i731.NetworkConnectivityManager>(
      () => apiModule.networkConnectivityManager(
        gh<_i409.GlobalKey<_i409.NavigatorState>>(),
      ),
      instanceName: 'mobileBffNetworkConnectivityManager',
    );
    gh.singleton<_i385.LocaleManager>(
      () => appModule.localeModel(gh<_i695.EquitiPreferences>()),
    );
    gh.singleton<_i811.ThemeManager>(
      () => appModule.themeManager(gh<_i695.EquitiPreferences>()),
    );
    gh.lazySingleton<_i313.TokenManager>(
      () => apiModule.tokenManager(gh<_i695.EquitiPreferences>()),
    );
    gh.lazySingleton<_i319.IdManager>(
      () => appModule.idManager(gh<_i695.EquitiPreferences>()),
    );
    gh.lazySingleton<_i0.NetworkLogManager>(
      () => apiModule.networkLogManager(
        gh<_i472.LoggerBase>(),
        gh<_i40.AppConfig>(),
      ),
    );
    gh.lazySingleton<_i511.HubFlags>(
      () => featureFlagsModule.hubFlags(gh<_i13.FeatureFlagService>()),
    );
    gh.lazySingleton<_i13.CommonFlags>(
      () => featureFlagsModule.commonFlags(gh<_i13.FeatureFlagService>()),
    );
    gh.lazySingleton<_i702.PaymentFlags>(
      () => featureFlagsModule.paymentFlags(gh<_i13.FeatureFlagService>()),
    );
    gh.lazySingleton<_i706.OnboardingFlags>(
      () => featureFlagsModule.onboardingFlags(gh<_i13.FeatureFlagService>()),
    );
    gh.lazySingleton<_i665.TraderFlags>(
      () => featureFlagsModule.traderFlags(gh<_i13.FeatureFlagService>()),
    );
    gh.lazySingleton<_i688.SocketClientBuilder>(
      () => apiModule.socketClientBuilder(
        gh<_i472.LoggerBase>(),
        gh<String>(instanceName: 'SocketBaseUrl'),
        gh<_i313.TokenManager>(),
        gh<_i0.NetworkLogManager>(),
        gh<_i917.AnalyticsService>(),
        gh<_i385.LocaleManager>(),
        gh<_i633.AppInfoInterceptor>(),
      ),
    );
    gh.lazySingleton<_i313.AuthInterceptor>(
      () => apiModule.authInterceptor(gh<_i313.TokenManager>()),
    );
    gh.lazySingleton<_i313.AuthInterceptor>(
      () => apiModule.mobileBffAuthInterceptor(gh<_i313.TokenManager>()),
      instanceName: 'mobileBffAuthInterceptor',
    );
    gh.lazySingleton<_i633.DioBuilder>(
      () => apiModule.mobileBffDioBuilder(
        gh<_i472.PrettyDioLogger>(),
        gh<_i40.AppConfig>(),
        gh<_i313.AuthInterceptor>(instanceName: 'mobileBffAuthInterceptor'),
        gh<_i633.CurlInterceptor>(),
        gh<_i633.AppInfoInterceptor>(),
        gh<_i0.NetworkLogManager>(),
        gh<_i731.NetworkConnectivityManager>(
          instanceName: 'mobileBffNetworkConnectivityManager',
        ),
      ),
      instanceName: 'mobileBffDioBuilder',
    );
    gh.lazySingleton<_i633.DioBuilder>(
      () => apiModule.dioBuilder(
        gh<_i472.PrettyDioLogger>(),
        gh<String>(instanceName: 'TradeBaseUrl'),
        gh<_i313.AuthInterceptor>(),
        gh<_i633.CurlInterceptor>(),
        gh<_i633.AppInfoInterceptor>(),
        gh<_i0.NetworkLogManager>(),
        gh<_i731.NetworkConnectivityManager>(),
      ),
    );
    gh.lazySingleton<_i633.ApiClientBase>(
      () => apiModule.mobileBffApiClient(
        gh<_i633.DioBuilder>(instanceName: 'mobileBffDioBuilder'),
      ),
      instanceName: 'mobileBffApiClient',
    );
    gh.lazySingleton<_i688.SocketClient>(
      () => apiModule.socketClient(gh<_i688.SocketClientBuilder>()),
    );
    gh.lazySingleton<_i633.ApiClientBase>(
      () => apiModule.apiClient(gh<_i633.DioBuilder>()),
    );
    gh.lazySingleton<_i313.UAEPassAuthRepository>(
      () => apiModule.uaePassAuthRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
        gh<_i40.AppConfig>(),
      ),
    );
    await _i319.EquitiAnalyticsPackageModule().init(gh);
    await _i43.UserAccountPackageModule().init(gh);
    await _i389.BrokerSettingsPackageModule().init(gh);
    await _i944.LoginPackageModule().init(gh);
    await _i323.DuploPackageModule().init(gh);
    await _i706.OnboardingPackageModule().init(gh);
    await _i702.PaymentPackageModule().init(gh);
    await _i511.HubPackageModule().init(gh);
    await _i665.ETraderPackageModule().init(gh);
    await _i494.DomainPackageModule().init(gh);
    await _i494.CampaignsPackageModule().init(gh);
    return this;
  }
}

class _$NavigationModule extends _i959.NavigationModule {}

class _$DeepLinkModule extends _i17.DeepLinkModule {}

class _$ApiModule extends _i26.ApiModule {}

class _$AppModule extends _i153.AppModule {}

class _$AnalyticsModule extends _i629.AnalyticsModule {}

class _$FeatureFlagsModule extends _i511.FeatureFlagsModule {}
