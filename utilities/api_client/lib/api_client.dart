/// Library that abstract all the API logic to make network connections
library api_client;

export 'src/http/api/dio/cache_interceptor.dart';
export 'src/http/api/dio/dio_api_client.dart';
export 'src/http/api/dio/dio_builder.dart';
export 'src/http/api/model/http_api_exception.dart';
export 'src/http/api/model/http_api_response.dart';
export 'src/http/api_client_base.dart';
export 'src/http/mock_api/mock_api_interceptor.dart';
export 'src/http/mock_api/mock_response.dart';
export 'src/http/utils/curl_interceptor.dart';
export 'src/http/api/model/mobile_bff_base_error.dart';
export 'src/http/utils/app_info_interceptor.dart';
