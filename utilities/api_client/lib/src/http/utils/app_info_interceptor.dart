import 'package:dio/dio.dart';

/// Interceptor that adds app version, build number, and device type headers to all requests.
///
/// This interceptor is designed to be instantiated at the app layer with the actual
/// version information retrieved from PackageInfo or similar sources.
class AppInfoInterceptor extends Interceptor {
  final String appVersion;
  final String deviceType;
  final String platform;

  const AppInfoInterceptor({
    required this.appVersion,
    required this.platform,
    this.deviceType = 'mobile',
  });

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.headers['X-App-Version'] = appVersion;
    options.headers['X-Device-Type'] = deviceType;
    options.headers['X-Platform'] = platform;
    super.onRequest(options, handler);
  }
}
