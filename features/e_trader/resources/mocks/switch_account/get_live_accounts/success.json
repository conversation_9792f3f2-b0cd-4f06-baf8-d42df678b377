{"success": true, "data": {"accounts": [{"accountId": "1e3b0952-4e85-8bf8-7072-64ec5e74624d", "accountStatus": "Active", "accountType": "LandingWallet", "platformTypeName": "Standard", "accountCurrency": "USD", "platformAccountNumber": "*********-001", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 0, "serverCode": "", "platformType": "<PERSON><PERSON><PERSON>", "leadSource": "", "balance": 1140.01, "currentBalance": 1140.01, "actualBalance": 1140.01, "margin": 0, "equity": 1140.01, "profit": 0, "grossProfit": 0, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": 0, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 5000, "marginAlternateCurrency": 0, "equityAlternateCurrency": 5000, "profitAlternateCurrency": 0, "grossProfitAlternateCurrency": 0, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "44d23095-b9c2-8423-7894-6511891ea6a1", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "USD", "platformAccountNumber": "********", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Premiere", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 50, "serverCode": "equitiTrader-live-01", "platformType": "EquitiTrader", "leadSource": "", "balance": 241341.27, "currentBalance": 241339.41, "actualBalance": 1140.01, "margin": 27.82, "equity": 241339.41, "profit": -4.86, "grossProfit": -5.23, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": ********, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 241341.27, "marginAlternateCurrency": 27.82, "equityAlternateCurrency": 241339.41, "profitAlternateCurrency": -4.86, "grossProfitAlternateCurrency": -5.23, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "cf24f7fc-ec7e-2d97-aa5d-65e721616418", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "USD", "platformAccountNumber": "********", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 500, "serverCode": "equitiTrader-live-01", "platformType": "EquitiTrader", "leadSource": "", "balance": 54590, "currentBalance": 54590, "actualBalance": 1140.01, "margin": 0, "equity": 54590, "profit": 0, "grossProfit": 0, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": 1008576, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 54590, "marginAlternateCurrency": 0, "equityAlternateCurrency": 54590, "profitAlternateCurrency": 0, "grossProfitAlternateCurrency": 0, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "5e4018b5-102e-bbfd-6915-6661751aadc5", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "USD", "platformAccountNumber": "********", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 100, "serverCode": "equitiTrader-live-01", "platformType": "EquitiTrader", "leadSource": "", "balance": 3126.24, "currentBalance": 3126.24, "actualBalance": 1140.01, "margin": 6.94, "equity": 5040.87, "profit": 1912.63, "grossProfit": 2083.63, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": ********, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 3126.24, "marginAlternateCurrency": 6.94, "equityAlternateCurrency": 5040.87, "profitAlternateCurrency": 1912.63, "grossProfitAlternateCurrency": 2083.63, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "b146700b-d50d-c825-b91c-668e71f98109", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "USD", "platformAccountNumber": "********", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 30, "serverCode": "equitiTrader-live-01", "platformType": "EquitiTrader", "leadSource": "", "balance": 0, "currentBalance": 0, "actualBalance": 1140.01, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": ********, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 0, "marginAlternateCurrency": 0, "equityAlternateCurrency": 0, "profitAlternateCurrency": 0, "grossProfitAlternateCurrency": 0, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "63714d92-8bac-860b-0291-66d5be3b2d50", "accountStatus": "Active", "accountType": "LandingWallet", "platformTypeName": "Standard", "accountCurrency": "USD", "platformAccountNumber": "*********-002", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 100, "serverCode": "", "platformType": "<PERSON><PERSON><PERSON>", "leadSource": "", "balance": 0, "currentBalance": 0, "actualBalance": 1140.01, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": 0, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 0, "marginAlternateCurrency": 0, "equityAlternateCurrency": 0, "profitAlternateCurrency": 0, "grossProfitAlternateCurrency": 0, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "1a15e449-169c-effd-56af-66f520d108bb", "accountStatus": "Active", "accountType": "LandingWallet", "platformTypeName": "Standard", "accountCurrency": "SAR", "platformAccountNumber": "*********-003", "clientId": "27f1c08a-2847-d557-56cd-64ec5ed54709", "accountGroup": "", "brokerId": "927fb61b-bc1b-c1d6-5067-58c7ff9a1558", "platformAccountType": "Standard", "name": "Mr <PERSON><PERSON>", "primaryEmail": "<EMAIL>", "leverage": 100, "serverCode": "", "platformType": "<PERSON><PERSON><PERSON>", "leadSource": "", "balance": 0, "currentBalance": 0, "actualBalance": 1140.01, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2023-08-28T08:43:59", "isDemo": false, "classification": "DirectClient", "accountIdLong": 0, "credit": 0, "accountCurrencyUsdPair": "", "balanceAlternateCurrency": 0, "marginAlternateCurrency": 0, "equityAlternateCurrency": 0, "profitAlternateCurrency": 0, "grossProfitAlternateCurrency": 0, "creditAlternateCurrency": 0, "isSwapFree": false, "freeMargin": 0, "nickName": "<PERSON><PERSON><PERSON>"}]}, "error": {"code": "", "description": ""}}