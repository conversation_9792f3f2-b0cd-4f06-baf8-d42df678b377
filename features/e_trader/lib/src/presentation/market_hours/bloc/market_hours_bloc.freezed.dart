// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'market_hours_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MarketHoursEvent {

 String get symbolCode;
/// Create a copy of MarketHoursEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketHoursEventCopyWith<MarketHoursEvent> get copyWith => _$MarketHoursEventCopyWithImpl<MarketHoursEvent>(this as MarketHoursEvent, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursEvent&&(identical(other.symbolCode, symbolCode) || other.symbolCode == symbolCode));
}


@override
int get hashCode => Object.hash(runtimeType,symbolCode);

@override
String toString() {
  return 'MarketHoursEvent(symbolCode: $symbolCode)';
}


}

/// @nodoc
abstract mixin class $MarketHoursEventCopyWith<$Res>  {
  factory $MarketHoursEventCopyWith(MarketHoursEvent value, $Res Function(MarketHoursEvent) _then) = _$MarketHoursEventCopyWithImpl;
@useResult
$Res call({
 String symbolCode
});




}
/// @nodoc
class _$MarketHoursEventCopyWithImpl<$Res>
    implements $MarketHoursEventCopyWith<$Res> {
  _$MarketHoursEventCopyWithImpl(this._self, this._then);

  final MarketHoursEvent _self;
  final $Res Function(MarketHoursEvent) _then;

/// Create a copy of MarketHoursEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? symbolCode = null,}) {
  return _then(_self.copyWith(
symbolCode: null == symbolCode ? _self.symbolCode : symbolCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc


class _GetMarketHours implements MarketHoursEvent {
  const _GetMarketHours({required this.symbolCode});
  

@override final  String symbolCode;

/// Create a copy of MarketHoursEvent
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetMarketHoursCopyWith<_GetMarketHours> get copyWith => __$GetMarketHoursCopyWithImpl<_GetMarketHours>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetMarketHours&&(identical(other.symbolCode, symbolCode) || other.symbolCode == symbolCode));
}


@override
int get hashCode => Object.hash(runtimeType,symbolCode);

@override
String toString() {
  return 'MarketHoursEvent.getMarketHours(symbolCode: $symbolCode)';
}


}

/// @nodoc
abstract mixin class _$GetMarketHoursCopyWith<$Res> implements $MarketHoursEventCopyWith<$Res> {
  factory _$GetMarketHoursCopyWith(_GetMarketHours value, $Res Function(_GetMarketHours) _then) = __$GetMarketHoursCopyWithImpl;
@override @useResult
$Res call({
 String symbolCode
});




}
/// @nodoc
class __$GetMarketHoursCopyWithImpl<$Res>
    implements _$GetMarketHoursCopyWith<$Res> {
  __$GetMarketHoursCopyWithImpl(this._self, this._then);

  final _GetMarketHours _self;
  final $Res Function(_GetMarketHours) _then;

/// Create a copy of MarketHoursEvent
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? symbolCode = null,}) {
  return _then(_GetMarketHours(
symbolCode: null == symbolCode ? _self.symbolCode : symbolCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$MarketHoursState {

 MarketHoursProcessState get proccesState;
/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketHoursStateCopyWith<MarketHoursState> get copyWith => _$MarketHoursStateCopyWithImpl<MarketHoursState>(this as MarketHoursState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursState&&(identical(other.proccesState, proccesState) || other.proccesState == proccesState));
}


@override
int get hashCode => Object.hash(runtimeType,proccesState);

@override
String toString() {
  return 'MarketHoursState(proccesState: $proccesState)';
}


}

/// @nodoc
abstract mixin class $MarketHoursStateCopyWith<$Res>  {
  factory $MarketHoursStateCopyWith(MarketHoursState value, $Res Function(MarketHoursState) _then) = _$MarketHoursStateCopyWithImpl;
@useResult
$Res call({
 MarketHoursProcessState proccesState
});


$MarketHoursProcessStateCopyWith<$Res> get proccesState;

}
/// @nodoc
class _$MarketHoursStateCopyWithImpl<$Res>
    implements $MarketHoursStateCopyWith<$Res> {
  _$MarketHoursStateCopyWithImpl(this._self, this._then);

  final MarketHoursState _self;
  final $Res Function(MarketHoursState) _then;

/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? proccesState = null,}) {
  return _then(_self.copyWith(
proccesState: null == proccesState ? _self.proccesState : proccesState // ignore: cast_nullable_to_non_nullable
as MarketHoursProcessState,
  ));
}
/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketHoursProcessStateCopyWith<$Res> get proccesState {
  
  return $MarketHoursProcessStateCopyWith<$Res>(_self.proccesState, (value) {
    return _then(_self.copyWith(proccesState: value));
  });
}
}


/// @nodoc


class _MarketHoursState implements MarketHoursState {
  const _MarketHoursState({this.proccesState = const MarketHoursProcessState.loading()});
  

@override@JsonKey() final  MarketHoursProcessState proccesState;

/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MarketHoursStateCopyWith<_MarketHoursState> get copyWith => __$MarketHoursStateCopyWithImpl<_MarketHoursState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MarketHoursState&&(identical(other.proccesState, proccesState) || other.proccesState == proccesState));
}


@override
int get hashCode => Object.hash(runtimeType,proccesState);

@override
String toString() {
  return 'MarketHoursState(proccesState: $proccesState)';
}


}

/// @nodoc
abstract mixin class _$MarketHoursStateCopyWith<$Res> implements $MarketHoursStateCopyWith<$Res> {
  factory _$MarketHoursStateCopyWith(_MarketHoursState value, $Res Function(_MarketHoursState) _then) = __$MarketHoursStateCopyWithImpl;
@override @useResult
$Res call({
 MarketHoursProcessState proccesState
});


@override $MarketHoursProcessStateCopyWith<$Res> get proccesState;

}
/// @nodoc
class __$MarketHoursStateCopyWithImpl<$Res>
    implements _$MarketHoursStateCopyWith<$Res> {
  __$MarketHoursStateCopyWithImpl(this._self, this._then);

  final _MarketHoursState _self;
  final $Res Function(_MarketHoursState) _then;

/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? proccesState = null,}) {
  return _then(_MarketHoursState(
proccesState: null == proccesState ? _self.proccesState : proccesState // ignore: cast_nullable_to_non_nullable
as MarketHoursProcessState,
  ));
}

/// Create a copy of MarketHoursState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$MarketHoursProcessStateCopyWith<$Res> get proccesState {
  
  return $MarketHoursProcessStateCopyWith<$Res>(_self.proccesState, (value) {
    return _then(_self.copyWith(proccesState: value));
  });
}
}

/// @nodoc
mixin _$MarketHoursProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketHoursProcessState()';
}


}

/// @nodoc
class $MarketHoursProcessStateCopyWith<$Res>  {
$MarketHoursProcessStateCopyWith(MarketHoursProcessState _, $Res Function(MarketHoursProcessState) __);
}


/// @nodoc


class MarketHoursLoadingState implements MarketHoursProcessState {
  const MarketHoursLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketHoursProcessState.loading()';
}


}




/// @nodoc


class MarketHoursSuccessState implements MarketHoursProcessState {
  const MarketHoursSuccessState(this.marketInfo, this.isMarketOpen);
  

 final  MarketTimingCalculator marketInfo;
 final  bool? isMarketOpen;

/// Create a copy of MarketHoursProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MarketHoursSuccessStateCopyWith<MarketHoursSuccessState> get copyWith => _$MarketHoursSuccessStateCopyWithImpl<MarketHoursSuccessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursSuccessState&&(identical(other.marketInfo, marketInfo) || other.marketInfo == marketInfo)&&(identical(other.isMarketOpen, isMarketOpen) || other.isMarketOpen == isMarketOpen));
}


@override
int get hashCode => Object.hash(runtimeType,marketInfo,isMarketOpen);

@override
String toString() {
  return 'MarketHoursProcessState.success(marketInfo: $marketInfo, isMarketOpen: $isMarketOpen)';
}


}

/// @nodoc
abstract mixin class $MarketHoursSuccessStateCopyWith<$Res> implements $MarketHoursProcessStateCopyWith<$Res> {
  factory $MarketHoursSuccessStateCopyWith(MarketHoursSuccessState value, $Res Function(MarketHoursSuccessState) _then) = _$MarketHoursSuccessStateCopyWithImpl;
@useResult
$Res call({
 MarketTimingCalculator marketInfo, bool? isMarketOpen
});




}
/// @nodoc
class _$MarketHoursSuccessStateCopyWithImpl<$Res>
    implements $MarketHoursSuccessStateCopyWith<$Res> {
  _$MarketHoursSuccessStateCopyWithImpl(this._self, this._then);

  final MarketHoursSuccessState _self;
  final $Res Function(MarketHoursSuccessState) _then;

/// Create a copy of MarketHoursProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? marketInfo = null,Object? isMarketOpen = freezed,}) {
  return _then(MarketHoursSuccessState(
null == marketInfo ? _self.marketInfo : marketInfo // ignore: cast_nullable_to_non_nullable
as MarketTimingCalculator,freezed == isMarketOpen ? _self.isMarketOpen : isMarketOpen // ignore: cast_nullable_to_non_nullable
as bool?,
  ));
}


}

/// @nodoc


class MarketHoursErrorState implements MarketHoursProcessState {
  const MarketHoursErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MarketHoursErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'MarketHoursProcessState.error()';
}


}




// dart format on
