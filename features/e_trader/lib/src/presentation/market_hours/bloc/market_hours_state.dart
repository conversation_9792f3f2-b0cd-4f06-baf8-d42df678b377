part of 'market_hours_bloc.dart';

@freezed
sealed class MarketHoursState with _$MarketHoursState {
  const factory MarketHoursState({
    @Default(MarketHoursProcessState.loading())
    MarketHoursProcessState proccesState,
  }) = _MarketHoursState;
}

@freezed
sealed class MarketHoursProcessState with _$MarketHoursProcessState {
  const factory MarketHoursProcessState.loading() = MarketHoursLoadingState;
  const factory MarketHoursProcessState.success(
    MarketTimingCalculator marketInfo,
    bool? isMarketOpen,
  ) = MarketHoursSuccessState;
  const factory MarketHoursProcessState.error() = MarketHoursErrorState;
}
