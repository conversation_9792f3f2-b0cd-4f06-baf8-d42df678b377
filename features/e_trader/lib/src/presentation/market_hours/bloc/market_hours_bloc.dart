import 'dart:async';

import 'package:e_trader/src/data/api/symbol_quote_model.dart';
import 'package:e_trader/src/domain/usecase/get_market_timing_calculator_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/presentation/market_hours/bloc/market_timing_calculator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:monitoring/monitoring.dart';

part 'market_hours_bloc.freezed.dart';
part 'market_hours_event.dart';
part 'market_hours_state.dart';

class MarketHoursBloc extends Bloc<MarketHoursEvent, MarketHoursState> {
  final GetMarketTimingCalculatorUseCase _getMarketLimitCalculatorUseCase;
  final LoggerBase _logger;
  final SubscribeToSymbolQuotesUseCase _subscribeToSymbolQuotesUseCase;

  MarketHoursBloc({
    required LoggerBase logger,
    required GetMarketTimingCalculatorUseCase getMarketLimitCalculatorUseCase,
    required SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
  }) : _logger = logger,
       _getMarketLimitCalculatorUseCase = getMarketLimitCalculatorUseCase,
       _subscribeToSymbolQuotesUseCase = subscribeToSymbolQuotesUseCase,
       super(MarketHoursState()) {
    on<_GetMarketHours>(_getMarketHours);
  }

  FutureOr<void> _getMarketHours(
    _GetMarketHours event,
    Emitter<MarketHoursState> emit,
  ) async {
    final result =
        await _getMarketLimitCalculatorUseCase(
          symbolCode: event.symbolCode,
        ).run();

    await result.fold(
      (error) {
        addError(error);
        emit(state.copyWith(proccesState: MarketHoursProcessState.error()));
      },
      (checker) async {
        final symbolQuoteResult =
            await _subscribeToSymbolQuotesUseCase(
              symbol: event.symbolCode,
              subscriberId: '${MarketHoursBloc}_$hashCode',
            ).run();

        return symbolQuoteResult.fold(
          (left) {
            addError(left);
            emit(state.copyWith(proccesState: MarketHoursProcessState.error()));
          },
          (subscribeResultStream) {
            return emit.forEach<SymbolQuoteModel?, MarketHoursState>(
              subscribeResultStream,
              onData:
                  (symbolQuoteModel) => state.copyWith(
                    proccesState: MarketHoursProcessState.success(
                      checker,
                      symbolQuoteModel?.isMarketOpen,
                    ),
                  ),
            );
          },
        );
      },
    );
  }

  @override
  void addError(Object error, [StackTrace? stackTrace]) {
    _logger.logError(error);
    super.addError(error, stackTrace);
  }
}
