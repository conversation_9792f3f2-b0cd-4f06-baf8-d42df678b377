part of 'accounts_bloc.dart';

@freezed
sealed class AccountsState with _$AccountsState {
  const factory AccountsState({
    required AccountTotalsViewModel accountTotalsViewModel,
    @Default([]) List<TradingAccountViewModel> tradingAccounts,
    @Default([]) List<TradingAccountViewModel> wallets,
    @Default([]) List<TradingAccountModel> accounts,
    @Default(['USD']) List<String> availableCurrencies,
    @Default('USD') String selectedCurrency,
    @Default(AccountsProcessState.loading()) AccountsProcessState processState,
    @Default(CampaignsState.loading()) CampaignsState campaignsState,
  }) = _AccountsState;
}

@freezed
sealed class AccountsProcessState with _$AccountsProcessState {
  const factory AccountsProcessState.loading() = AccountsLoadingProcessState;
  const factory AccountsProcessState.success() = AccountsSuccessProcessState;
  const factory AccountsProcessState.error() = AccountsErrorProcessState;
}

@freezed
sealed class CampaignsState with _$CampaignsState {
  const factory CampaignsState.loading() = CampaignsLoadingState;
  const factory CampaignsState.success(CampaignsModel campaigns) =
      CampaignsSuccessState;
  const factory CampaignsState.empty() = CampaignsEmptyState;
  const factory CampaignsState.error(String errorMessage) = CampaignsErrorState;
}
