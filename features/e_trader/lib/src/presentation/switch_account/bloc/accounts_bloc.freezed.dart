// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'accounts_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$AccountsEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent()';
}


}

/// @nodoc
class $AccountsEventCopyWith<$Res>  {
$AccountsEventCopyWith(AccountsEvent _, $Res Function(AccountsEvent) __);
}


/// @nodoc


class _FetchAccounts implements AccountsEvent {
  const _FetchAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FetchAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.fetch()';
}


}




/// @nodoc


class _RefreshAccounts implements AccountsEvent {
  const _RefreshAccounts();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RefreshAccounts);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.refreshAccounts()';
}


}




/// @nodoc


class _OnAccountSelected implements AccountsEvent {
  const _OnAccountSelected({required this.accountId});
  

 final  String accountId;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAccountSelectedCopyWith<_OnAccountSelected> get copyWith => __$OnAccountSelectedCopyWithImpl<_OnAccountSelected>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAccountSelected&&(identical(other.accountId, accountId) || other.accountId == accountId));
}


@override
int get hashCode => Object.hash(runtimeType,accountId);

@override
String toString() {
  return 'AccountsEvent.onAccountSelected(accountId: $accountId)';
}


}

/// @nodoc
abstract mixin class _$OnAccountSelectedCopyWith<$Res> implements $AccountsEventCopyWith<$Res> {
  factory _$OnAccountSelectedCopyWith(_OnAccountSelected value, $Res Function(_OnAccountSelected) _then) = __$OnAccountSelectedCopyWithImpl;
@useResult
$Res call({
 String accountId
});




}
/// @nodoc
class __$OnAccountSelectedCopyWithImpl<$Res>
    implements _$OnAccountSelectedCopyWith<$Res> {
  __$OnAccountSelectedCopyWithImpl(this._self, this._then);

  final _OnAccountSelected _self;
  final $Res Function(_OnAccountSelected) _then;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountId = null,}) {
  return _then(_OnAccountSelected(
accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _OnCurrencySelected implements AccountsEvent {
  const _OnCurrencySelected({required this.currency});
  

 final  String currency;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnCurrencySelectedCopyWith<_OnCurrencySelected> get copyWith => __$OnCurrencySelectedCopyWithImpl<_OnCurrencySelected>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCurrencySelected&&(identical(other.currency, currency) || other.currency == currency));
}


@override
int get hashCode => Object.hash(runtimeType,currency);

@override
String toString() {
  return 'AccountsEvent.onCurrencySelected(currency: $currency)';
}


}

/// @nodoc
abstract mixin class _$OnCurrencySelectedCopyWith<$Res> implements $AccountsEventCopyWith<$Res> {
  factory _$OnCurrencySelectedCopyWith(_OnCurrencySelected value, $Res Function(_OnCurrencySelected) _then) = __$OnCurrencySelectedCopyWithImpl;
@useResult
$Res call({
 String currency
});




}
/// @nodoc
class __$OnCurrencySelectedCopyWithImpl<$Res>
    implements _$OnCurrencySelectedCopyWith<$Res> {
  __$OnCurrencySelectedCopyWithImpl(this._self, this._then);

  final _OnCurrencySelected _self;
  final $Res Function(_OnCurrencySelected) _then;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? currency = null,}) {
  return _then(_OnCurrencySelected(
currency: null == currency ? _self.currency : currency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _UpdateBalanceSubscription implements AccountsEvent {
  const _UpdateBalanceSubscription({required this.subscribe});
  

 final  bool subscribe;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateBalanceSubscriptionCopyWith<_UpdateBalanceSubscription> get copyWith => __$UpdateBalanceSubscriptionCopyWithImpl<_UpdateBalanceSubscription>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateBalanceSubscription&&(identical(other.subscribe, subscribe) || other.subscribe == subscribe));
}


@override
int get hashCode => Object.hash(runtimeType,subscribe);

@override
String toString() {
  return 'AccountsEvent.updateBalanceSubscription(subscribe: $subscribe)';
}


}

/// @nodoc
abstract mixin class _$UpdateBalanceSubscriptionCopyWith<$Res> implements $AccountsEventCopyWith<$Res> {
  factory _$UpdateBalanceSubscriptionCopyWith(_UpdateBalanceSubscription value, $Res Function(_UpdateBalanceSubscription) _then) = __$UpdateBalanceSubscriptionCopyWithImpl;
@useResult
$Res call({
 bool subscribe
});




}
/// @nodoc
class __$UpdateBalanceSubscriptionCopyWithImpl<$Res>
    implements _$UpdateBalanceSubscriptionCopyWith<$Res> {
  __$UpdateBalanceSubscriptionCopyWithImpl(this._self, this._then);

  final _UpdateBalanceSubscription _self;
  final $Res Function(_UpdateBalanceSubscription) _then;

/// Create a copy of AccountsEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? subscribe = null,}) {
  return _then(_UpdateBalanceSubscription(
subscribe: null == subscribe ? _self.subscribe : subscribe // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _GoToSymbols implements AccountsEvent {
  const _GoToSymbols();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToSymbols);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToSymbols()';
}


}




/// @nodoc


class _GoToDepositPaymentOptions implements AccountsEvent {
  const _GoToDepositPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToDepositPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToDepositPaymentOptions()';
}


}




/// @nodoc


class _GoToWithdrawPaymentOptions implements AccountsEvent {
  const _GoToWithdrawPaymentOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToWithdrawPaymentOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToWithdrawPaymentOptions()';
}


}




/// @nodoc


class _GoToTransferOptions implements AccountsEvent {
  const _GoToTransferOptions();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GoToTransferOptions);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.goToTransferOptions()';
}


}




/// @nodoc


class _GetCampaigns implements AccountsEvent {
  const _GetCampaigns();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetCampaigns);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.getCampaigns()';
}


}




/// @nodoc


class _OnCampaignClose implements AccountsEvent {
  const _OnCampaignClose();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCampaignClose);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.onCampaignClose()';
}


}




/// @nodoc


class _OnCampaignFundAccount implements AccountsEvent {
  const _OnCampaignFundAccount();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnCampaignFundAccount);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsEvent.onCampaignFundAccount()';
}


}




/// @nodoc
mixin _$AccountsState {

 AccountTotalsViewModel get accountTotalsViewModel; List<TradingAccountViewModel> get tradingAccounts; List<TradingAccountViewModel> get wallets; List<TradingAccountModel> get accounts; List<String> get availableCurrencies; String get selectedCurrency; AccountsProcessState get processState; CampaignsState get campaignsState;
/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AccountsStateCopyWith<AccountsState> get copyWith => _$AccountsStateCopyWithImpl<AccountsState>(this as AccountsState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsState&&(identical(other.accountTotalsViewModel, accountTotalsViewModel) || other.accountTotalsViewModel == accountTotalsViewModel)&&const DeepCollectionEquality().equals(other.tradingAccounts, tradingAccounts)&&const DeepCollectionEquality().equals(other.wallets, wallets)&&const DeepCollectionEquality().equals(other.accounts, accounts)&&const DeepCollectionEquality().equals(other.availableCurrencies, availableCurrencies)&&(identical(other.selectedCurrency, selectedCurrency) || other.selectedCurrency == selectedCurrency)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.campaignsState, campaignsState) || other.campaignsState == campaignsState));
}


@override
int get hashCode => Object.hash(runtimeType,accountTotalsViewModel,const DeepCollectionEquality().hash(tradingAccounts),const DeepCollectionEquality().hash(wallets),const DeepCollectionEquality().hash(accounts),const DeepCollectionEquality().hash(availableCurrencies),selectedCurrency,processState,campaignsState);

@override
String toString() {
  return 'AccountsState(accountTotalsViewModel: $accountTotalsViewModel, tradingAccounts: $tradingAccounts, wallets: $wallets, accounts: $accounts, availableCurrencies: $availableCurrencies, selectedCurrency: $selectedCurrency, processState: $processState, campaignsState: $campaignsState)';
}


}

/// @nodoc
abstract mixin class $AccountsStateCopyWith<$Res>  {
  factory $AccountsStateCopyWith(AccountsState value, $Res Function(AccountsState) _then) = _$AccountsStateCopyWithImpl;
@useResult
$Res call({
 AccountTotalsViewModel accountTotalsViewModel, List<TradingAccountViewModel> tradingAccounts, List<TradingAccountViewModel> wallets, List<TradingAccountModel> accounts, List<String> availableCurrencies, String selectedCurrency, AccountsProcessState processState, CampaignsState campaignsState
});


$AccountsProcessStateCopyWith<$Res> get processState;$CampaignsStateCopyWith<$Res> get campaignsState;

}
/// @nodoc
class _$AccountsStateCopyWithImpl<$Res>
    implements $AccountsStateCopyWith<$Res> {
  _$AccountsStateCopyWithImpl(this._self, this._then);

  final AccountsState _self;
  final $Res Function(AccountsState) _then;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? accountTotalsViewModel = null,Object? tradingAccounts = null,Object? wallets = null,Object? accounts = null,Object? availableCurrencies = null,Object? selectedCurrency = null,Object? processState = null,Object? campaignsState = null,}) {
  return _then(_self.copyWith(
accountTotalsViewModel: null == accountTotalsViewModel ? _self.accountTotalsViewModel : accountTotalsViewModel // ignore: cast_nullable_to_non_nullable
as AccountTotalsViewModel,tradingAccounts: null == tradingAccounts ? _self.tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountViewModel>,wallets: null == wallets ? _self.wallets : wallets // ignore: cast_nullable_to_non_nullable
as List<TradingAccountViewModel>,accounts: null == accounts ? _self.accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>,availableCurrencies: null == availableCurrencies ? _self.availableCurrencies : availableCurrencies // ignore: cast_nullable_to_non_nullable
as List<String>,selectedCurrency: null == selectedCurrency ? _self.selectedCurrency : selectedCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountsProcessState,campaignsState: null == campaignsState ? _self.campaignsState : campaignsState // ignore: cast_nullable_to_non_nullable
as CampaignsState,
  ));
}
/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountsProcessStateCopyWith<$Res> get processState {
  
  return $AccountsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsStateCopyWith<$Res> get campaignsState {
  
  return $CampaignsStateCopyWith<$Res>(_self.campaignsState, (value) {
    return _then(_self.copyWith(campaignsState: value));
  });
}
}


/// @nodoc


class _AccountsState implements AccountsState {
  const _AccountsState({required this.accountTotalsViewModel, final  List<TradingAccountViewModel> tradingAccounts = const [], final  List<TradingAccountViewModel> wallets = const [], final  List<TradingAccountModel> accounts = const [], final  List<String> availableCurrencies = const ['USD'], this.selectedCurrency = 'USD', this.processState = const AccountsProcessState.loading(), this.campaignsState = const CampaignsState.loading()}): _tradingAccounts = tradingAccounts,_wallets = wallets,_accounts = accounts,_availableCurrencies = availableCurrencies;
  

@override final  AccountTotalsViewModel accountTotalsViewModel;
 final  List<TradingAccountViewModel> _tradingAccounts;
@override@JsonKey() List<TradingAccountViewModel> get tradingAccounts {
  if (_tradingAccounts is EqualUnmodifiableListView) return _tradingAccounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tradingAccounts);
}

 final  List<TradingAccountViewModel> _wallets;
@override@JsonKey() List<TradingAccountViewModel> get wallets {
  if (_wallets is EqualUnmodifiableListView) return _wallets;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_wallets);
}

 final  List<TradingAccountModel> _accounts;
@override@JsonKey() List<TradingAccountModel> get accounts {
  if (_accounts is EqualUnmodifiableListView) return _accounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_accounts);
}

 final  List<String> _availableCurrencies;
@override@JsonKey() List<String> get availableCurrencies {
  if (_availableCurrencies is EqualUnmodifiableListView) return _availableCurrencies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_availableCurrencies);
}

@override@JsonKey() final  String selectedCurrency;
@override@JsonKey() final  AccountsProcessState processState;
@override@JsonKey() final  CampaignsState campaignsState;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AccountsStateCopyWith<_AccountsState> get copyWith => __$AccountsStateCopyWithImpl<_AccountsState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AccountsState&&(identical(other.accountTotalsViewModel, accountTotalsViewModel) || other.accountTotalsViewModel == accountTotalsViewModel)&&const DeepCollectionEquality().equals(other._tradingAccounts, _tradingAccounts)&&const DeepCollectionEquality().equals(other._wallets, _wallets)&&const DeepCollectionEquality().equals(other._accounts, _accounts)&&const DeepCollectionEquality().equals(other._availableCurrencies, _availableCurrencies)&&(identical(other.selectedCurrency, selectedCurrency) || other.selectedCurrency == selectedCurrency)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.campaignsState, campaignsState) || other.campaignsState == campaignsState));
}


@override
int get hashCode => Object.hash(runtimeType,accountTotalsViewModel,const DeepCollectionEquality().hash(_tradingAccounts),const DeepCollectionEquality().hash(_wallets),const DeepCollectionEquality().hash(_accounts),const DeepCollectionEquality().hash(_availableCurrencies),selectedCurrency,processState,campaignsState);

@override
String toString() {
  return 'AccountsState(accountTotalsViewModel: $accountTotalsViewModel, tradingAccounts: $tradingAccounts, wallets: $wallets, accounts: $accounts, availableCurrencies: $availableCurrencies, selectedCurrency: $selectedCurrency, processState: $processState, campaignsState: $campaignsState)';
}


}

/// @nodoc
abstract mixin class _$AccountsStateCopyWith<$Res> implements $AccountsStateCopyWith<$Res> {
  factory _$AccountsStateCopyWith(_AccountsState value, $Res Function(_AccountsState) _then) = __$AccountsStateCopyWithImpl;
@override @useResult
$Res call({
 AccountTotalsViewModel accountTotalsViewModel, List<TradingAccountViewModel> tradingAccounts, List<TradingAccountViewModel> wallets, List<TradingAccountModel> accounts, List<String> availableCurrencies, String selectedCurrency, AccountsProcessState processState, CampaignsState campaignsState
});


@override $AccountsProcessStateCopyWith<$Res> get processState;@override $CampaignsStateCopyWith<$Res> get campaignsState;

}
/// @nodoc
class __$AccountsStateCopyWithImpl<$Res>
    implements _$AccountsStateCopyWith<$Res> {
  __$AccountsStateCopyWithImpl(this._self, this._then);

  final _AccountsState _self;
  final $Res Function(_AccountsState) _then;

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? accountTotalsViewModel = null,Object? tradingAccounts = null,Object? wallets = null,Object? accounts = null,Object? availableCurrencies = null,Object? selectedCurrency = null,Object? processState = null,Object? campaignsState = null,}) {
  return _then(_AccountsState(
accountTotalsViewModel: null == accountTotalsViewModel ? _self.accountTotalsViewModel : accountTotalsViewModel // ignore: cast_nullable_to_non_nullable
as AccountTotalsViewModel,tradingAccounts: null == tradingAccounts ? _self._tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountViewModel>,wallets: null == wallets ? _self._wallets : wallets // ignore: cast_nullable_to_non_nullable
as List<TradingAccountViewModel>,accounts: null == accounts ? _self._accounts : accounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccountModel>,availableCurrencies: null == availableCurrencies ? _self._availableCurrencies : availableCurrencies // ignore: cast_nullable_to_non_nullable
as List<String>,selectedCurrency: null == selectedCurrency ? _self.selectedCurrency : selectedCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as AccountsProcessState,campaignsState: null == campaignsState ? _self.campaignsState : campaignsState // ignore: cast_nullable_to_non_nullable
as CampaignsState,
  ));
}

/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$AccountsProcessStateCopyWith<$Res> get processState {
  
  return $AccountsProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of AccountsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsStateCopyWith<$Res> get campaignsState {
  
  return $CampaignsStateCopyWith<$Res>(_self.campaignsState, (value) {
    return _then(_self.copyWith(campaignsState: value));
  });
}
}

/// @nodoc
mixin _$AccountsProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState()';
}


}

/// @nodoc
class $AccountsProcessStateCopyWith<$Res>  {
$AccountsProcessStateCopyWith(AccountsProcessState _, $Res Function(AccountsProcessState) __);
}


/// @nodoc


class AccountsLoadingProcessState implements AccountsProcessState {
  const AccountsLoadingProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsLoadingProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.loading()';
}


}




/// @nodoc


class AccountsSuccessProcessState implements AccountsProcessState {
  const AccountsSuccessProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsSuccessProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.success()';
}


}




/// @nodoc


class AccountsErrorProcessState implements AccountsProcessState {
  const AccountsErrorProcessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AccountsErrorProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'AccountsProcessState.error()';
}


}




/// @nodoc
mixin _$CampaignsState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState()';
}


}

/// @nodoc
class $CampaignsStateCopyWith<$Res>  {
$CampaignsStateCopyWith(CampaignsState _, $Res Function(CampaignsState) __);
}


/// @nodoc


class CampaignsLoadingState implements CampaignsState {
  const CampaignsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState.loading()';
}


}




/// @nodoc


class CampaignsSuccessState implements CampaignsState {
  const CampaignsSuccessState(this.campaigns);
  

 final  CampaignsModel campaigns;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsSuccessStateCopyWith<CampaignsSuccessState> get copyWith => _$CampaignsSuccessStateCopyWithImpl<CampaignsSuccessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsSuccessState&&(identical(other.campaigns, campaigns) || other.campaigns == campaigns));
}


@override
int get hashCode => Object.hash(runtimeType,campaigns);

@override
String toString() {
  return 'CampaignsState.success(campaigns: $campaigns)';
}


}

/// @nodoc
abstract mixin class $CampaignsSuccessStateCopyWith<$Res> implements $CampaignsStateCopyWith<$Res> {
  factory $CampaignsSuccessStateCopyWith(CampaignsSuccessState value, $Res Function(CampaignsSuccessState) _then) = _$CampaignsSuccessStateCopyWithImpl;
@useResult
$Res call({
 CampaignsModel campaigns
});


$CampaignsModelCopyWith<$Res> get campaigns;

}
/// @nodoc
class _$CampaignsSuccessStateCopyWithImpl<$Res>
    implements $CampaignsSuccessStateCopyWith<$Res> {
  _$CampaignsSuccessStateCopyWithImpl(this._self, this._then);

  final CampaignsSuccessState _self;
  final $Res Function(CampaignsSuccessState) _then;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? campaigns = null,}) {
  return _then(CampaignsSuccessState(
null == campaigns ? _self.campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as CampaignsModel,
  ));
}

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsModelCopyWith<$Res> get campaigns {
  
  return $CampaignsModelCopyWith<$Res>(_self.campaigns, (value) {
    return _then(_self.copyWith(campaigns: value));
  });
}
}

/// @nodoc


class CampaignsEmptyState implements CampaignsState {
  const CampaignsEmptyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsEmptyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState.empty()';
}


}




/// @nodoc


class CampaignsErrorState implements CampaignsState {
  const CampaignsErrorState(this.errorMessage);
  

 final  String errorMessage;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsErrorStateCopyWith<CampaignsErrorState> get copyWith => _$CampaignsErrorStateCopyWithImpl<CampaignsErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsErrorState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'CampaignsState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $CampaignsErrorStateCopyWith<$Res> implements $CampaignsStateCopyWith<$Res> {
  factory $CampaignsErrorStateCopyWith(CampaignsErrorState value, $Res Function(CampaignsErrorState) _then) = _$CampaignsErrorStateCopyWithImpl;
@useResult
$Res call({
 String errorMessage
});




}
/// @nodoc
class _$CampaignsErrorStateCopyWithImpl<$Res>
    implements $CampaignsErrorStateCopyWith<$Res> {
  _$CampaignsErrorStateCopyWithImpl(this._self, this._then);

  final CampaignsErrorState _self;
  final $Res Function(CampaignsErrorState) _then;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = null,}) {
  return _then(CampaignsErrorState(
null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
