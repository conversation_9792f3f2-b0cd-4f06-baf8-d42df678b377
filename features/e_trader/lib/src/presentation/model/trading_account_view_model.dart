import 'package:domain/domain.dart';
import 'package:flutter/foundation.dart';

/// A [ChangeNotifier] that holds real-time account balance data.
///
/// This class efficiently notifies listeners only when values change,
/// allowing widgets to rebuild independently without going through bloc
/// state emissions for every balance update.
class TradingAccountViewModel extends ChangeNotifier {
  TradingAccountViewModel({
    String? name,
    String? nickName,
    String? accountNumber,
    PlatformType platformType = PlatformType.unknown,
    PlatformAccountType platformAccountType = PlatformAccountType.unknown,
    double? equity,
    double? profit,
    double? grossProfit,
    bool isSwapFree = false,
    required String homeCurrency,
    required String accountId,
    double? balance,
    double? credit,
    double? margin,
    double? marginLevel,
    bool isSelected = false,
    String platformTypeName = '',
  }) : _name = name,
       _nickName = nickName,
       _accountNumber = accountNumber,
       _platformType = platformType,
       _platformAccountType = platformAccountType,
       _equity = equity,
       _profit = profit,
       _grossProfit = grossProfit,
       _isSwapFree = isSwapFree,
       _homeCurrency = homeCurrency,
       _accountId = accountId,
       _balance = balance,
       _credit = credit,
       _margin = margin,
       _marginLevel = marginLevel,
       _isSelected = isSelected,
       _platformTypeName = platformTypeName;

  // Private fields
  String? _name;
  String? _nickName;
  String? _accountNumber;
  PlatformType _platformType;
  PlatformAccountType _platformAccountType;
  double? _equity;
  double? _profit;
  double? _grossProfit;
  bool _isSwapFree;
  String _homeCurrency;
  String _accountId;
  double? _balance;
  double? _credit;
  double? _margin;
  double? _marginLevel;
  bool _isSelected;
  String _platformTypeName;

  // Getters
  String? get name => _name;
  String? get nickName => _nickName;
  String? get accountNumber => _accountNumber;
  PlatformType get platformType => _platformType;
  PlatformAccountType get platformAccountType => _platformAccountType;
  double? get equity => _equity;
  double? get profit => _profit;
  double? get grossProfit => _grossProfit;
  bool get isSwapFree => _isSwapFree;
  String get homeCurrency => _homeCurrency;
  String get accountId => _accountId;
  double? get balance => _balance;
  double? get credit => _credit;
  double? get margin => _margin;
  double? get marginLevel => _marginLevel;
  bool get isSelected => _isSelected;
  set isSelected(bool value) {
    if (_isSelected != value) {
      _isSelected = value;
      notifyListeners();
    }
  }

  String get platformTypeName => _platformTypeName;

  /// Updates balance-related properties in a single notification batch.
  ///
  /// This is more efficient than setting properties individually when
  /// multiple values change simultaneously from socket updates.
  void updateBalance({
    double? newEquity,
    double? newBalance,
    double? newCredit,
    double? newMargin,
    double? newProfit,
    double? newMarginLevel,
  }) {
    var hasChanges = false;

    if (newEquity != null && _equity != newEquity) {
      _equity = newEquity;
      hasChanges = true;
    }
    if (newBalance != null && _balance != newBalance) {
      _balance = newBalance;
      hasChanges = true;
    }
    if (newCredit != null && _credit != newCredit) {
      _credit = newCredit;
      hasChanges = true;
    }
    if (newMargin != null && _margin != newMargin) {
      _margin = newMargin;
      hasChanges = true;
    }
    if (newProfit != null && _profit != newProfit) {
      _profit = newProfit;
      hasChanges = true;
    }
    if (newMarginLevel != null && _marginLevel != newMarginLevel) {
      _marginLevel = newMarginLevel;
      hasChanges = true;
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// Creates a [TradingAccountViewModel] from a [TradingAccountModel].
  factory TradingAccountViewModel.fromModel(TradingAccountModel model) {
    return TradingAccountViewModel(
      name: model.name,
      nickName: model.nickName,
      accountNumber: model.accountNumber,
      platformType: model.platformType,
      platformAccountType: model.platformAccountType,
      equity: model.equity,
      profit: model.profit,
      grossProfit: model.grossProfit,
      isSwapFree: model.isSwapFree,
      homeCurrency: model.homeCurrency,
      accountId: model.recordId,
      balance: model.currentBalance,
      credit: model.credit,
      margin: model.margin,
      marginLevel: model.marginLevel,
      platformTypeName: model.platformTypeName,
    );
  }
}
