import 'package:api_client/api_client.dart';
import 'package:broker_settings/broker_settings.dart';
import 'package:clock/clock.dart';
import 'package:device_calendar/device_calendar.dart';
import 'package:domain/domain.dart';
import 'package:e_trader/src/data/api/document_contents_firestore.dart';
import 'package:e_trader/src/data/api/legal_documents_api.dart';
import 'package:e_trader/src/data/pool/position_model_pool.dart';
import 'package:e_trader/src/data/pool/price_alert_pool.dart';
import 'package:e_trader/src/data/socket/order_model.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/analytics/account_analytics.dart';
import 'package:e_trader/src/domain/analytics/trading_analytics.dart';
import 'package:e_trader/src/domain/repository/account_repository.dart';
import 'package:e_trader/src/domain/repository/activity_repository.dart';
import 'package:campaigns/campaigns.dart';
import 'package:e_trader/src/domain/repository/broker_settings_repository.dart';
import 'package:e_trader/src/domain/repository/calendar_repository.dart';
import 'package:e_trader/src/domain/repository/change_leverage_repository.dart';
import 'package:e_trader/src/domain/repository/create_wallet_repository.dart';
import 'package:e_trader/src/domain/repository/events_news_repository.dart';
import 'package:e_trader/src/domain/repository/funding_repository.dart';
import 'package:e_trader/src/domain/repository/get_active_alert_repository.dart';
import 'package:e_trader/src/domain/repository/historical_repository.dart';
import 'package:e_trader/src/domain/repository/legal_document_repository.dart';
import 'package:e_trader/src/domain/repository/margin_repository.dart';
import 'package:e_trader/src/domain/repository/margin_requirment_hub_repository.dart';
import 'package:e_trader/src/domain/repository/market_hours_repository.dart';
import 'package:e_trader/src/domain/repository/order_repository.dart';
import 'package:e_trader/src/domain/repository/position_repository.dart';
import 'package:e_trader/src/domain/repository/price_alert_repository.dart';
import 'package:e_trader/src/domain/repository/product_detail_info_repository.dart';
import 'package:e_trader/src/domain/repository/register_push_notification_token_repository.dart';
import 'package:e_trader/src/domain/repository/reset_balance_repository.dart';
import 'package:e_trader/src/domain/repository/statements_repository.dart';
import 'package:e_trader/src/domain/repository/switch_account_repository.dart';
import 'package:e_trader/src/domain/repository/symbol_quote_repository.dart';
import 'package:e_trader/src/domain/repository/symbol_repository.dart';
import 'package:e_trader/src/domain/repository/trader_account_preferences_repository.dart';
import 'package:e_trader/src/domain/repository/trading_chart_repository.dart';
import 'package:e_trader/src/domain/repository/watchlist_repository.dart';
import 'package:e_trader/src/domain/repository/withdraw_repository.dart';
import 'package:e_trader/src/domain/usecase/add_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/calculate_partial_close_profit_use_case.dart';
import 'package:e_trader/src/domain/usecase/calculate_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/calendar_use_case.dart';
import 'package:e_trader/src/domain/usecase/change_account_password_use_case.dart';
import 'package:e_trader/src/domain/usecase/change_leverage_use_case.dart';
import 'package:e_trader/src/domain/usecase/check_symbol_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/close_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/create_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/create_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/create_wallet_use_case.dart';
import 'package:e_trader/src/domain/usecase/deal_by_id_use_case.dart';
import 'package:e_trader/src/domain/usecase/delete_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/delete_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/events_news_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/events_use_case.dart';
import 'package:e_trader/src/domain/usecase/fetch_all_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/filter_transfer_destination_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/filter_transfer_source_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_number_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_account_type_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_active_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_broker_settings_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_category_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_funding_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_historical_profit_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_historical_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_language_model_from_language_code_usecase.dart';
import 'package:e_trader/src/domain/usecase/get_legal_documents_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_leverage_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_market_timing_calculator_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_news_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_office_code_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_platform_password_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_product_holidays_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_product_sessions_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_risk_percentage_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_selected_language_code_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_statements_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_symbols_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trade_size_from_volume_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_chart_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_wallet_accounts_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/get_watchlist_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/historical_performance_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_order_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/modify_trade_use_case.dart';
import 'package:e_trader/src/domain/usecase/product_detail_info_usecase.dart';
import 'package:e_trader/src/domain/usecase/push_notification_token_use_case.dart';
import 'package:e_trader/src/domain/usecase/remove_watchlist_use_case.dart';
import 'package:e_trader/src/domain/usecase/reset_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_account_number_and_is_demo_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_close_positions_dialog_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_price_alert_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_id_for_payments_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_selected_account_use_case.dart';
import 'package:e_trader/src/domain/usecase/save_trading_preferences_use_case.dart';
import 'package:e_trader/src/domain/usecase/should_show_close_positions_dialog_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_grouped_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_list_of_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_orders_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_symbol_quotes_use_case.dart';
import 'package:e_trader/src/domain/usecase/subscribe_to_trading_account_balance_use_case.dart';
import 'package:e_trader/src/domain/usecase/symbol_local_data_use_case.dart';
import 'package:e_trader/src/domain/usecase/trading_activity_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_account_details_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_active_alerts_hub_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_margin_requirment_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_order_hub_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_positions_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_quotes_by_symbols_use_case.dart';
import 'package:e_trader/src/domain/usecase/update_trading_account_balance_hub_use_case.dart';
import 'package:e_trader/src/domain/usecase/validate_margin_allocation_use_case.dart';
import 'package:e_trader/src/domain/usecase/watchlist_local_cache_use_case.dart';
import 'package:e_trader/src/domain/usecase/withdraw_use_case.dart';
import 'package:e_trader/src/navigation/equiti_trader_navigation.dart';
import 'package:e_trader/src/presentation/account/bloc/account_details_bloc.dart';
import 'package:e_trader/src/presentation/change_account_password/bloc/change_account_password_bloc.dart';
import 'package:e_trader/src/presentation/change_leverage/bloc/change_leverage_bloc.dart';
import 'package:e_trader/src/presentation/create_new_wallet/bloc/create_new_wallet_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/create_order/create_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_price/order_price_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/input_order_size/input_order_size_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/market_order/market_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_pending_order/bloc/modify_pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/modify_trade/bloc/modify_trade_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/order_limit/order_limit_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/bloc/pending_order/pending_order_bloc.dart';
import 'package:e_trader/src/presentation/create_trade/create_trade_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/input_order_size_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/market_order_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/order_price_widget.dart';
import 'package:e_trader/src/presentation/create_trade/widgets/pending_order_widget.dart';
import 'package:e_trader/src/presentation/discover/events/bloc/events_bloc.dart';
import 'package:e_trader/src/presentation/discover/news/bloc/news_bloc.dart';
import 'package:e_trader/src/presentation/historical_performance/bloc/historical_performance_bloc.dart';
import 'package:e_trader/src/presentation/legal_documents/bloc/legal_documents_bloc.dart';
import 'package:e_trader/src/presentation/market_hours/bloc/market_hours_bloc.dart';
import 'package:e_trader/src/presentation/model/order_limit_config.dart';
import 'package:e_trader/src/presentation/more/trading_settings/bloc/trading_settings_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/account_balance/bloc/account_balance_bloc.dart';
import 'package:e_trader/src/presentation/navigation_bottom_bar/bloc/navigation_bottom_bar_bloc.dart';
import 'package:e_trader/src/presentation/partial_close/bloc/partial_close_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/bloc/performance_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/funding_tab/bloc/funding_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/statements_tab/bloc/statements_bloc.dart';
import 'package:e_trader/src/presentation/performance_screen/trading_tab/bloc/trading_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/orders/bloc/orders_bloc.dart';
import 'package:e_trader/src/presentation/portfolio/positions/bloc/position_bloc.dart';
import 'package:e_trader/src/presentation/position_option/bloc/position_option_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/active_price_alerts/bloc/active_price_alerts_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/edit_price_alert/edit_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/price_alert/set_price_alert/bloc/set_price_alert_bloc.dart';
import 'package:e_trader/src/presentation/product_detail_overview/bloc/product_detail_overview_bloc.dart';
import 'package:e_trader/src/presentation/product_details/bloc/product_details_bloc.dart';
import 'package:e_trader/src/presentation/reset_balance/bloc/reset_balance_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/bloc/accounts_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/rename_account/bloc/rename_account_bloc.dart';
import 'package:e_trader/src/presentation/switch_account/wallet_details/bloc/wallet_details_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/categories/categories_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/search_symbol/search_symbol_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbol_view/symbol_view_cubit.dart';
import 'package:e_trader/src/presentation/symbols/bloc/symbols/category_symbols_bloc.dart';
import 'package:e_trader/src/presentation/symbols/bloc/watchlist_symbols/watchlist_symbols_bloc.dart';
import 'package:e_trader/src/presentation/trade_options_view/bloc/trade_options_bloc.dart';
import 'package:e_trader/src/presentation/trading_chart/bloc/trading_chart_view_bloc.dart';
import 'package:e_trader/src/presentation/watchlisted_symbol_indicator/bloc/watchlisted_symbol_indicator_bloc.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart';
import 'package:monitoring/monitoring.dart';
import 'package:preferences/preferences.dart';
import 'package:socket_client/socket_client.dart';
import 'package:user_account/user_account.dart';
import 'package:validator/validator.dart';

@module
abstract class EquitiTraderModule {
  @injectable
  ResetBalanceRepository resetBalanceRepository(ApiClientBase apiClientBase) =>
      ResetBalanceRepository(apiClientBase);

  @injectable
  ResetBalanceUseCase resetBalanceUseCase(
    ResetBalanceRepository resetBalanceRepository,
  ) => ResetBalanceUseCase(resetBalanceRepository);

  @injectable
  GetAccountNumberUseCase getAccountNumberUseCase(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => GetAccountNumberUseCase(getSelectedAccountUseCase);

  @injectable
  ResetBalanceBloc resetBalanceBloc(
    ResetBalanceUseCase resetBalanceUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    LoggerBase logger,
  ) => ResetBalanceBloc(resetBalanceUseCase, logger);

  @injectable
  LegalDocumentsApi legalDocumentsApi(
    ApiClientBase apiClientBase,
    DocumentContentsFirestore documentContentsFirestore,
  ) => LegalDocumentsApi(apiClientBase, documentContentsFirestore);

  @injectable
  LegalDocumentRepository legalDocumentRepository(
    LegalDocumentsApi legalDocumentsApi,
  ) => LegalDocumentRepository(legalDocumentsApi);

  @injectable
  GetSelectedLanguageCodeUseCase getSelectedLanguageCodeUseCase() =>
      GetSelectedLanguageCodeUseCase();

  @injectable
  GetLegalDocumentsUseCase getLegalDocumentUseCase(
    LegalDocumentRepository legalDocumentRepository,
    GetSelectedLanguageCodeUseCase getSelectedLanguageCodeUseCase,
  ) => GetLegalDocumentsUseCase(
    legalDocumentRepository,
    getSelectedLanguageCodeUseCase,
  );

  @injectable
  LegalDocumentsBloc legalDocumentsBloc(
    GetLegalDocumentsUseCase getLegalDocumentUseCase,
    LoggerBase logger,
  ) => LegalDocumentsBloc(getLegalDocumentUseCase, logger);

  @injectable
  WithdrawRepository withdrawRepository(ApiClientBase apiClientBase) =>
      WithdrawRepository(apiClientBase);

  @injectable
  WithdrawUseCase withdrawUseCase(
    WithdrawRepository withdrawRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
    GetAccountTypeUseCase getAccountTypeUseCase,
  ) => WithdrawUseCase(
    getAccountNumberUseCase: getAccountNumberUseCase,
    getAccountTypeUseCase: getAccountTypeUseCase,
    withdrawRepository: withdrawRepository,
  );

  @injectable
  BrokerSettingsRepository getBrokerSettingRepository(
    ApiClientBase apiClientBase,
  ) => BrokerSettingsRepository(apiClientBase: apiClientBase);

  @injectable
  GetBrokerSettingsUseCase getBrokerSettingsUseCase(
    BrokerSettingsRepository brokerSettingRepository,
  ) => GetBrokerSettingsUseCase(
    brokerSettingRepository: brokerSettingRepository,
  );

  @injectable
  MarginRequirmentHubRepository marginRequirmentRepository(
    SocketClient socketClient,
  ) => MarginRequirmentHubRepository(socketClient: socketClient);

  @injectable
  OrderRepository orderRepository(
    ApiClientBase apiClientBase,
    SocketClient socketClient,
  ) => OrderRepository(apiClient: apiClientBase, socketClient: socketClient);

  @lazySingleton
  PositionModelPool positionModelPool() => PositionModelPool();

  @injectable
  PositionRepository positionRepository(
    ApiClientBase apiClientBase,
    SocketClient socketClient,
    PositionModelPool positionModelPool,
  ) => PositionRepository(
    apiClient: apiClientBase,
    socketClient: socketClient,
    positionModelPool: positionModelPool,
  );

  @injectable
  CreateOrderUseCase createOrderUseCase(OrderRepository orderRepository) =>
      CreateOrderUseCase(orderRepository: orderRepository);

  @injectable
  CreateTradeUseCase createTradeUseCase(
    PositionRepository positionRepository,
  ) => CreateTradeUseCase(positionRepository: positionRepository);

  @injectable
  SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase(
    MarginRequirmentHubRepository marginRequirmentRepository,
  ) => SubscribeToMarginRequirmentUseCase(
    marginRequirmentRepository: marginRequirmentRepository,
  );

  @injectable
  CalculateVolumeUseCase calculateVolumeUseCase() => CalculateVolumeUseCase();

  @injectable
  CreateTradeBloc createTradeBloc(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase,
    SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
    CalculateVolumeUseCase calculateVolumeUseCase,
    @factoryParam CreateTradeArgs args,
    LoggerBase logger,
    UpdateMarginRequirmentUseCase updateMarginRequirmentUseCase,
  ) => CreateTradeBloc(
    getSelectedAccountUseCase,
    subscribeToMarginRequirmentUseCase,
    subscribeToSymbolQuotesUseCase,
    calculateVolumeUseCase,
    args,
    logger,
    updateMarginRequirmentUseCase,
  );

  @injectable
  InputOrderSizeBloc inputOrderSizeBloc(@factoryParam OrderSizeArgs args) =>
      InputOrderSizeBloc(args: args);

  @injectable
  OrderPriceBloc inputOrderPriceBloc(
    SubscribeToSymbolQuotesUseCase subscribeToProductQuotesUseCase,
    @factoryParam OrderPriceArgs args,
  ) => OrderPriceBloc(subscribeToProductQuotesUseCase, args);

  @injectable
  SymbolRepository getSymbolRepository(ApiClientBase apiClientBase) =>
      SymbolRepository(apiClientBase: apiClientBase);

  @injectable
  TradingChartRepository tradingChartRepository(ApiClientBase apiClientBase) =>
      TradingChartRepository(apiClientBase: apiClientBase);

  @injectable
  AccountRepository getAccountRepository(
    ApiClientBase apiClientBase,
    SocketClient baseSocketClient,
    EquitiPreferences preferences,
    AuthService authService,
  ) => AccountRepository(
    apiClientBase,
    baseSocketClient,
    preferences,
    () => diContainer<AuthService>(),
  );

  @injectable
  GetCategoryUseCase getCategoryUseCase(
    SymbolRepository symbolRepository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => GetCategoryUseCase(getBrokerIdUseCase, symbolRepository);

  @injectable
  SubscribeToListOfSymbolQuotesUseCase getProductsBySymbolUseCase(
    SymbolQuoteRepository symbolQuoteRepository,
  ) => SubscribeToListOfSymbolQuotesUseCase(symbolQuoteRepository);
  @injectable
  GetSymbolsUseCase getSymbolsUseCase(
    AccountRepository accountRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => GetSymbolsUseCase(accountRepository, getAccountNumberUseCase);
  @injectable
  UpdateQuotesBySymbolsUseCase updateQuotesBySymbolsUseCase(
    SymbolQuoteRepository symbolQuoteRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => UpdateQuotesBySymbolsUseCase(
    symbolQuoteRepository,
    getAccountNumberUseCase,
  );

  @injectable
  CategorySymbolsBloc categorySymbolsBloc(
    GetSymbolsUseCase getSymbolsUseCase,
    SymbolLocalDataUseCase symbolLocalDataUseCase,
    SubscribeToListOfSymbolQuotesUseCase getProductsBySymbolUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    EquitiTraderNavigation navigation,
    @factoryParam String categoryId,
    @factoryParam SymbolViewCubit symbolViewCubit,
    WatchlistLocalCacheUseCase watchlistLocalCacheUseCase,
  ) => CategorySymbolsBloc(
    getSymbolsUseCase,
    symbolLocalDataUseCase,
    getProductsBySymbolUseCase,
    getAccountNumberUseCase,
    navigation,
    symbolViewCubit,
    categoryId,
    watchlistLocalCacheUseCase,
  );

  @injectable
  SearchSymbolBloc searchSymbolBloc(
    GetSymbolsUseCase getSymbolsUseCase,
    SubscribeToListOfSymbolQuotesUseCase subscribeToSymbolsQuotesUseCase,
    SymbolLocalDataUseCase symbolLocalDataUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    EquitiTraderNavigation navigation,
    @factoryParam SymbolViewCubit symbolViewCubit,
    TradingAnalytics tradingAnalyticsEvent,
  ) => SearchSymbolBloc(
    getSymbolsUseCase,
    subscribeToSymbolsQuotesUseCase,
    symbolLocalDataUseCase,
    getAccountNumberUseCase,
    navigation,
    symbolViewCubit,
    tradingAnalyticsEvent,
  );

  @injectable
  WatchlistSymbolsBloc watchlistSymbolsBloc(
    SubscribeToListOfSymbolQuotesUseCase subscribeToSymbolsQuotesUseCase,
    GetWatchlistDataUseCase getWatchlistDataUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    EquitiTraderNavigation navigation,
    @factoryParam SymbolViewCubit symbolViewCubit,
    LoggerBase logger,
  ) => WatchlistSymbolsBloc(
    subscribeToSymbolsQuotesUseCase,
    getWatchlistDataUseCase,
    getAccountNumberUseCase,
    navigation,
    symbolViewCubit,
    logger,
  );
  @injectable
  CategoriesBloc categoriesBloc(
    GetCategoryUseCase getSymbolCategoryUseCase,
    LoggerBase logger,
  ) => CategoriesBloc(getSymbolCategoryUseCase);

  @injectable
  SymbolViewCubit symbolViewCubit(
    SymbolLocalDataUseCase symbolLocalDataUseCase,
  ) => SymbolViewCubit(symbolLocalDataUseCase);

  @injectable
  SymbolQuoteRepository symbolQuoteRepository(SocketClient baseSocketClient) =>
      SymbolQuoteRepository(socketClient: baseSocketClient);

  @injectable
  SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase(
    SymbolQuoteRepository repository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => SubscribeToSymbolQuotesUseCase(
    getAccountNumberUseCase: getAccountNumberUseCase,
    symbolQuoteRepository: repository,
  );

  @injectable
  FilterTransferSourceAccountsUseCase getTransferSourceAccountUseCase() =>
      FilterTransferSourceAccountsUseCase();

  @injectable
  FilterTransferDestinationAccountsUseCase
  getTransferDestinationAccountUseCase() =>
      FilterTransferDestinationAccountsUseCase();

  @injectable
  SwitchAccountRepository switchAccountRepository(
    ApiClientBase apiClientBase,
    @Named('mobileBffApiClient') ApiClientBase mobileBffClient,
  ) => SwitchAccountRepository(apiClientBase, mobileBffClient);

  @injectable
  GetTradingAccountsUseCase getTradingAccountsUseCase(
    SwitchAccountRepository switchAccountRepository,
  ) => GetTradingAccountsUseCase(switchAccountRepository);

  @injectable
  GetWalletAccountsUseCase getWalletAccountsUseCase(
    SwitchAccountRepository switchAccountRepository,
  ) => GetWalletAccountsUseCase(switchAccountRepository);

  @injectable
  UpdateAccountDetailsUseCase updateAccountDetailsUseCase(
    SwitchAccountRepository switchAccountRepository,
  ) => UpdateAccountDetailsUseCase(switchAccountRepository);

  @injectable
  MarginRepository marginRepository(SocketClient baseSocketClient) =>
      MarginRepository(socketClient: baseSocketClient);

  @injectable
  SubscribeToTradingAccountBalanceUseCase getTradingAccountUseCase(
    MarginRepository marginRepository,
  ) => SubscribeToTradingAccountBalanceUseCase(marginRepository);
  @injectable
  GetRiskPercentageUseCase getRiskPercentageUseCase() =>
      GetRiskPercentageUseCase();

  @injectable
  SaveAccountNumberAndIsDemoUseCase saveAccountNumberAndIsDemoUseCase(
    AccountRepository accountRepository,
  ) => SaveAccountNumberAndIsDemoUseCase(accountRepository);

  MarketHoursRepository marketHoursRepository(ApiClientBase apiClientBase) =>
      MarketHoursRepository(apiClientBase: apiClientBase);

  @injectable
  GetProductSessionsUseCase getProductSessionsUseCase(
    MarketHoursRepository repository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => GetProductSessionsUseCase(
    repository: repository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  GetProductHolidaysUseCase getMarketHoursUseCase(
    MarketHoursRepository repository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => GetProductHolidaysUseCase(
    repository: repository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  GetMarketTimingCalculatorUseCase getMarketTimingCalculatorUseCase(
    GetProductSessionsUseCase getProductSessionsUseCase,
    GetProductHolidaysUseCase getProductHolidaysUseCase,
    Clock clock,
  ) => GetMarketTimingCalculatorUseCase(
    getProductHolidaysUseCase: getProductHolidaysUseCase,
    getProductSessionsUseCase: getProductSessionsUseCase,
    clock: clock,
  );

  @injectable
  MarketHoursBloc marketHours(
    GetMarketTimingCalculatorUseCase getMarketTimingCalculatorUseCase,
    LoggerBase logger,
    SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
  ) => MarketHoursBloc(
    getMarketLimitCalculatorUseCase: getMarketTimingCalculatorUseCase,
    logger: logger,
    subscribeToSymbolQuotesUseCase: subscribeToSymbolQuotesUseCase,
  );

  @injectable
  EditPriceAlertBloc editPriceAlertBloc() => EditPriceAlertBloc();

  @injectable
  SetPriceAlertBloc setPriceAlertBloc(
    SavePriceAlertUseCase savePriceAlertUseCase,
    ModifyPriceAlertUseCase modifyPriceAlertUseCase,
    SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
    DeletePriceAlertUseCase deletePriceAlertUseCase,
    TradingAnalytics tradingAnalyticsEvent,
  ) => SetPriceAlertBloc(
    savePriceAlertUseCase,
    subscribeToSymbolQuotesUseCase,
    deletePriceAlertUseCase,
    modifyPriceAlertUseCase,
    tradingAnalyticsEvent,
  );

  @injectable
  ProductDetailsBloc productDetailswBloc(
    SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
    TradingAnalytics tradingAnalyticsEvent,
  ) =>
      ProductDetailsBloc(subscribeToSymbolQuotesUseCase, tradingAnalyticsEvent);

  @injectable
  ProductDetailOverviewBloc productOverviewBloc(
    ProductDetailInfoUseCase productInfoUserCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) =>
      ProductDetailOverviewBloc(productInfoUserCase, getSelectedAccountUseCase);

  @injectable
  ProductDetailInfoUseCase productInfoUseCase(
    ProductDetailInfoRepository productRepository,
  ) => ProductDetailInfoUseCase(productRepository: productRepository);

  @injectable
  ProductDetailInfoRepository productInfoRepository(
    ApiClientBase apiClientBase,
  ) => ProductDetailInfoRepository(apiClientBase: apiClientBase);

  @injectable
  SymbolLocalDataUseCase symbolLocalDataUseCase(
    EquitiPreferences preferences,
  ) => SymbolLocalDataUseCase(preferences: preferences);

  @injectable
  CheckSymbolWatchlistUseCase checkSymbolWatchlistUseCase(
    GetAccountNumberUseCase getAccountNumberUseCase,
    SymbolRepository symbolRepository,
  ) => CheckSymbolWatchlistUseCase(
    getAccountNumberUseCase: getAccountNumberUseCase,
    symbolRepository: symbolRepository,
  );

  @injectable
  WatchlistRepository watchlistRepository() =>
      WatchlistRepository(apiClient: () => diContainer<ApiClientBase>());

  @injectable
  AddWatchlistUseCase addWatchlistUseCase(
    WatchlistRepository watchlistRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => AddWatchlistUseCase(
    watchlistRepository: watchlistRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  RemoveWatchlistUseCase removeWatchlistUseCase(
    WatchlistRepository watchlistRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => RemoveWatchlistUseCase(
    watchlistRepository: watchlistRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  WatchlistedSymbolIndicatorBloc watchlistedSymbolIndicatorBloc(
    AddWatchlistUseCase addWatchlistUseCase,
    RemoveWatchlistUseCase removeWatchlistUseCase,
    WatchlistLocalCacheUseCase watchlistLocalCacheUseCase,
    LoggerBase logger,
    CheckSymbolWatchlistUseCase checkSymbolWatchlistUseCase,
  ) => WatchlistedSymbolIndicatorBloc(
    addWatchlistUseCase,
    removeWatchlistUseCase,
    watchlistLocalCacheUseCase,
    logger,
    checkSymbolWatchlistUseCase,
  );

  @injectable
  SavePriceAlertUseCase saveProceAlertUseCase(
    PriceAlertRepository repository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => SavePriceAlertUseCase(repository, getAccountNumberUseCase);

  @injectable
  PriceAlertRepository priceAlertRepository(ApiClientBase apiClientBase) =>
      PriceAlertRepository(apiClientBase: apiClientBase);

  OrderLimitBloc orderLimitBloc(
    @factoryParam OrderLimitConfig config,
    @factoryParam bool isOrderLimitEnabled,
  ) => OrderLimitBloc(config: config, isEnabled: isOrderLimitEnabled);

  @singleton
  InputValidator get inputValidator => InputValidator();

  @injectable
  ValidateMarginAllocationUseCase validateMarginAllocationUseCase(
    InputValidator inputValidatorForMarginAllocation,
  ) => ValidateMarginAllocationUseCase(inputValidatorForMarginAllocation);

  @injectable
  MarketOrderBloc marketOrderBloc(
    @factoryParam MarketOrderArgs args,
    CreateTradeUseCase createTradeUseCase,
    CalculateVolumeUseCase calculateVolumeUseCase,
    ValidateMarginAllocationUseCase validateMarginAllocationUseCase,
    EquitiTraderNavigation equitiTraderNavigation,
    TradingAnalytics tradingAnalyticsEvent,
  ) => MarketOrderBloc(
    args,
    createTradeUseCase,
    calculateVolumeUseCase,
    validateMarginAllocationUseCase,
    equitiTraderNavigation,
    tradingAnalyticsEvent,
  );

  @injectable
  PendingOrderBloc pendingOrderBloc(
    @factoryParam PendingOrderArgs args,
    CreateOrderUseCase createOrderUseCase,
    CalculateVolumeUseCase calculateVolumeUseCase,
    ValidateMarginAllocationUseCase validateMarginAllocationUseCase,
    EquitiTraderNavigation equitiTraderNavigation,
    TradingAnalytics tradingAnalyticsEvent,
  ) => PendingOrderBloc(
    args,
    validateMarginAllocationUseCase,
    calculateVolumeUseCase,
    createOrderUseCase,
    equitiTraderNavigation,
    tradingAnalyticsEvent,
  );

  @injectable
  ActivePriceAlertsBloc activePriceAlerts(
    GetActiveAlertUseCase getActiveAlertUseCase,
    UpdateActiveAlertsHubUseCase updateActiveAlertsHubUseCase,
    LoggerBase logger,
    TradingAnalytics tradingAnalyticsEvent,
  ) => ActivePriceAlertsBloc(
    getActiveAlertUseCase,
    updateActiveAlertsHubUseCase,
    logger,
    tradingAnalyticsEvent,
  );

  @injectable
  GetActiveAlertUseCase getActiveAlertUseCase(
    GetActiveAlertRepository activeAlertRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => GetActiveAlertUseCase(
    activeAlertRepository: activeAlertRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @lazySingleton
  PriceAlertPool priceAlertPool() => PriceAlertPool();

  @injectable
  GetActiveAlertRepository getActiveAlertRepository(
    SocketClient socketClient,
    PriceAlertPool priceAlertPool,
  ) => GetActiveAlertRepository(
    socketClient: socketClient,
    alertPool: priceAlertPool,
  );

  @injectable
  ModifyPriceAlertUseCase getModifyPriceAlertUseCase(
    PriceAlertRepository priceAlertRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => ModifyPriceAlertUseCase(priceAlertRepository, getAccountNumberUseCase);

  @injectable
  DeletePriceAlertUseCase deletePriceAlertUseCase(
    PriceAlertRepository priceAlertRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => DeletePriceAlertUseCase(priceAlertRepository, getAccountNumberUseCase);

  @injectable
  GetTradeSizeFromVolumeUseCase getTradeSizeFromVolumeUseCase() =>
      GetTradeSizeFromVolumeUseCase();

  @injectable
  SubscribeToPositionsUseCase subscribeToPositionsUseCase(
    PositionRepository positionRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => SubscribeToPositionsUseCase(positionRepository, getAccountNumberUseCase);

  @injectable
  SubscribeToGroupedPositionsUseCase subscribeToGroupedPositionsUseCase(
    PositionRepository positionRepository,
  ) => SubscribeToGroupedPositionsUseCase(positionRepository);

  @injectable
  PositionBloc positionBloc(
    SubscribeToPositionsUseCase subscribeToPositionsUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    LoggerBase logger,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    @factoryParam String platformName,
    TradingAnalytics tradingAnalyticsEvent,
  ) => PositionBloc(
    subscribeToPositionsUseCase,
    updatePositionsUseCase,
    getSelectedAccountUseCase,
    logger,
    platformName,
    tradingAnalyticsEvent,
  );

  @injectable
  GetTradingChartUseCase getTradingChartUseCase(
    TradingChartRepository tradingChartRepository,
  ) => GetTradingChartUseCase(tradingChartRepository);

  TradingChartViewBloc tradingChartViewBloc(
    LoggerBase logger,
    EquitiPreferences preferences,
    GetAccountNumberUseCase getAccountNumberUseCase,
    GetTradingChartUseCase getTradingChartUseCase,
    EquitiTraderNavigation equitiTraderNavigation,
    SubscribeToSymbolQuotesUseCase subscribeToSymbolQuotesUseCase,
    SubscribeToPositionsUseCase subscribeToPositionsUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => TradingChartViewBloc(
    logger: logger,
    preferences: preferences,
    getAccountNumberUseCase: getAccountNumberUseCase,
    getTradingChartUseCase: getTradingChartUseCase,
    subscribeToSymbolQuotesUseCase: subscribeToSymbolQuotesUseCase,
    subscribeToPositionsUseCase: subscribeToPositionsUseCase,
    updatePositionsUseCase: updatePositionsUseCase,
    getSelectedAccountUseCase: getSelectedAccountUseCase,
  );

  @injectable
  ShouldShowClosePositionsDialogUseCase shouldShowClosePositionsDialogUseCase(
    TraderAccountPreferencesRepository traderAccountPreferencesRepository,
  ) =>
      ShouldShowClosePositionsDialogUseCase(traderAccountPreferencesRepository);

  @injectable
  SaveClosePositionsDialogUseCase saveClosePositionsDialogUseCase(
    TraderAccountPreferencesRepository traderAccountPreferencesRepository,
  ) => SaveClosePositionsDialogUseCase(traderAccountPreferencesRepository);

  @injectable
  TradeOptionsBloc tradeOptionsBloc(
    @factoryParam PositionModel position,
    SubscribeToPositionsUseCase getPositionUpdatesUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    GetTradeSizeFromVolumeUseCase getTradeSizeFromVolumeUseCase,
    CloseTradeUseCase _closeTradeUseCase,
    TradingAnalytics tradingAnalyticsEvent,
  ) => TradeOptionsBloc(
    position,
    getPositionUpdatesUseCase,
    updatePositionsUseCase,
    getAccountNumberUseCase,
    getTradeSizeFromVolumeUseCase,
    _closeTradeUseCase,
    tradingAnalyticsEvent,
  );

  @injectable
  PositionOptionBloc positionOptionBloc(
    SubscribeToGroupedPositionsUseCase _subscribeToGroupedPositionsUseCase,
    GetAccountNumberUseCase _getAccountNumberUseCase,
    EquitiTraderNavigation _equitiTraderNavigation,
    ShouldShowClosePositionsDialogUseCase
    _shouldShowClosePositionsDialogUseCase,
    SaveClosePositionsDialogUseCase _saveClosePositionsDialogUseCase,
    CloseTradeUseCase _closeTradeUseCase,
    GetSelectedAccountUseCase _getSelectedAccountUseCase,
    TradingAnalytics _tradingAnalyticsEvent,
    UpdatePositionsUseCase _updatePositionsUseCase,
  ) => PositionOptionBloc(
    _subscribeToGroupedPositionsUseCase,
    _getAccountNumberUseCase,
    _closeTradeUseCase,
    _shouldShowClosePositionsDialogUseCase,
    _saveClosePositionsDialogUseCase,
    _equitiTraderNavigation,
    _getSelectedAccountUseCase,
    _tradingAnalyticsEvent,
    _updatePositionsUseCase,
  );
  SubscribeToOrdersUseCase subscribeToOrdersUseCase(
    OrderRepository orderRepository,
  ) => SubscribeToOrdersUseCase(orderRepository);

  @injectable
  OrdersBloc ordersBloc(
    SubscribeToOrdersUseCase _subscribeToOrdersUseCase,
    GetAccountNumberUseCase _getAccountNumberUseCase,
    UpdateOrderHubUseCase _updateOrderHubUseCase,
    @factoryParam String platformName,
    TradingAnalytics tradingAnalyticsEvent,
  ) => OrdersBloc(
    _subscribeToOrdersUseCase,
    _updateOrderHubUseCase,
    _getAccountNumberUseCase,
    platformName,
    tradingAnalyticsEvent,
  );

  @injectable
  NavigationBottomBarBloc navigationBottomBarBloc(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    LoggerBase logger,
    EquitiTraderNavigation equitiTraderNavigation,
    GetTradingAccountsUseCase getTradingAccountsUseCase,
  ) => NavigationBottomBarBloc(
    getSelectedAccountUseCase,
    logger,
    equitiTraderNavigation,
    getTradingAccountsUseCase,
  );

  @injectable
  FetchAllWatchlistUseCase fetchAllWatchlistUseCase(
    GetAccountNumberUseCase getAccountNumberUseCase,
    WatchlistRepository watchlistRepository,
  ) => FetchAllWatchlistUseCase(
    getAccountNumberUseCase: getAccountNumberUseCase,
    watchlistRepository: watchlistRepository,
  );

  @injectable
  WatchlistLocalCacheUseCase watchlistLocalCacheUseCase(
    EquitiPreferences preferences,
  ) => WatchlistLocalCacheUseCase(preferences: preferences);

  @injectable
  ModifyOrderUseCase modifyOrderUseCase(OrderRepository orderRepository) =>
      ModifyOrderUseCase(orderRepository);

  @injectable
  DeleteOrderUseCase deleteOrderUseCase(OrderRepository orderRepository) =>
      DeleteOrderUseCase(orderRepository: orderRepository);

  @injectable
  ModifyPendingOrderBloc modifyPendingOrderBloc(
    @factoryParam OrderModel orderModel,
    SubscribeToOrdersUseCase subscribeToOrderUseCase,
    ModifyOrderUseCase modifyOrderUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase,
    DeleteOrderUseCase deleteOrderUseCase,
    TradingAnalytics tradingAnalyticsEvent,
  ) => ModifyPendingOrderBloc(
    subscribeToOrderUseCase,
    modifyOrderUseCase,
    getAccountNumberUseCase,
    subscribeToMarginRequirmentUseCase,
    deleteOrderUseCase,
    orderModel,
    tradingAnalyticsEvent,
  );

  @injectable
  PartialCloseBloc partialCloseBloc(
    SubscribeToPositionsUseCase _subscribeToPositionsUseCase,
    GetAccountNumberUseCase _getAccountNumberUseCase,
    CloseTradeUseCase _closeTradeUseCase,
    CalculatePartialCloseProfitUseCase _calculatePartialCloseProfitUseCase,
    GetTradeSizeFromVolumeUseCase _getTradeSizeFromVolumeUseCase,
    GetSelectedAccountUseCase _getSelectedAccountUseCase,
    UpdatePositionsUseCase _updatePositionsUseCase,
    TradingAnalytics tradingAnalyticsEvent,
  ) => PartialCloseBloc(
    _subscribeToPositionsUseCase,
    _getAccountNumberUseCase,
    _closeTradeUseCase,
    _calculatePartialCloseProfitUseCase,
    _getSelectedAccountUseCase,
    _updatePositionsUseCase,
    tradingAnalyticsEvent,
  );

  @injectable
  CloseTradeUseCase closeTradeUseCase(PositionRepository positionRepository) =>
      CloseTradeUseCase(positionRepository: positionRepository);

  @injectable
  CalculatePartialCloseProfitUseCase calculatePartialCloseProfitUseCase() =>
      CalculatePartialCloseProfitUseCase();

  @injectable
  TradingSettingsBloc tradingSettingsBloc(
    GetTradingPreferencesUseCase getTradingPreferencesUseCase,
    SaveTradingPreferencesUseCase saveTradingPreferencesUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    ShouldShowClosePositionsDialogUseCase shouldShowClosePositionsDialogUseCase,
    SaveClosePositionsDialogUseCase saveClosePositionsDialogUseCase,
  ) => TradingSettingsBloc(
    getTradingPreferencesUseCase: getTradingPreferencesUseCase,
    saveTradingPreferencesUseCase: saveTradingPreferencesUseCase,
    getSelectedAccountUseCase: getSelectedAccountUseCase,
    shouldShowClosePositionsDialogUseCase:
        shouldShowClosePositionsDialogUseCase,
    saveClosePositionsDialogUseCase: saveClosePositionsDialogUseCase,
  );

  @injectable
  GetTradingPreferencesUseCase getTradingPreferencesUseCase(
    TraderAccountPreferencesRepository preferences,
  ) => GetTradingPreferencesUseCase(preferences: preferences);

  @injectable
  SaveTradingPreferencesUseCase saveTradingPreferencesUseCase(
    TraderAccountPreferencesRepository preferences,
  ) => SaveTradingPreferencesUseCase(preferences: preferences);

  @injectable
  TraderAccountPreferencesRepository traderAccountPreferencesRepository(
    EquitiPreferences preferences,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => TraderAccountPreferencesRepository(
    preferences: preferences,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  GetLanguageModelFromLanguageCodeUsecase
  getLanguageModelFromLanguageCodeUsecase() =>
      GetLanguageModelFromLanguageCodeUsecase();

  @injectable
  ModifyTradeUseCase modifyTradeUseCase(
    PositionRepository positionRepository,
  ) => ModifyTradeUseCase(positionRepository);

  @injectable
  ModifyTradeBloc modifyTradeBloc(
    SubscribeToPositionsUseCase subscribeToPositionsUseCase,
    ModifyTradeUseCase modifyTradeUseCase,
    GetAccountNumberUseCase getAccountNumberUseCase,
    SubscribeToMarginRequirmentUseCase subscribeToMarginRequirmentUseCase,
    UpdatePositionsUseCase updatePositionsUseCase,
    UpdateMarginRequirmentUseCase updateMarginRequirmentUseCase,
    @factoryParam PositionModel positionModel,
    TradingAnalytics tradingAnalyticsEvent,
  ) => ModifyTradeBloc(
    subscribeToPositionsUseCase,
    modifyTradeUseCase,
    getAccountNumberUseCase,
    subscribeToMarginRequirmentUseCase,
    updatePositionsUseCase,
    updateMarginRequirmentUseCase,
    positionModel,
    tradingAnalyticsEvent,
  );

  @injectable
  ChangeLeverageRepository changeLeverageRepository(
    ApiClientBase apiClientBase,
  ) => ChangeLeverageRepository(apiClient: apiClientBase);

  @injectable
  RegisterPushNotificationTokenRepository
  registerPushNotificationTokenRepository(
    ApiClientBase apiClientBase,
    EquitiPreferences preferences,
  ) => RegisterPushNotificationTokenRepository(
    apiClient: apiClientBase,
    preferences: preferences,
  );

  @injectable
  GetLeverageUseCase getLeverageUseCase(
    ChangeLeverageRepository changeLeverageRepository,
    BrokerSettingsRepository brokerSettingsRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
    GetOfficeCodeUseCase getOfficeCodeUseCase,
  ) => GetLeverageUseCase(
    changeLeverageRepository: changeLeverageRepository,
    brokerSettingsRepository: brokerSettingsRepository,
    getOfficeCodeUseCase: getOfficeCodeUseCase,
  );

  @injectable
  ChangeLeverageUseCase changeLeverageUseCase(
    GetAccountNumberUseCase getAccountNumberUseCase,
    ChangeLeverageRepository changeLeverageRepository,
  ) =>
      ChangeLeverageUseCase(changeLeverageRepository: changeLeverageRepository);

  @injectable
  PushNotificationTokenUseCase pushNotificationTokenUseCase(
    RegisterPushNotificationTokenRepository
    registerPushNotificationTokenRepository,
  ) => PushNotificationTokenUseCase(
    registerPushNotificationTokenRepository:
        registerPushNotificationTokenRepository,
  );

  @injectable
  ChangeLeverageBloc changeLeverageBloc(
    GetLeverageUseCase getLeverageUseCase,
    ChangeLeverageUseCase changeLeverageUseCase,
    LoggerBase logger,
    GetTradingPreferencesUseCase getTradingPreferencesUseCase,
    SaveTradingPreferencesUseCase saveTradingPreferencesUseCase,
    @factoryParam int? leverage,
  ) => ChangeLeverageBloc(
    getLeverageUseCase: getLeverageUseCase,
    changeLeverageUseCase: changeLeverageUseCase,
    logger: logger,
    getTradingPreferencesUseCase: getTradingPreferencesUseCase,
    saveTradingPreferencesUseCase: saveTradingPreferencesUseCase,
    leverage: leverage,
  );

  @injectable
  StatementsRepository statementsRepository(ApiClientBase apiClientBase) =>
      StatementsRepository(apiClient: apiClientBase);

  @injectable
  GetStatementsUseCase getStatementsUseCase(
    StatementsRepository statementsRepository,
  ) => GetStatementsUseCase(statementsRepository);

  @injectable
  StatementsBloc statementsBloc(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    GetStatementsUseCase getStatementsUseCase,
    Clock clock,
  ) => StatementsBloc(getSelectedAccountUseCase, getStatementsUseCase, clock);
  @injectable
  PerformanceBloc performanceBloc(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => PerformanceBloc(getSelectedAccountUseCase);
  @injectable
  EventsBloc eventsBloc(
    EventsUseCase _eventsUseCase,
    EventsNewsLocalDataUseCase _eventsLocalDataUseCase,
    CalendarUseCase _calendarUseCase,
    @factoryParam String? _tickerName,
  ) => EventsBloc(
    _eventsUseCase,
    _eventsLocalDataUseCase,
    _calendarUseCase,
    _tickerName,
  );
  @injectable
  EventsUseCase eventsUseCase(EventsNewsRepository _repository) =>
      EventsUseCase(_repository);
  @injectable
  EventsNewsRepository eventsRepository(ApiClientBase apiClientBase) =>
      EventsNewsRepository(apiClientBase);
  @injectable
  EventsNewsLocalDataUseCase eventsNewsLocalDataUseCase(
    EquitiPreferences _prefernces,
  ) => EventsNewsLocalDataUseCase(preferences: _prefernces);
  @injectable
  NewsBloc newsBloc(
    GetNewsUseCase _getNewsUseCase,
    EventsNewsLocalDataUseCase _eventsLocalDataUseCase,
    LocaleManager localeManager,
    @factoryParam String? _tickerName,
  ) => NewsBloc(_getNewsUseCase, _eventsLocalDataUseCase, _tickerName);
  @injectable
  GetNewsUseCase getNewsUseCase(EventsNewsRepository _repository) =>
      GetNewsUseCase(_repository);
  @injectable
  CalendarRepository calendarService(
    EventsNewsLocalDataUseCase _localDataUseCase,
    DeviceCalendarPlugin deviceCalendarPlugin,
  ) => CalendarRepository(deviceCalendarPlugin);
  @injectable
  CalendarUseCase calendarUseCase(CalendarRepository _calendarService) =>
      CalendarUseCase(_calendarService);
  @injectable
  DeviceCalendarPlugin deviceCalendarPlugin() => DeviceCalendarPlugin();
  @injectable
  GetWatchlistDataUseCase getWatchlistData(
    WatchlistRepository watchlistRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => GetWatchlistDataUseCase(
    watchlistRepository: watchlistRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );
  @injectable
  SaveSelectedAccountUseCase saveSelectedAccountUseCase(
    EquitiPreferences equitiPreferences,
  ) => SaveSelectedAccountUseCase(equitiPreferences);

  @injectable
  GetSelectedAccountUseCase getSelectedAccountUseCase(
    EquitiPreferences equitiPreference,
  ) => GetSelectedAccountUseCase(equitiPreference);

  @injectable
  SaveSelectedAccountIdForPaymentsUseCase
  saveSelectedAccountIdForPaymentsUseCase(EquitiPreferences equitiPreference) =>
      SaveSelectedAccountIdForPaymentsUseCase(equitiPreference);

  @injectable
  AccountsBloc accountsBloc(
    GetTradingAccountsUseCase getTradingAccountsUseCase,
    GetWalletAccountsUseCase getWalletAccountsUseCase,
    SaveSelectedAccountUseCase saveSelectedAccountUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    EquitiTraderNavigation equitiTraderNavigation,
    SubscribeToTradingAccountBalanceUseCase
    subscribeToTradingAccountBalanceUseCase,
    UpdateTradingAccountBalanceHubUseCase updateTradingAccountBalanceHubUseCase,
    SaveTradingPreferencesUseCase saveTradingPreferencesUseCase,
    PushNotificationTokenUseCase pushNotificationTokenUseCase,
    SaveSelectedAccountIdForPaymentsUseCase
    saveSelectedAccountIdForPaymentsUseCase,
    AccountAnalytics accountAnalyticsEvent,
    WatchlistLocalCacheUseCase watchlistLocalCacheUseCase,
    GetCampaignsUseCase getCampaignsUseCase,
  ) => AccountsBloc(
    getTradingAccountsUseCase,
    getWalletAccountsUseCase,
    saveSelectedAccountUseCase,
    getSelectedAccountUseCase,
    subscribeToTradingAccountBalanceUseCase,
    updateTradingAccountBalanceHubUseCase,
    equitiTraderNavigation,
    saveTradingPreferencesUseCase,
    pushNotificationTokenUseCase,
    saveSelectedAccountIdForPaymentsUseCase,
    accountAnalyticsEvent,
    watchlistLocalCacheUseCase,
    getCampaignsUseCase,
  );

  @injectable
  UpdateMarginRequirmentUseCase updateMarginRequirmentUseCase(
    MarginRequirmentHubRepository marginRequirmentRepository,
  ) => UpdateMarginRequirmentUseCase(marginRequirmentRepository);

  @injectable
  FundingRepository fundingRepository(ApiClientBase apiClient) =>
      FundingRepository(apiClient);

  @injectable
  GetFundingUseCase getFundingUseCase(FundingRepository fundingRepository) =>
      GetFundingUseCase(fundingRepository);

  @injectable
  FundingBloc fundingBloc(
    GetSelectedAccountUseCase getSelectedAccountUseCase,
    GetFundingUseCase getFundingUseCase,
    Clock clock,
  ) => FundingBloc(getSelectedAccountUseCase, getFundingUseCase, clock);

  @injectable
  AccountDetailsBloc accountDetailsBloc(
    GetSelectedAccountUseCase _getSelectedAccountUseCase,
    SubscribeToTradingAccountBalanceUseCase _getTradingAccountUseCase,
    UpdateTradingAccountBalanceHubUseCase updateTradingAccountBalanceHubUseCase,
    GetTradingPreferencesUseCase getTradingPreferencesUseCase,
    @factoryParam TradingAccountModel? tradingAccountModel,
    LoggerBase logger,
  ) => AccountDetailsBloc(
    _getSelectedAccountUseCase,
    _getTradingAccountUseCase,
    updateTradingAccountBalanceHubUseCase,
    getTradingPreferencesUseCase,
    tradingAccountModel,
    logger,
  );

  @injectable
  WalletDetailsBloc walletDetailsBloc(
    GetFundingUseCase getFundingUseCase,
    Clock clock,
    EquitiTraderNavigation equitiTraderNavigation,
  ) => WalletDetailsBloc(getFundingUseCase, clock, equitiTraderNavigation);
  @injectable
  ActivityRepository activityRepository(ApiClientBase apiClientBase) =>
      ActivityRepository(apiClientBase);
  @injectable
  TradingActivityUseCase tradingActivityUseCase(
    ActivityRepository activityRepository,
  ) => TradingActivityUseCase(activityRepository);
  @injectable
  DealByIdUseCase dealByIdUseCase(ActivityRepository activityRepository) =>
      DealByIdUseCase(activityRepository: activityRepository);
  @injectable
  TradingBloc tradingBloc(
    DealByIdUseCase dealByIdUseCase,
    TradingActivityUseCase tradingActivityUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => TradingBloc(
    dealByIdUseCase,
    tradingActivityUseCase,
    getSelectedAccountUseCase,
  );

  @injectable
  UpdatePositionsUseCase unsubscribeFromPositionsUseCase(
    PositionRepository positionRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => UpdatePositionsUseCase(positionRepository, getAccountNumberUseCase);

  @injectable
  UpdateTradingAccountBalanceHubUseCase updateTradingAccountBalanceHubUseCase(
    MarginRepository marginRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => UpdateTradingAccountBalanceHubUseCase(
    marginRepository,
    getAccountNumberUseCase,
  );

  @injectable
  UpdateOrderHubUseCase updateOrderHubUseCase(
    OrderRepository orderRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => UpdateOrderHubUseCase(
    orderRepository: orderRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );

  @injectable
  UpdateActiveAlertsHubUseCase updateActiveAlertsHubUseCase(
    GetActiveAlertRepository activeAlertRepository,
    GetAccountNumberUseCase getAccountNumberUseCase,
  ) => UpdateActiveAlertsHubUseCase(
    activeAlertRepository: activeAlertRepository,
    getAccountNumberUseCase: getAccountNumberUseCase,
  );
  @injectable
  HistoricalPerformanceBloc historicalPerformanceBloc(
    GetHistoricalProfitUseCase _getHistoricalProfitUseCase,
    GetHistoricalVolumeUseCase _getHistoricalVolumeUSeCase,
    HistoricalPerformancePreferencesUseCase _performancePreferencesUseCase,
  ) => HistoricalPerformanceBloc(
    _getHistoricalProfitUseCase,
    _getHistoricalVolumeUSeCase,
    _performancePreferencesUseCase,
  );
  @injectable
  GetHistoricalProfitUseCase getHistoricalProfitUseCase(
    HistoricalRepository historicalRepository,
  ) => GetHistoricalProfitUseCase(historicalRepository);
  @injectable
  HistoricalRepository historicalRepository(ApiClientBase apiClientBase) =>
      HistoricalRepository(apiClientBase);
  @injectable
  GetHistoricalVolumeUseCase getHistoricalVolumeUseCase(
    HistoricalRepository historicalRepository,
  ) => GetHistoricalVolumeUseCase(historicalRepository);
  @injectable
  HistoricalPerformancePreferencesUseCase historicalPerformanceUseCase(
    EquitiPreferences preferences,
  ) => HistoricalPerformancePreferencesUseCase(preferences);
  @injectable
  AccountBalanceBloc accountBalanceBloc(
    SubscribeToTradingAccountBalanceUseCase
    subscribeToTradingAccountBalanceUseCase,
    UpdateTradingAccountBalanceHubUseCase updateTradingAccountBalanceHubUseCase,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => AccountBalanceBloc(
    updateTradingAccountBalanceHubUseCase,
    subscribeToTradingAccountBalanceUseCase,
    getSelectedAccountUseCase,
  );

  @injectable
  GetPlatformPasswordUseCase getPlatformPasswordUseCase() =>
      GetPlatformPasswordUseCase();

  @injectable
  ChangeAccountPasswordUseCase changePasswordUseCase(
    AccountRepository accountRepository,
  ) => ChangeAccountPasswordUseCase(accountRepository: accountRepository);

  @injectable
  ChangeAccountPasswordBloc changeAccountPasswordBloc(
    ChangeAccountPasswordUseCase changeAccountPasswordUseCase,
    ClientProfileUseCase clientProfileUseCase,
    LoggerBase logger,
  ) => ChangeAccountPasswordBloc(
    clientProfileUseCase: clientProfileUseCase,
    changeAccountPasswordUseCase: changeAccountPasswordUseCase,
    logger: logger,
  );

  @injectable
  CreateNewWalletBloc createNewWalletBloc(
    CreateWalletUseCase createWalletUseCase,
    GetBrokerCurrenciesUseCase getBrokerCurrenciesUseCase,
  ) => CreateNewWalletBloc(
    createWalletUseCase: createWalletUseCase,
    getBrokerCurrenciesUseCase: getBrokerCurrenciesUseCase,
  );

  @injectable
  CreateWalletUseCase createWalletUseCase(
    CreateWalletRepository createWalletRepository,
    GetBrokerIdUseCase getBrokerIdUseCase,
  ) => CreateWalletUseCase(repository: createWalletRepository);

  @injectable
  CreateWalletRepository createWalletRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClientBase,
  ) => CreateWalletRepository(apiClient: apiClientBase);

  @injectable
  RenameAccountBloc renameAccountBloc(
    UpdateAccountDetailsUseCase updateAccountDetailsUseCase,
  ) => RenameAccountBloc(updateAccountDetailsUseCase);

  @lazySingleton
  AccountAnalytics accountAnalyticsEvent(AnalyticsService analyticsService) =>
      AccountAnalytics(analyticsService);

  @lazySingleton
  TradingAnalytics tradingAnalyticsEvent(
    AnalyticsService analyticsService,
    GetSelectedAccountUseCase getSelectedAccountUseCase,
  ) => TradingAnalytics(analyticsService, getSelectedAccountUseCase);
}
