import 'package:domain/domain.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// Usage: I see {"1013052 - USD - 6999.5"} in the {'Source Account'} dropdown
Future<void> iSeeInTheDropdown(
  WidgetTester tester,
  String valueToSelect,
  String dropdownLabel,
) async {
  final destinationAccountDropdown = find.byWidgetPredicate(
    (widget) =>
        widget is DropdownButtonFormField<Object?> &&
        widget.decoration.labelText == dropdownLabel,
  );

  expect(destinationAccountDropdown, findsOneWidget);

  final dropdownFormField = tester
      .widget<DropdownButtonFormField<TradingAccountModel>>(
        destinationAccountDropdown,
      );

  // Find the selected AccountModel
  final selectedAccountModel = dropdownFormField.initialValue;

  // Format the value as it is displayed in the dropdown
  final displayedValue =
      "${selectedAccountModel?.accountNumber ?? "null"} - ${selectedAccountModel?.homeCurrency ?? "null"} - ${selectedAccountModel?.currentBalance ?? "null"}";

  await tester.pumpAndSettle();

  // Assert the dropdown displays the expected value
  expect(displayedValue, valueToSelect);
}
