import 'package:api_client/api_client.dart';
import 'package:campaigns/campaigns.dart';
import 'package:clock/clock.dart';
import 'package:e_trader/fusion.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:flutter/widgets.dart';
import 'package:injectable/injectable.dart';
import 'package:locale_manager/locale_manager.dart'; // Add this import
import 'package:monitoring/monitoring.dart';
import 'package:preferences/preferences.dart';
import 'package:socket_client/socket_client.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:user_account/user_account.dart';
import '../mocks/auth_service_mock.dart';
import '../mocks/client_profile_use_case_mock.dart';
import '../mocks/equiti_preferences_mock.dart';
import '../mocks/equiti_trader_navigation_mock.dart';
import '../mocks/get_broker_id_use_case_mock.dart';
import '../mocks/locale_manager_mock.dart';
import '../mocks/theme_manager_mock.dart';
import '../mocks/trader_flags_mock.dart'; // Add this import

Future<void> setupDi() async {
  final gh = GetItHelper(diContainer);
  gh.lazySingleton(() => MockApiInterceptor());

  await MonitoringPackageModule().init(gh);

  gh.lazySingleton(
    () =>
        DioBuilder()
            .setBaseUrl('http://equiti-platform.com/')
            .addInterceptor(gh<MockApiInterceptor>())
            .withNativeAdapter()
            .withReporter(),
  );

  gh.lazySingleton<ApiClientBase>(() => DioApiClient(gh<DioBuilder>().build()));

  // Register mobileBffApiClient for SwitchAccountRepository
  gh.lazySingleton<ApiClientBase>(
    () => DioApiClient(gh<DioBuilder>().build()),
    instanceName: 'mobileBffApiClient',
  );

  gh.lazySingleton(() => MockSocketInterceptor(gh()));
  gh.lazySingleton(
    () => SocketClient(
      gh(),
      options: SocketConnectionOptions(
        baseUrl: 'https://equiti-backend-demo-dev.equiti.me.uk/hubs/',
        unsubscribeDebounceDuration:
            Duration.zero, // Disable debouncing in tests
        isTestMode: true, // Disable timers in test mode
      ),
      interceptors: [gh<MockSocketInterceptor>()],
    ),
  );

  gh.lazySingleton<AuthService>(() => AuthServiceMock());

  gh.lazySingleton<ClientProfileUseCase>(() => ClientProfileUseCaseMock());

  gh.lazySingleton<EquitiTraderNavigation>(() => EquitiTraderNavigationMock());

  gh.lazySingleton<EquitiPreferences>(() => EquitiPreferencesMock());

  // Add LocaleManager registration
  gh.lazySingleton<LocaleManager>(() => LocaleManagerMock());

  // Add ThemeManager registration
  gh.lazySingleton<ThemeManager>(() => ThemeManagerMock());
  gh.lazySingleton<TraderFlags>(() => TraderFlagsMock());

  gh.lazySingleton(() => GlobalKey<NavigatorState>());

  gh.lazySingleton(() => Clock());
  await DuploPackageModule().init(gh);
  await EquitiAnalyticsPackageModule().init(gh);
  await CampaignsPackageModule().init(gh);
  await ETraderPackageModule().init(gh);

  // Setup GetBrokerIdUseCase mock
  setupGetBrokerIdUseCaseMock();
}
