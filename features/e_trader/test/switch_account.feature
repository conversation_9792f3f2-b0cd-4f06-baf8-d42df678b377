import 'package:equiti_test/equiti_test.dart';
import 'scenarios/switch_account_success_scenario.dart';
import 'scenarios/switch_account_failure_scenario.dart';
import 'scenarios/switch_account_empty_list_scenario.dart';
import 'package:e_trader/src/presentation/switch_account/switch_account_screen.dart';
import 'scenarios/market_success_scenario.dart';


Feature: Switch Account Screen

@testMethodName: testGoldens
Scenario Outline: User views switch account screen
  Given The {SwitchAccountScreen()} app is rendered <scenario>
  And I wait
  Then screenshot verified <golden_file_name> with custom pump
  Examples:
  | scenario                                     | golden_file_name             |
  | scenarios: [switchAccountSuccessScenario]    | 'switch_account_success'     |
  | scenarios: [switchAccountFailureScenario]    | 'switch_account_failure'     |
  | scenarios: [switchAccountEmptyListScenario]  | 'switch_account_empty_list'  |
