import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:socket_client/socket_client.dart';

void switchAccountEmptyListScenario() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'middlewareapi/Broker/broker-settings': [
        MockResponse(
          bodyFilePath: 'resources/mocks/change_leverage/success.json',
        ),
      ],
      'api/client/trading-accounts': [
        MockResponse(
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/success_empty_account_list.json',
        ),
      ],
      'api/client/wallet-accounts': [
        MockResponse(
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/success_empty_account_list.json',
        ),
      ],
      'api/v1/client/campaigns': [
        MockResponse(bodyFilePath: 'resources/mocks/campaigns/empty.json'),
      ],
    });
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/accountBalanceHub",
    eventType: TradingSocketEvent.accountBalance.register,
    responses: [""],
  );
}
