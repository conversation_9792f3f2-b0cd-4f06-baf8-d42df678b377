import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:socket_client/socket_client.dart';

void switchAccountSuccessScenario() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'middlewareapi/Broker/broker-settings': [
        MockResponse(
          bodyFilePath: 'resources/mocks/change_leverage/success.json',
        ),
      ],
      'api/client/trading-accounts': [
        MockResponse(
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/success.json',
        ),
      ],
      'api/client/wallet-accounts': [
        MockResponse(
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/success.json',
        ),
      ],
      'api/v1/client/campaigns': [
        MockResponse(bodyFilePath: 'resources/mocks/campaigns/success.json'),
      ],
    });
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/accountBalanceHub",
    eventType: TradingSocketEvent.accountBalance.register,
    responses: [
      {
        "accountNumber": "1008576",
        "balance": 10000.50,
        "equity": 10500.00,
        "margin": 12000.00,
        "freeMargin": -1500.00,
        "profit": -500.00,
        "grossProfit": -300.00,
        "credit": 500.00,
        "marginLevel": 87.50,
        "productsMarginAllocation": [
          {
            "category": "Forex",
            "marginAllocation": 8000.00,
            "marginAllocationPercentage": 66.67,
          },
          {
            "category": "Commodities",
            "marginAllocation": 4000.00,
            "marginAllocationPercentage": 33.33,
          },
        ],
      },
    ],
  );
}
