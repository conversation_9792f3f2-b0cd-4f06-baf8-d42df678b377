import 'package:api_client/api_client.dart';
import 'package:e_trader/src/data/api/trading_socket_event.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:socket_client/socket_client.dart';

void switchAccountFailureScenario() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      'api/client/trading-accounts': [
        MockResponse(
          code: 400,
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/failure.json',
        ),
      ],
      'api/client/wallet-accounts': [
        MockResponse(
          code: 400,
          bodyFilePath:
              'resources/mocks/switch_account/get_live_accounts/failure.json',
        ),
      ],
      'middlewareapi/Broker/broker-settings': [
        MockResponse(
          code: 400,
          bodyFilePath: 'resources/mocks/change_leverage/failure.json',
        ),
      ],
      'api/v1/client/campaigns': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/campaigns/empty.json',
        ),
      ],
    });
  diContainer<MockSocketInterceptor>().addMockResponse(
    url: "https://equiti-backend-demo-dev.equiti.me.uk/hubs/accountBalanceHub",
    eventType: TradingSocketEvent.accountBalance.register,
    responses: [""],
  );
}
