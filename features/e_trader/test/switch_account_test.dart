// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: unused_import, directives_ordering

import 'package:equiti_test/equiti_test.dart';
import 'scenarios/switch_account_success_scenario.dart';
import 'scenarios/switch_account_failure_scenario.dart';
import 'scenarios/switch_account_empty_list_scenario.dart';
import 'package:e_trader/src/presentation/switch_account/switch_account_screen.dart';
import 'scenarios/market_success_scenario.dart';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import './bdd_hooks/hooks.dart';
import './step/the_app_is_rendered.dart';
import 'package:bdd_widget_test/step/i_wait.dart';
import './step/screenshot_verified_with_custom_pump.dart';

void main() {
  setUpAll(() async {
    await Hooks.beforeAll();
  });
  tearDownAll(() async {
    await Hooks.afterAll();
  });

  group('''Switch Account Screen''', () {
    Future<void> beforeEach(String title, [List<String>? tags]) async {
      await Hooks.beforeEach(title, tags);
    }

    Future<void> afterEach(
      String title,
      bool success, [
      List<String>? tags,
    ]) async {
      await Hooks.afterEach(title, success, tags);
    }

    testGoldens(
      '''Outline: User views switch account screen (scenarios: [switchAccountSuccessScenario], 'switch_account_success')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountSuccessScenario], 'switch_account_success')''',
          );
          await theAppIsRendered(
            tester,
            SwitchAccountScreen(),
            scenarios: [switchAccountSuccessScenario],
          );
          await iWait(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'switch_account_success',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountSuccessScenario], 'switch_account_success')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views switch account screen (scenarios: [switchAccountFailureScenario], 'switch_account_failure')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountFailureScenario], 'switch_account_failure')''',
          );
          await theAppIsRendered(
            tester,
            SwitchAccountScreen(),
            scenarios: [switchAccountFailureScenario],
          );
          await iWait(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'switch_account_failure',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountFailureScenario], 'switch_account_failure')''',
            success,
          );
        }
      },
    );
    testGoldens(
      '''Outline: User views switch account screen (scenarios: [switchAccountEmptyListScenario], 'switch_account_empty_list')''',
      (tester) async {
        var success = true;
        try {
          await beforeEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountEmptyListScenario], 'switch_account_empty_list')''',
          );
          await theAppIsRendered(
            tester,
            SwitchAccountScreen(),
            scenarios: [switchAccountEmptyListScenario],
          );
          await iWait(tester);
          await screenshotVerifiedWithCustomPump(
            tester,
            'switch_account_empty_list',
          );
        } on TestFailure {
          success = false;
          rethrow;
        } finally {
          await afterEach(
            '''Outline: User views switch account screen (scenarios: [switchAccountEmptyListScenario], 'switch_account_empty_list')''',
            success,
          );
        }
      },
    );
  });
}
