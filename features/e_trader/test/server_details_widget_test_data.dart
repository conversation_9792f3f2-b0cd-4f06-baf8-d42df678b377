import 'package:domain/domain.dart';
import 'package:e_trader/src/presentation/switch_account/widgets/server_details_widget.dart';
// Mock trading accounts for different server details scenarios

final mockMt4PositiveProfitAccount = TradingAccountModel(
  recordId: 'mt4-positive-profit-id',
  accountNumber: '123456',
  accountIdLong: 123456,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'USD',
  accountType: AccountType.trading,
  name: 'MT4 Trader',
  platformType: PlatformType.mt4,
  primaryEmail: '<EMAIL>',
  leverage: 500,
  currentBalance: 10000.0,
  margin: 250.0,
  equity: 10750.0,
  profit: 750.0,
  server: 'mt4-live-01',
  isDemo: false,
);

final mockMt5NegativeProfitAccount = TradingAccountModel(
  recordId: 'mt5-negative-profit-id',
  accountNumber: '789012',
  accountIdLong: 789012,
  platformAccountType: PlatformAccountType.premiere,
  platformTypeName: 'Premiere',
  homeCurrency: 'EUR',
  accountType: AccountType.trading,
  name: 'MT5 Professional',
  platformType: PlatformType.mt5,
  primaryEmail: '<EMAIL>',
  leverage: 200,
  currentBalance: 25000.0,
  margin: 1200.0,
  equity: 24250.0,
  profit: -750.0,
  server: 'mt5-live-02',
  isDemo: false,
);

final mockDulcimerZeroProfitAccount = TradingAccountModel(
  recordId: 'dulcimer-zero-profit-id',
  accountNumber: '345678',
  accountIdLong: 345678,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'CHF',
  accountType: AccountType.trading,
  name: 'Dulcimer User',
  platformType: PlatformType.equitiTrader,
  primaryEmail: '<EMAIL>',
  leverage: 100,
  currentBalance: 50000.0,
  margin: 0.0,
  equity: 50000.0,
  profit: 0.0,
  server: 'dulcimer-live-01',
  isDemo: false,
);

final mockMt4HighEquityAccount = TradingAccountModel(
  recordId: 'mt4-high-equity-id',
  accountNumber: '901234',
  accountIdLong: 901234,
  platformAccountType: PlatformAccountType.premiere,
  platformTypeName: 'Premiere',
  homeCurrency: 'USD',
  accountType: AccountType.trading,
  name: 'High Equity Trader',
  platformType: PlatformType.mt4,
  primaryEmail: '<EMAIL>',
  leverage: 400,
  currentBalance: 100000.0,
  margin: 5000.0,
  equity: 105500.0,
  profit: 5500.0,
  server: 'mt4-premium-01',
  isDemo: false,
);

final mockMt5DemoAccount = TradingAccountModel(
  recordId: 'mt5-demo-id',
  accountNumber: '567890',
  accountIdLong: 567890,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'GBP',
  accountType: AccountType.trading,
  name: 'Demo MT5 User',
  platformType: PlatformType.mt5,
  primaryEmail: '<EMAIL>',
  leverage: 50,
  currentBalance: 75000.0,
  margin: 1200.0,
  equity: 75800.0,
  profit: 800.0,
  server: 'mt5-demo-01',
  isDemo: true,
);

final mockDulcimerNoServerAccount = TradingAccountModel(
  recordId: 'dulcimer-no-server-id',
  accountNumber: '111222',
  accountIdLong: 111222,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'AUD',
  accountType: AccountType.trading,
  name: 'No Server User',
  platformType: PlatformType.equitiTrader,
  primaryEmail: '<EMAIL>',
  leverage: 300,
  currentBalance: 15000.0,
  margin: 500.0,
  equity: 14800.0,
  profit: -200.0,
  server: null, // No server information
  isDemo: false,
);

// Widget instances for testing
final mt4PositiveProfitServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockMt4PositiveProfitAccount,
);

final mt5NegativeProfitServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockMt5NegativeProfitAccount,
);

final dulcimerZeroProfitServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockDulcimerZeroProfitAccount,
);

final mt4HighEquityServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockMt4HighEquityAccount,
);

final mt5DemoServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockMt5DemoAccount,
);

final dulcimerNoServerServerDetails = ServerDetailsWidget(
  tradingAccountModel: mockDulcimerNoServerAccount,
);
