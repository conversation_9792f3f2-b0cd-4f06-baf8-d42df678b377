// ignore_for_file: prefer-match-file-name

import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/domain/model/trading_environment.dart';
import 'package:flutter/material.dart';

// Mock trading accounts for different scenarios
final mockLiveDulcimerAccount = TradingAccountModel(
  recordId: '3f8858d4-e605-09c7-94b2-6459f96e60dc',
  accountNumber: '********',
  accountIdLong: ********,
  platformAccountType: PlatformAccountType.premiere,
  platformTypeName: 'Premiere',
  homeCurrency: 'USD',
  accountType: AccountType.trading,
  name: 'Mr Mobile Team Testing',
  platformType: PlatformType.equitiTrader,
  primaryEmail: '<EMAIL>',
  leverage: 400,
  currentBalance: 69264.15,
  margin: 130.35,
  equity: 69794.85,
  profit: 530.70,
  server: 'dulcimer-demo-01',
  isDemo: false,
);

final mockLiveMt4Account = TradingAccountModel(
  recordId: '2c5eabe4-9e03-9cab-53e8-6644901c7aa9',
  accountNumber: '123456',
  accountIdLong: 123456,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'EUR',
  accountType: AccountType.trading,
  name: 'Steve Jobs',
  platformType: PlatformType.mt4,
  primaryEmail: '<EMAIL>',
  leverage: 500,
  currentBalance: 10000.0,
  margin: 250.0,
  equity: 10500.0,
  profit: 500.0,
  server: 'mt4-live-01',
  isDemo: false,
);

final mockDemoDulcimerAccount = TradingAccountModel(
  recordId: 'demo-dulcimer-id',
  accountNumber: '********',
  accountIdLong: ********,
  platformAccountType: PlatformAccountType.standard,
  platformTypeName: 'Standard',
  homeCurrency: 'USD',
  accountType: AccountType.trading,
  name: 'Demo User',
  platformType: PlatformType.equitiTrader,
  primaryEmail: '<EMAIL>',
  leverage: 200,
  currentBalance: 50000.0,
  margin: 0.0,
  equity: 50000.0,
  profit: 0.0,
  server: 'dulcimer-demo-01',
  isDemo: true,
);

final mockDemoMt4Account = TradingAccountModel(
  recordId: 'demo-mt4-id',
  accountNumber: '789012',
  accountIdLong: 789012,
  platformAccountType: PlatformAccountType.premiere,
  homeCurrency: 'GBP',
  accountType: AccountType.trading,
  name: 'Demo MT4 User',
  platformType: PlatformType.mt4,
  primaryEmail: '<EMAIL>',
  leverage: 100,
  currentBalance: 25000.0,
  margin: 500.0,
  equity: 25200.0,
  profit: 200.0,
  server: 'mt4-demo-01',
  isDemo: true,
);

final mockLiveMt5Account = TradingAccountModel(
  recordId: '4f9959e5-f706-0ac8-95c3-7560a97f61ed',
  accountNumber: '345678',
  accountIdLong: 345678,
  platformAccountType: PlatformAccountType.standard,
  homeCurrency: 'JPY',
  accountType: AccountType.trading,
  name: 'John Smith',
  platformType: PlatformType.mt5,
  primaryEmail: '<EMAIL>',
  leverage: 300,
  currentBalance: 150000.0,
  margin: 750.0,
  equity: 151000.0,
  profit: 1000.0,
  server: 'mt5-live-01',
  isDemo: false,
);

final mockDemoMt5Account = TradingAccountModel(
  recordId: 'demo-mt5-id',
  accountNumber: '567890',
  accountIdLong: 567890,
  platformAccountType: PlatformAccountType.standard,
  homeCurrency: 'CHF',
  accountType: AccountType.trading,
  name: 'Demo MT5 User',
  platformType: PlatformType.mt5,
  primaryEmail: '<EMAIL>',
  leverage: 50,
  currentBalance: 75000.0,
  margin: 1200.0,
  equity: 75800.0,
  profit: 800.0,
  server: 'mt5-demo-01',
  isDemo: true,
);

// Test wrapper widget that recreates the account settings content
class AccountSettingsTestWidget extends StatelessWidget {
  const AccountSettingsTestWidget({
    super.key,
    required this.account,
    required this.tradingEnvironment,
  });

  final TradingAccountModel account;
  final TradingEnvironment tradingEnvironment;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SingleChildScrollView(child: _buildAccountSettingsContent()),
    );
  }

  Widget _buildAccountSettingsContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (tradingEnvironment == TradingEnvironment.live) ...[
            const SizedBox(height: 24),
            _AccountSummaryWidget(account),
            const SizedBox(height: 16),
            DuploFundingButtons(
              onDepositPressed: () => debugPrint('Deposit pressed'),
              onWithdrawPressed: () => debugPrint('Withdraw pressed'),
              onTransferPressed: () => debugPrint('Transfer pressed'),
            ),
            const SizedBox(height: 12),
          ],
          _ChangeMaxLeverageWidget(account),
          if (account.platformType != PlatformType.equitiTrader)
            _ServerDetailsWidget(account),
          if (tradingEnvironment == TradingEnvironment.demo)
            _ResetBalanceWidget(account),
          _AccountInformationWidget(account),
          _ChangeAccountPasswordWidget(account),
        ],
      ),
    );
  }
}

// Widget classes copied from the original file for testing
class _ChangeMaxLeverageWidget extends StatelessWidget {
  const _ChangeMaxLeverageWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: 'Change max leverage',
    onPressed: () {
      debugPrint("Change leverage");
    },
  );
}

class _ServerDetailsWidget extends StatelessWidget {
  const _ServerDetailsWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: 'MetaTrader server details',
    onPressed: () {
      debugPrint("Server details");
    },
  );
}

class _AccountSummaryWidget extends StatelessWidget {
  const _AccountSummaryWidget(this.accountModel);

  final TradingAccountModel accountModel;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final duploTextStyles = context.duploTextStyles;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DuploText(
          text: accountModel.name ?? accountModel.accountNumber,
          style: duploTextStyles.textMd,
          color: theme.text.textPrimary,
          fontWeight: DuploFontWeight.semiBold,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        DuploText(
          text: "${accountModel.platformTypeName}",
          style: duploTextStyles.textXs,
          color: theme.text.textTertiary,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}

class _AccountInformationWidget extends StatelessWidget {
  const _AccountInformationWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) => TextChevronWidget(
    title: 'Account information',
    onPressed: () {
      debugPrint("Account information");
    },
  );
}

class _ChangeAccountPasswordWidget extends StatelessWidget {
  const _ChangeAccountPasswordWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}

class _ResetBalanceWidget extends StatelessWidget {
  const _ResetBalanceWidget(this.account);

  final TradingAccountModel account;

  @override
  Widget build(BuildContext context) {
    return TextChevronWidget(
      title: 'Reset balance',
      onPressed: () {
        debugPrint("Reset balance");
      },
    );
  }
}

// Test widget instances for different scenarios
final liveDulcimerAccountSettings = AccountSettingsTestWidget(
  account: mockLiveDulcimerAccount,
  tradingEnvironment: TradingEnvironment.live,
);

final liveMt4AccountSettings = AccountSettingsTestWidget(
  account: mockLiveMt4Account,
  tradingEnvironment: TradingEnvironment.live,
);

final liveMt5AccountSettings = AccountSettingsTestWidget(
  account: mockLiveMt5Account,
  tradingEnvironment: TradingEnvironment.live,
);

final demoDulcimerAccountSettings = AccountSettingsTestWidget(
  account: mockDemoDulcimerAccount,
  tradingEnvironment: TradingEnvironment.demo,
);

final demoMt4AccountSettings = AccountSettingsTestWidget(
  account: mockDemoMt4Account,
  tradingEnvironment: TradingEnvironment.demo,
);

final demoMt5AccountSettings = AccountSettingsTestWidget(
  account: mockDemoMt5Account,
  tradingEnvironment: TradingEnvironment.demo,
);
