name: payment
publish_to: none
description: "A new Flutter package project."
version: 0.0.1

environment:
  sdk: 3.8.1
  flutter: 3.32.6

dependencies:
  flutter:
    sdk: flutter
  prelude:
    path: ../../utilities/prelude
  duplo:
    path: ../../core/duplo
  domain:
    path: ../../core/domain
  campaigns:
    path: ../../core/campaigns
  api_client:
    path: ../../utilities/api_client
  equiti_router:
    path: ../../utilities/equiti_router
  equiti_localization:
    path: ../../core/equiti_localization
  user_account:
    path: ../../core/user_account
  equiti_analytics:
    path: ../../core/equiti_analytics
  equiti_auth:
    path: ../../core/equiti_auth
  feature_flags:
    path: ../../core/feature_flags
  flutter_bloc: 9.1.1
  dio: 5.8.0+1
  get_it: 8.0.3
  injectable: 2.5.0
  flutter_svg: 2.0.10+1
  flutter_inappwebview: 6.1.5
  file_picker: 10.2.0
  freezed_annotation: 3.0.0
  json_annotation: 4.9.0
  vector_graphics: 1.1.19
  path_provider: 2.1.5
  open_file: 3.5.10
  lean_sdk_flutter: 3.0.11
  url_launcher: 6.3.2
  intl: 0.20.2



dev_dependencies:
  flutter_test:
    sdk: flutter
  dependency_validator: 5.0.2
  equiti_lint:
    path: ../../utilities/equiti_lint
  bdd_steps:
    path: ../../utilities/bdd_steps
  equiti_test:
    path: ../../utilities/equiti_test
  dart_code_metrics_presets: 2.22.0
  flutter_gen_runner: 5.11.0
  bdd_widget_test: 1.8.1
  freezed: 3.0.6
  injectable_generator: 2.7.0
  json_serializable: 6.9.5
  mocktail: 1.0.4
  build_verify: 3.1.1
  build_runner: 2.5.4
  vector_graphics_compiler: 1.1.17

dependency_overrides:
  analyzer: ^6.3.0
  dart_style: ^3.0.1

flutter_gen:
  output: lib/src/assets/
  assets:
    exclude:
      - resources/mocks/**/*
      - assets/images/.DS_Store
      - assets/images/**/.DS_Store
    outputs:
      package_parameter_enabled: true
  integrations:
    flutter_svg: true

flutter:
  generate: true
  uses-material-design: true

  assets:
  - path: assets/images/
    transformers:
      - package: vector_graphics_compiler
  - resources/mocks/payment_options/
  - resources/mocks/conversion_rate/
  - resources/mocks/payment_list_of_accounts/
  - resources/mocks/status/
  - resources/mocks/new_transfer_funds/
  - resources/mocks/bank_transfer/
  - resources/mocks/client_profile/
  - resources/mocks/check_withdrawal_allowed/
  - resources/mocks/withdraw_fees/
  - resources/mocks/get_countries/
  - resources/mocks/withdraw_cards/
  - resources/mocks/lean_account/
  - resources/mocks/get_transfer_type/
  - resources/mocks/campaigns/
  - resources/mocks/campaign_info/
