{"success": true, "data": {"accounts": [{"accountId": "1a6d0541-f08d-a2c2-d635-673c83add063", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "AED", "platformAccountNumber": "1090388", "clientId": "6b350c83-81a6-e47d-e021-673c83d7ca52", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SE\\USD\\FM_SP_ZZZZ_MU000_C000_0_L_BXC", "brokerId": "42bfd663-d215-43f7-a5c1-8d286a3cb8c7", "platformAccountType": "Standard", "name": "<PERSON>", "primaryEmail": "<EMAIL>", "leverage": 400, "serverCode": "mt5-live-01", "platformType": "MT5", "leadSource": "", "balance": 1001, "currentBalance": 1001, "actualBalance": 1001, "margin": 0, "equity": 1001, "profit": 0, "grossProfit": 0, "dateCreated": "2024-11-19T12:25:22", "isDemo": false, "classification": "DirectClient", "accountIdLong": 1090388, "credit": 0, "accountCurrencyUsdPair": "", "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "43d00ffe-0439-7da5-68d2-673c835f0b7b", "accountStatus": "Active", "accountType": "LandingWallet", "platformTypeName": "Standard", "accountCurrency": "USD", "platformAccountNumber": "*********-001", "clientId": "6b350c83-81a6-e47d-e021-673c83d7ca52", "accountGroup": "", "brokerId": "42bfd663-d215-43f7-a5c1-8d286a3cb8c7", "platformAccountType": "Standard", "name": "<PERSON>", "primaryEmail": "<EMAIL>", "leverage": 0, "serverCode": "", "platformType": "<PERSON><PERSON><PERSON>", "leadSource": "", "balance": 0, "currentBalance": 0, "actualBalance": 1001, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2024-11-19T12:25:22", "isDemo": false, "classification": "DirectClient", "accountIdLong": 0, "credit": 0, "accountCurrencyUsdPair": "", "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "ce5e6c0c-3b3f-8991-ec3d-67923f23f14c", "accountStatus": "Active", "accountType": "Trading", "accountCurrency": "USD", "platformAccountNumber": "1090447", "clientId": "6b350c83-81a6-e47d-e021-673c83d7ca52", "accountGroup": "EQJO\\Retail\\EX\\Direct\\SF\\USD\\FM_SF_ZZZZ_MU005_C000_0_L_BXC", "brokerId": "42bfd663-d215-43f7-a5c1-8d286a3cb8c7", "platformAccountType": "Crypto", "name": "<PERSON>", "primaryEmail": "<EMAIL>", "leverage": 1, "serverCode": "mt5-live-01", "platformType": "MT5", "leadSource": "", "currentBalance": 0, "actualBalance": 1001, "balance": 0, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2024-11-19T12:25:22", "isDemo": false, "classification": "DirectClient", "accountIdLong": 1090447, "credit": 0, "accountCurrencyUsdPair": "", "nickName": "<PERSON><PERSON><PERSON>"}, {"accountId": "3c091927-0870-15a1-6b41-67f67e2601f3", "accountStatus": "Active", "accountType": "LandingWallet", "platformTypeName": "Standard", "accountCurrency": "KWD", "platformAccountNumber": "*********-002", "clientId": "6b350c83-81a6-e47d-e021-673c83d7ca52", "accountGroup": "", "brokerId": "42bfd663-d215-43f7-a5c1-8d286a3cb8c7", "platformAccountType": "Standard", "name": "<PERSON>", "primaryEmail": "<EMAIL>", "leverage": 1, "serverCode": "", "platformType": "<PERSON><PERSON><PERSON>", "leadSource": "", "balance": 0, "currentBalance": 0, "actualBalance": 1001, "margin": 0, "equity": 0, "profit": 0, "grossProfit": 0, "dateCreated": "2024-11-19T12:25:22", "isDemo": false, "classification": "DirectClient", "accountIdLong": 0, "credit": 0, "accountCurrencyUsdPair": "", "nickName": "<PERSON><PERSON><PERSON>"}]}}