// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/add_ic.svg
  SvgGenImage get addIc => const SvgGenImage.vec('assets/images/add_ic.svg');

  /// File path: assets/images/alert-triangle.svg
  SvgGenImage get alertTriangle =>
      const SvgGenImage.vec('assets/images/alert-triangle.svg');

  /// File path: assets/images/arrow_right_ic.svg
  SvgGenImage get arrowRightIc =>
      const SvgGenImage.vec('assets/images/arrow_right_ic.svg');

  /// File path: assets/images/ban.svg
  SvgGenImage get ban => const SvgGenImage.vec('assets/images/ban.svg');

  /// File path: assets/images/bank.svg
  SvgGenImage get bank => const SvgGenImage.vec('assets/images/bank.svg');

  /// File path: assets/images/bank_account_deleted_successfuly_ic.svg
  SvgGenImage get bankAccountDeletedSuccessfulyIc => const SvgGenImage.vec(
    'assets/images/bank_account_deleted_successfuly_ic.svg',
  );

  /// File path: assets/images/bank_error.svg
  SvgGenImage get bankError =>
      const SvgGenImage.vec('assets/images/bank_error.svg');

  /// File path: assets/images/bank_placeholder.svg
  SvgGenImage get bankPlaceholder =>
      const SvgGenImage.vec('assets/images/bank_placeholder.svg');

  /// File path: assets/images/bouns_alert.svg
  SvgGenImage get bounsAlert =>
      const SvgGenImage.vec('assets/images/bouns_alert.svg');

  /// File path: assets/images/campaign_cancelled.svg
  SvgGenImage get campaignCancelled =>
      const SvgGenImage.vec('assets/images/campaign_cancelled.svg');

  /// File path: assets/images/check_round.svg
  SvgGenImage get checkRound =>
      const SvgGenImage.vec('assets/images/check_round.svg');

  /// File path: assets/images/chevron_down.svg
  SvgGenImage get chevronDown =>
      const SvgGenImage.vec('assets/images/chevron_down.svg');

  /// File path: assets/images/chevron_right.svg
  SvgGenImage get chevronRight =>
      const SvgGenImage.vec('assets/images/chevron_right.svg');

  /// File path: assets/images/conversion_icon.svg
  SvgGenImage get conversionIcon =>
      const SvgGenImage.vec('assets/images/conversion_icon.svg');

  /// File path: assets/images/delete_account_ic.svg
  SvgGenImage get deleteAccountIc =>
      const SvgGenImage.vec('assets/images/delete_account_ic.svg');

  /// File path: assets/images/delete_ic.svg
  SvgGenImage get deleteIc =>
      const SvgGenImage.vec('assets/images/delete_ic.svg');

  /// File path: assets/images/delete_mode_ic.svg
  SvgGenImage get deleteModeIc =>
      const SvgGenImage.vec('assets/images/delete_mode_ic.svg');

  /// File path: assets/images/deposit_declined.svg
  SvgGenImage get depositDeclined =>
      const SvgGenImage.vec('assets/images/deposit_declined.svg');

  /// File path: assets/images/deposit_error.svg
  SvgGenImage get depositError =>
      const SvgGenImage.vec('assets/images/deposit_error.svg');

  /// File path: assets/images/deposit_submitted.svg
  SvgGenImage get depositSubmitted =>
      const SvgGenImage.vec('assets/images/deposit_submitted.svg');

  /// File path: assets/images/deposit_success.svg
  SvgGenImage get depositSuccess =>
      const SvgGenImage.vec('assets/images/deposit_success.svg');

  /// File path: assets/images/edit_icon.svg
  SvgGenImage get editIcon =>
      const SvgGenImage.vec('assets/images/edit_icon.svg');

  /// File path: assets/images/empty_account_ic.svg
  SvgGenImage get emptyAccountIc =>
      const SvgGenImage.vec('assets/images/empty_account_ic.svg');

  /// File path: assets/images/file_download.svg
  SvgGenImage get fileDownload =>
      const SvgGenImage.vec('assets/images/file_download.svg');

  /// File path: assets/images/info-circle.svg
  SvgGenImage get infoCircle =>
      const SvgGenImage.vec('assets/images/info-circle.svg');

  /// File path: assets/images/more_icon.svg
  SvgGenImage get moreIcon =>
      const SvgGenImage.vec('assets/images/more_icon.svg');

  /// File path: assets/images/neteller.svg
  SvgGenImage get neteller =>
      const SvgGenImage.vec('assets/images/neteller.svg');

  /// File path: assets/images/no_accounts_found.svg
  SvgGenImage get noAccountsFound =>
      const SvgGenImage.vec('assets/images/no_accounts_found.svg');

  /// File path: assets/images/no_card.svg
  SvgGenImage get noCard => const SvgGenImage.vec('assets/images/no_card.svg');

  /// File path: assets/images/no_wallets_found.svg
  SvgGenImage get noWalletsFound =>
      const SvgGenImage.vec('assets/images/no_wallets_found.svg');

  /// File path: assets/images/refresh_ccw.svg
  SvgGenImage get refreshCcw =>
      const SvgGenImage.vec('assets/images/refresh_ccw.svg');

  /// File path: assets/images/skrill.svg
  SvgGenImage get skrill => const SvgGenImage.vec('assets/images/skrill.svg');

  /// File path: assets/images/still_processing_ic.svg
  SvgGenImage get stillProcessingIc =>
      const SvgGenImage.vec('assets/images/still_processing_ic.svg');

  /// File path: assets/images/transfer_funds_failure.svg
  SvgGenImage get transferFundsFailure =>
      const SvgGenImage.vec('assets/images/transfer_funds_failure.svg');

  /// File path: assets/images/transfer_pending_updated.svg
  SvgGenImage get transferPendingUpdated =>
      const SvgGenImage.vec('assets/images/transfer_pending_updated.svg');

  /// File path: assets/images/transfer_rejected.svg
  SvgGenImage get transferRejected =>
      const SvgGenImage.vec('assets/images/transfer_rejected.svg');

  /// File path: assets/images/trasnfer_funds_success.svg
  SvgGenImage get trasnferFundsSuccess =>
      const SvgGenImage.vec('assets/images/trasnfer_funds_success.svg');

  /// File path: assets/images/wallet.svg
  SvgGenImage get wallet => const SvgGenImage.vec('assets/images/wallet.svg');

  /// File path: assets/images/withdrawal_error.svg
  SvgGenImage get withdrawalError =>
      const SvgGenImage.vec('assets/images/withdrawal_error.svg');

  /// File path: assets/images/withdrawal_error_status.svg
  SvgGenImage get withdrawalErrorStatus =>
      const SvgGenImage.vec('assets/images/withdrawal_error_status.svg');

  /// File path: assets/images/withdrawal_request_submitted.svg
  SvgGenImage get withdrawalRequestSubmitted =>
      const SvgGenImage.vec('assets/images/withdrawal_request_submitted.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    addIc,
    alertTriangle,
    arrowRightIc,
    ban,
    bank,
    bankAccountDeletedSuccessfulyIc,
    bankError,
    bankPlaceholder,
    bounsAlert,
    campaignCancelled,
    checkRound,
    chevronDown,
    chevronRight,
    conversionIcon,
    deleteAccountIc,
    deleteIc,
    deleteModeIc,
    depositDeclined,
    depositError,
    depositSubmitted,
    depositSuccess,
    editIcon,
    emptyAccountIc,
    fileDownload,
    infoCircle,
    moreIcon,
    neteller,
    noAccountsFound,
    noCard,
    noWalletsFound,
    refreshCcw,
    skrill,
    stillProcessingIc,
    transferFundsFailure,
    transferPendingUpdated,
    transferRejected,
    trasnferFundsSuccess,
    wallet,
    withdrawalError,
    withdrawalErrorStatus,
    withdrawalRequestSubmitted,
  ];
}

class Assets {
  const Assets._();

  static const String package = 'payment';

  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'payment';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/payment/$_assetName';
}
