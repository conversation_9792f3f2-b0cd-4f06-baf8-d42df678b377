import 'package:payment/src/data/deposit_request_model/deposit_request_model.dart';
import 'package:payment/src/data/deposit_response/deposit_response.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/repository/deposit_repository.dart';
import 'package:prelude/prelude.dart';

class GetDepositDetailsUsecase {
  final DepositRepository repository;
  const GetDepositDetailsUsecase({required this.repository});
  TaskEither<Exception, DepositResponse> call({
    required String tradingAccountId,
    required String clientId,
    required String accountCurrency,
    required String selectedCurrency,
    required String conversionRateString,
    required double accountCurrencyAmount,
    required double selectedCurrencyAmount,
    required double conversionRateToAccountCurrency,
    required DepositMop mop,
    required bool isDarkTheme,
    required String campaignId,
    required String? leanAccountId,
  }) {
    final data = DepositRequestModel(
      accountInfo: AccountInfo(
        tradingAccountId: tradingAccountId,
        clientId: clientId,
        accountCurrency: accountCurrency,
      ),
      amountDetails: AmountDetails(
        selectedCurrency: selectedCurrency,
        conversionRateString: conversionRateString,
        selectedCurrencyAmount: selectedCurrencyAmount,
        accountCurrencyAmount: accountCurrencyAmount,
        conversionRateToAccountCurrency: conversionRateToAccountCurrency,
      ),
      campaigns: Campaigns(id: campaignId, optedIn: campaignId.isNotEmpty),
      mop: mop,
      language: "en",
      view: "trader",
      metadata: Metadata(
        is_dark_theme: isDarkTheme,
        leanAccountId: leanAccountId,
      ),
    );
    return repository.getDepositDetails(data: data);
  }
}
