import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/withdraw_analytics_event.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/lean_destination_account_response/lean_destination_account_response.dart';
import 'package:payment/src/data/payment_method/withdraw_payment_methods_model/withdraw_payment_methods_model.dart';
import 'package:payment/src/data/withdraw_card_model/withdraw_card_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/domain/data/withdrawal_mop.dart';

import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:payment/src/presentation/widgets/bouns_credit_note_bottom_sheet.dart';
import 'package:payment/src/presentation/widgets/withdraw_fees_display/withdraw_fees_display.dart';
import 'package:prelude/prelude.dart';
import 'package:payment/src/assets/assets.gen.dart' as paymentAssets;

import 'bloc/withdraw_accounts_and_amount_bloc.dart';

class WithdrawAccountsAndAmountScreen extends StatefulWidget {
  const WithdrawAccountsAndAmountScreen({
    super.key,
    required this.paymentMethod,
    this.account,
    this.selectedCard,
    required this.popUntilRoute,
    this.selectedLeanAccount,
  });
  final WithdrawalPaymentMethod paymentMethod;
  // optional field for additional payment methods
  final String? account;
  final WithdrawCard? selectedCard;
  final String popUntilRoute;
  final LeanDestinationAccount? selectedLeanAccount;

  @override
  State<WithdrawAccountsAndAmountScreen> createState() =>
      _WithdrawAccountsAndAmountScreenState();
}

class _WithdrawAccountsAndAmountScreenState
    extends State<WithdrawAccountsAndAmountScreen> {
  bool _isCurrentTabEmpty =
      true; // Initialize as empty to hide button by default

  void _handleContinueButtonTap(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final bloc = context.read<WithdrawAccountsAndAmountBloc>();
    final state = bloc.state;
    (state.selectedAccount?.credit ?? 0) > 0
        ? showBounsCreditNoteBottomSheet(
          context,
          localization
              .payments_campaign_bonus_credit_note_description_withdraw_funds,
          () {
            onContinueButtonTap(context);
          },
        )
        : onContinueButtonTap(context);
  }

  void onContinueButtonTap(BuildContext context) {
    final bloc = context.read<WithdrawAccountsAndAmountBloc>();
    final state = bloc.state;
    diContainer<WithdrawAnalyticsEvent>().withdrawInitiated(
      amount: state.accountCurrencyAmount,
      convertedAmount: state.selectedCurrencyAmount,
      accountCurrency: state.selectedAccount?.homeCurrency ?? '',
      selectedCurrency: state.selectedCurrency,
      conversionRate: state.conversionRateString,
    );
    bloc.add(
      WithdrawAccountsAndAmountEvent.handleContinueAction(
        mop: widget.paymentMethod.mop,
        account: widget.account,
        popUntilRoute: widget.popUntilRoute,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final textStyles = DuploTextStyles.of(context);
    final EquitiLocalization localization = EquitiLocalization.of(context);
    return BlocProvider(
      create:
          (createContext) =>
              diContainer<WithdrawAccountsAndAmountBloc>()
                ..add(
                  WithdrawAccountsAndAmountEvent.setPaymentMethod(
                    paymentMop: widget.paymentMethod.mop,
                    selectedLeanAccount: widget.selectedLeanAccount,
                  ),
                )
                ..add(
                  WithdrawAccountsAndAmountEvent.setSelectedCard(
                    widget.selectedCard,
                  ),
                ),
      child: Scaffold(
        backgroundColor: theme.background.bgPrimary,
        appBar: DuploAppBar(
          title:
              "${localization.payments_withdraw_with} ${widget.paymentMethod.name}",
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  DuploText(
                    text: localization.payments_withdraw_from_account,
                    style: DuploTextStyles.of(context).textLg,
                    fontWeight: DuploFontWeight.semiBold,
                    color: theme.text.textPrimary,
                  ),
                  const SizedBox(height: 5),
                  DuploText(
                    text: localization.payments_withdraw_select_trading_account,
                    style: DuploTextStyles.of(context).textSm,
                    fontWeight: DuploFontWeight.regular,
                    color: theme.text.textSecondary,
                  ),
                  const SizedBox(height: 15),
                  BlocBuilder<
                    WithdrawAccountsAndAmountBloc,
                    WithdrawAccountsAndAmountState
                  >(
                    buildWhen:
                        (previous, current) =>
                            current.selectedAccount != null ||
                            previous.isLoading != current.isLoading,
                    builder: (builderContext, state) {
                      return AccountListWidget(
                        args: (
                          selectByHighestBalance: false,
                          excludeAccountNumber: null,
                          onEmptyStateChanged: ({
                            required bool hasAccounts,
                            required bool hasWallets,
                            required bool isCurrentTabEmpty,
                            required int currentTabIndex,
                          }) {
                            // Store the values for use in the widget
                            setState(() {
                              _isCurrentTabEmpty = isCurrentTabEmpty;
                            });
                          },
                          isInputDisabled: state.isLoading,
                        ),
                        onAccountSelected: (TradingAccountModel acc) {
                          diContainer<WithdrawAnalyticsEvent>()
                              .withdrawAccountSelected(
                                accountType: acc.accountType.name,
                                accountCurrency: acc.homeCurrency,
                              );
                          builderContext
                              .read<WithdrawAccountsAndAmountBloc>()
                              .add(
                                WithdrawAccountsAndAmountEvent.onAccountChange(
                                  acc,
                                ),
                              );
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 15),
                  !_isCurrentTabEmpty
                      ? BlocBuilder<
                        WithdrawAccountsAndAmountBloc,
                        WithdrawAccountsAndAmountState
                      >(
                        buildWhen:
                            (previous, current) =>
                                previous.selectedAccount !=
                                current.selectedAccount,
                        builder: (builderContext, state) {
                          return state.selectedAccount != null
                              ? BlocBuilder<
                                WithdrawAccountsAndAmountBloc,
                                WithdrawAccountsAndAmountState
                              >(
                                buildWhen:
                                    (previous, current) =>
                                        previous.hasInsufficientFunds !=
                                            current.hasInsufficientFunds ||
                                        previous.insufficientFundsMessage !=
                                            current.insufficientFundsMessage ||
                                        previous.isLoading != current.isLoading,
                                builder: (withdrawContext, withdrawState) {
                                  return DepositWithdrawAmountConversionWidget(
                                    args: (
                                      account: state.selectedAccount!,
                                      accountCurrency:
                                          state.selectedAccount!.homeCurrency,
                                      selectedCurrency: _getSelectedCurrency(
                                        state,
                                      ),
                                      currencyMinMaxSuggestedAmountList:
                                          widget
                                              .paymentMethod
                                              .currencyAmountDetails ??
                                          [],
                                      currencies:
                                          widget.paymentMethod.currencies ?? [],
                                      showSuggestedAmounts: true,
                                      isStartWithConversionRate:
                                          _isStartWithConversionRate(state),
                                      externalErrorMessage:
                                          withdrawState.hasInsufficientFunds ||
                                                  withdrawState
                                                      .insufficientFundsMessage
                                                      .isNotEmpty
                                              ? withdrawState
                                                  .insufficientFundsMessage
                                              : null,
                                      paymentType: PaymentType.withdrawal,
                                      premierAccountMinAmountForDeposit: null,
                                      isInputDisabled: withdrawState.isLoading,
                                      canChangeConversionCurrency:
                                          widget.paymentMethod.mop !=
                                          WithdrawalMop.lean,
                                    ),
                                    callback: ({
                                      required String accountCurrencyAmount,
                                      required String selectedCurrencyAmount,
                                      String? selectedCurrency,
                                      required bool isAmountValid,
                                      required RatesModel?
                                      conversionRateSelectedToAccountCurrency,
                                      String? conversionRateString,
                                      required ConversionRateModel?
                                      conversionRateData,
                                    }) {
                                      final bloc =
                                          builderContext
                                              .read<
                                                WithdrawAccountsAndAmountBloc
                                              >();
                                      bloc.add(
                                        WithdrawAccountsAndAmountEvent.changeButtonState(
                                          isValid: isAmountValid,
                                          accountCurrencyAmount:
                                              accountCurrencyAmount,
                                          selectedCurrencyAmount:
                                              selectedCurrencyAmount,
                                          ratesModel:
                                              conversionRateSelectedToAccountCurrency,
                                          conversionRateString:
                                              conversionRateString,
                                          selectedCurrency: selectedCurrency,
                                        ),
                                      );
                                    },
                                  );
                                },
                              )
                              : Container();
                        },
                      )
                      : Container(),
                  SizedBox(height: DuploSpacing.spacing_xl_16),
                  !_isCurrentTabEmpty
                      ? BlocBuilder<
                        WithdrawAccountsAndAmountBloc,
                        WithdrawAccountsAndAmountState
                      >(
                        buildWhen: (previous, current) => previous != current,
                        builder: (ctx, state) {
                          final String mopString;
                          final withdrawalMop = widget.paymentMethod.mop;
                          switch (withdrawalMop) {
                            case WithdrawalMop.cards:
                              mopString = state.selectedCard?.mop ?? "";
                              break;
                            default:
                              mopString = withdrawalMop.name;
                              break;
                          }
                          return state.selectedAccount != null &&
                                  withdrawalMop != WithdrawalMop.bank
                              ? WithdrawFeesDisplay(
                                args: (
                                  accountCurrencyAmount:
                                      double.tryParse(
                                        state.accountCurrencyAmount,
                                      ) ??
                                      0,
                                  accountId: state.selectedAccount!.recordId,
                                  accountCurrency:
                                      state.selectedAccount!.homeCurrency,
                                  selectedCurrency: state.selectedCurrency,
                                  paymentType: mopString,
                                  selectedCurrencyAmount:
                                      double.tryParse(
                                        state.selectedCurrencyAmount,
                                      ) ??
                                      0,
                                  transferType: null,
                                ),
                                content: (
                                  idle:
                                      localization.payments_withdraw_fees_idle,
                                  loading:
                                      localization
                                          .payments_withdraw_fees_loading,
                                  zeroFees:
                                      localization.payments_withdraw_fees_zero,
                                  nonZeroFees: (fees, total, currency) {
                                    final formattedFees =
                                        EquitiFormatter.formatNumber(
                                          value: fees,
                                          locale:
                                              Localizations.localeOf(
                                                context,
                                              ).toString(),
                                        );

                                    EquitiFormatter.formatNumber(
                                      value: total,
                                      locale:
                                          Localizations.localeOf(
                                            context,
                                          ).toString(),
                                    );
                                    final formattedAmount =
                                        EquitiFormatter.formatNumber(
                                          value: total - fees,
                                          locale:
                                              Localizations.localeOf(
                                                context,
                                              ).toString(),
                                        );

                                    return DuploText.rich(
                                      spans: [
                                        DuploTextSpan(
                                          text: localization
                                              .payments_withdraw_fees_non_zero(
                                                currency,
                                                formattedFees,
                                                formattedAmount,
                                                total,
                                              ),
                                          style: textStyles.textSm,
                                          color: theme.text.textSecondary,
                                        ),
                                      ],
                                    );
                                  },
                                ),
                                onFeesChange: (withdrawFeesState) {
                                  print(
                                    "WithdrawFeesState: $withdrawFeesState",
                                  );
                                  final bloc =
                                      ctx.read<WithdrawAccountsAndAmountBloc>();
                                  bloc.add(
                                    WithdrawAccountsAndAmountEvent.handleFeeStateChange(
                                      withdrawFeesState,
                                    ),
                                  );
                                },
                              )
                              : Container();
                        },
                      )
                      : Container(),

                  const SizedBox(height: DuploSpacing.spacing_xl_16),
                  if (widget.paymentMethod.mop == WithdrawalMop.bank &&
                      !_isCurrentTabEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: DuploSpacing.spacing_xl_16,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          paymentAssets.Assets.images.infoCircle.svg(
                            height: DuploSpacing.spacing_xl_16,
                          ),
                          SizedBox(width: DuploSpacing.spacing_md_8),
                          DuploText(
                            text: localization.payments_noLocalBankFeesApply,
                            style: DuploTextStyles.of(context).textXs,
                            color: theme.text.textQuaternary,
                          ),
                        ],
                      ),
                    ),
                  !_isCurrentTabEmpty
                      ? BlocBuilder<
                        WithdrawAccountsAndAmountBloc,
                        WithdrawAccountsAndAmountState
                      >(
                        buildWhen:
                            (previous, current) =>
                                previous.isButtonEnabled !=
                                    current.isButtonEnabled ||
                                previous.isLoading != current.isLoading,
                        builder: (builderContext, state) {
                          return DuploButton.defaultPrimary(
                            title: localization.payments_continue,
                            onTap:
                                () => _handleContinueButtonTap(builderContext),
                            isDisabled: !state.isButtonEnabled,
                            isLoading: state.isLoading,
                            useFullWidth: true,
                            trailingIcon: Assets.images.chevronRight.keyName,
                          );
                        },
                      )
                      : Container(),
                  const SizedBox(height: 20), // Extra space at the bottom
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String? _getSelectedCurrency(WithdrawAccountsAndAmountState state) {
    // in lean we dont want user to change currency as we can only withdraw using
    // USD and AED account so if the trading account selected is not AED/USD
    // we select the currency to lean account currency
    if (widget.paymentMethod.mop == WithdrawalMop.lean) {
      return state.selectedLeanAccount?.currencyIsoCode;
    }
    return AmountConversionUtils.getSelectedCurrency(
      selectedCurrency: state.selectedCurrency,
      accountCurrency: state.selectedAccount!.homeCurrency,
      defaultCurrency: widget.paymentMethod.defaultCurrency,
      currencyAmountDetails: widget.paymentMethod.currencyAmountDetails,
    );
  }

  bool _isStartWithConversionRate(WithdrawAccountsAndAmountState state) {
    // in lean we dont want user to change currency as we can only deposit using
    // USD and AED account so if the trading account selected is not AED/USD we
    // should show start with conversion
    if (widget.paymentMethod.mop == WithdrawalMop.lean) {
      return state.selectedLeanAccount?.currencyIsoCode !=
          state.selectedAccount!.homeCurrency;
    }
    return AmountConversionUtils.getIsStartWithConversionRate(
      accountCurrency: state.selectedAccount!.homeCurrency,
      currencyAmountDetails: widget.paymentMethod.currencyAmountDetails,
    );
  }
}
