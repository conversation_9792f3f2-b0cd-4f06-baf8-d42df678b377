import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';

import 'package:domain/domain.dart';
import 'package:payment/src/assets/assets.gen.dart' as assets;
import 'package:prelude/prelude.dart';

class SourceAccountCard extends StatelessWidget {
  final TradingAccountModel account;
  const SourceAccountCard({super.key, required this.account});

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final formattedBalance = EquitiFormatter.decimalPatternDigits(
      value: account.effectiveBalance ?? 0,
      digits: 2,
      locale: Localizations.localeOf(context).toString(),
    );
    final String balanceWhole = formattedBalance.split('.').firstOrNull!;
    final String balanceFraction =
        formattedBalance.split('.').elementAtOrNull(1)!;
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.border.borderSecondary),
        color: theme.background.bgPrimary,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploText(
                  text:
                      "${account.nickName ?? ""} ${EquitiLocalization.of(context).payments_balance}",
                  style: style.textXs,
                  color: theme.text.textSecondary,
                  fontWeight: DuploFontWeight.medium,
                ),
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      DuploText(
                        text: balanceWhole,
                        style: style.displayXs,
                        color: theme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                      ),
                      DuploText(
                        text: ".${balanceFraction}",
                        style: style.textXs,
                        color: theme.text.textSecondary,
                        fontWeight: DuploFontWeight.medium,
                        textAlign: TextAlign.end,
                      ),
                      DuploText(
                        text: " ${account.homeCurrency.capitalize()}",
                        style: style.textXs,
                        color: theme.text.textSecondary,
                        fontWeight: DuploFontWeight.medium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: assets.Assets.images.editIcon.svg(),
            ),
          ],
        ),
      ),
    );
  }
}
