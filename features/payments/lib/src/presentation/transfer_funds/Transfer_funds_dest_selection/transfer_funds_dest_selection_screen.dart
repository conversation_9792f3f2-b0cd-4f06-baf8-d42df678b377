import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/analytics/transfer_analytics_event.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';
import 'package:payment/src/di/di_container.dart';

import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/bloc/transfer_funds_dest_selection_bloc.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/error_transfer_screen.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/source_account_card.dart';
import 'package:payment/src/presentation/transfer_funds/Transfer_funds_dest_selection/widgets/transfer_status_view.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/transfer_amount_conversion_widget/transfer_amount_conversion_widget.dart';

class TransferFundsDestSelectionScreen extends StatelessWidget {
  const TransferFundsDestSelectionScreen({
    super.key,
    required this.account,
    required this.originRoute,
  });
  final TradingAccountModel account;
  final String originRoute;

  @override
  Widget build(BuildContext context) {
    final theme = context.duploTheme;
    final style = context.duploTextStyles;
    final localization = EquitiLocalization.of(context);
    return BlocProvider(
      create: (blocProviderContext) {
        diContainer<TransferAnalyticsEvent>()
            .transferDestinationAccountPageLoaded();
        return diContainer<TransferFundsDestSelectionBloc>();
      },
      child: BlocBuilder<
        TransferFundsDestSelectionBloc,
        TransferFundsDestSelectionState
      >(
        buildWhen:
            (previous, current) =>
                (previous.processState != current.processState ||
                    previous.destinationAccount != current.destinationAccount ||
                    previous.isCurrentTabEmpty != current.isCurrentTabEmpty ||
                    previous.hasAccounts != current.hasAccounts ||
                    previous.hasWallets != current.hasWallets ||
                    previous.errorMessage != current.errorMessage ||
                    previous.isButtonLoading != current.isButtonLoading),
        builder: (blocBuilderContext, state) {
          return Scaffold(
            backgroundColor: theme.background.bgPrimary,
            appBar: switch (state.processState) {
              ErrorTransfer() || TransferStatusScreen() => null,
              (_) => DuploAppBar(title: localization.payments_transfer),
            },
            body: switch (state.processState) {
              ErrorTransfer() => ErrorTransferScreen(),
              TransferStatusScreen(
                :final sourceAccount,
                :final destinationAccount,
                :final amount,
                :final status,
              ) =>
                TransferStatusView(
                  sourceAccount: sourceAccount,
                  destinationAccount: destinationAccount,
                  amount: amount,
                  originRoute: originRoute,
                  transferStatus: status,
                ),
              StartedScreen() => SafeArea(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      children: [
                        const SizedBox(height: 8),
                        Align(
                          alignment: AlignmentDirectional.centerStart,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              DuploText(
                                text:
                                    localization.payments_source_account_title,
                                style: style.textLg,
                                color: theme.text.textPrimary,
                                fontWeight: DuploFontWeight.semiBold,
                              ),
                              const SizedBox(height: 8),
                              SourceAccountCard(account: account),
                              const SizedBox(height: 23),
                              DuploText(
                                text:
                                    localization
                                        .payments_destination_account_title,
                                style: style.textLg,
                                color: theme.text.textPrimary,
                                fontWeight: DuploFontWeight.semiBold,
                              ),
                              const SizedBox(height: 8),
                              DuploText(
                                text:
                                    localization
                                        .payments_destination_account_subtext,
                                style: style.textSm,
                                color: theme.text.textSecondary,
                              ),
                              const SizedBox(height: 23),
                            ],
                          ),
                        ),
                        AccountListWidget(
                          args: (
                            selectByHighestBalance: false,
                            excludeAccountNumber: account.accountNumber,
                            isInputDisabled: state.isButtonLoading,
                            onEmptyStateChanged: ({
                              required bool hasAccounts,
                              required bool hasWallets,
                              required bool isCurrentTabEmpty,
                              required int currentTabIndex,
                            }) {
                              blocBuilderContext
                                  .read<TransferFundsDestSelectionBloc>()
                                  .add(
                                    TransferFundsDestSelectionEvent.updateEmptyState(
                                      hasAccounts: hasAccounts,
                                      hasWallets: hasWallets,
                                      isCurrentTabEmpty: isCurrentTabEmpty,
                                      currentTabIndex: currentTabIndex,
                                    ),
                                  );
                            },
                          ),
                          onAccountSelected: (
                            TradingAccountModel destinationAccount,
                          ) {
                            diContainer<TransferAnalyticsEvent>()
                                .transferDestinationAccountSelected();
                            blocBuilderContext
                                .read<TransferFundsDestSelectionBloc>()
                              ..add(
                                TransferFundsDestSelectionEvent.selectDestinationAccount(
                                  destinationAccount,
                                ),
                              );
                          },
                        ),
                        // Only show DepositAmount if current tab is not empty AND destination account is selected
                        if (!state.isCurrentTabEmpty &&
                            state.destinationAccount != null)
                          TransferAmountConversionWidget(
                            key: ValueKey(
                              '${account.homeCurrency}_${state.destinationAccount!.homeCurrency}',
                            ),
                            args: (
                              sourceCurrency: account.homeCurrency,
                              account: account,
                              currencyMinMaxSuggestedAmountList:
                                  _getCurrencyMinMaxSuggestedAmountList(state),
                              currencies: [
                                state.destinationAccount?.homeCurrency ??
                                    account.homeCurrency,
                              ],
                              showSuggestedAmounts: false,
                              isStartWithConversionRate:
                                  (account.homeCurrency !=
                                      state.destinationAccount?.homeCurrency),
                              destinationCurrency:
                                  state.destinationAccount!.homeCurrency,
                              externalErrorMessage: state.errorMessage,
                              paymentType: PaymentType.transfer,
                              isInputDisabled: state.isButtonLoading,
                            ),
                            callback: ({
                              required String amountInDestinationCurrency,
                              required String amountInSourceCurrency,
                              String? selectedCurrency,
                              required bool isAmountValid,
                              required RatesModel?
                              conversionRateToDestinationCurrency,
                              String? conversionRateString,
                              required ConversionRateModel? conversionRateData,
                            }) {
                              blocBuilderContext
                                  .read<TransferFundsDestSelectionBloc>()
                                  .add(
                                    TransferFundsDestSelectionEvent.changeButtonStateAndUpdateAmount(
                                      amountInDestinationCurrency:
                                          amountInDestinationCurrency,
                                      isDisabled: !isAmountValid,
                                      amountInSourceCurrency:
                                          amountInSourceCurrency,
                                      conversionRateModel: conversionRateData,
                                      conversionRateString:
                                          conversionRateString,
                                      conversionRateToDestinationCurrency:
                                          conversionRateToDestinationCurrency,
                                    ),
                                  );
                            },
                          ),
                        // Show message when current tab is empty
                        if (state.isCurrentTabEmpty) const SizedBox(),
                        if (!state.isCurrentTabEmpty &&
                            state.destinationAccount != null)
                          Padding(
                            padding: const EdgeInsets.only(
                              top: DuploSpacing.spacing_3xl_24,
                              bottom: DuploSpacing.spacing_xl_16,
                            ),
                            child: BlocBuilder<
                              TransferFundsDestSelectionBloc,
                              TransferFundsDestSelectionState
                            >(
                              buildWhen:
                                  (previous, current) =>
                                      (previous.isButtonDisabled !=
                                              current.isButtonDisabled ||
                                          previous.amountInSourceCurrency !=
                                              current.amountInSourceCurrency) ||
                                      previous.isButtonLoading !=
                                          current.isButtonLoading ||
                                      previous.errorMessage !=
                                          current.errorMessage,
                              builder: (buttonContext, buttonState) {
                                return DuploButton.defaultPrimary(
                                  isDisabled:
                                      buttonState.isButtonDisabled ||
                                      buttonState.errorMessage != null,
                                  title: localization.payments_continue,
                                  useFullWidth: true,
                                  isLoading: buttonState.isButtonLoading,
                                  trailingIcon:
                                      Assets.images.chevronRight.keyName,

                                  onTap: () {
                                    buttonContext
                                        .read<TransferFundsDestSelectionBloc>()
                                      ..add(
                                        TransferFundsDestSelectionEvent.transferFunds(
                                          sourceAccount: account,
                                          destinationAccount:
                                              state.destinationAccount!,
                                        ),
                                      );
                                  },
                                );
                              },
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            },
          );
        },
      ),
    );
  }

  List<CurrencyAmountDetail> _getCurrencyMinMaxSuggestedAmountList(
    TransferFundsDestSelectionState state,
  ) {
    return (account.homeCurrency != state.destinationAccount?.homeCurrency)
        ? [
          CurrencyAmountDetail(
            currency: state.destinationAccount!.homeCurrency,
            maxAmount: account.effectiveBalance,
            minAmount: 1,
          ),
          CurrencyAmountDetail(
            currency: account.homeCurrency,
            maxAmount: account.effectiveBalance,
            minAmount: 1,
          ),
        ]
        : [
          CurrencyAmountDetail(
            currency: account.homeCurrency,
            maxAmount: account.effectiveBalance,
            minAmount: 1,
          ),
        ];
  }
}
