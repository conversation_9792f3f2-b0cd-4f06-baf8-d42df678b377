part of 'transfer_funds_bloc.dart';

@freezed
sealed class TransferFundsState with _$TransferFundsState {
  const factory TransferFundsState({TradingAccountModel? selectedAccount}) =
      _AccountSelected;
  const TransferFundsState._();
  bool get isButtonStateDisabled =>
      selectedAccount == null ||
      selectedAccount!.effectiveBalance == null ||
      selectedAccount!.effectiveBalance == 0;
}
