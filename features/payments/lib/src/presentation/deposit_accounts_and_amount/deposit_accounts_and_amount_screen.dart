import 'dart:developer';

import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/analytics/deposit_analytics_event.dart';
import 'package:payment/src/utils/payment/lean_payment.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:domain/domain.dart';
import 'package:campaigns/campaigns.dart';
import 'package:payment/src/domain/usecase/get_deposit_details_usecase.dart';
import 'package:payment/src/navigation/arguments/lean_account_args/lean_account_args.dart';
import 'package:payment/src/navigation/payment_navigation.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/widgets/deposit_accounts_amount_loaded_screen.dart';

import 'package:payment/src/presentation/widgets/transaction_status_screen.dart';
import 'package:payment/src/utils/payment/apple_pay_utils.dart';
import 'package:payment/src/utils/payment/google_pay_utils.dart';

import 'bloc/deposit_accounts_and_amount_bloc.dart';

class DepositAccountsAndAmountScreen extends StatelessWidget {
  const DepositAccountsAndAmountScreen({
    super.key,
    required this.paymentMethod,
    this.maxPollingAttempts,
    this.pollingFrequencySeconds,
    required this.depositFlowConfig,
    this.leanAccountArgs,
  });
  final DepositPaymentMethod paymentMethod;
  final DepositFlowConfig depositFlowConfig;
  final num? maxPollingAttempts;
  final num? pollingFrequencySeconds;
  final LeanAccountArgs? leanAccountArgs;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    final theme = DuploTheme.of(context);
    final textStyle = context.duploTextStyles;
    return BlocProvider(
      create:
          (ctx) =>
              DepositAccountsAndAmountBloc(
                  getDepositDetailsUsecase:
                      diContainer<GetDepositDetailsUsecase>(),
                  getCampaignsUseCase: diContainer<GetCampaignsUseCase>(),
                  getCampaignInfoUseCase: diContainer<GetCampaignInfoUseCase>(),
                  paymentNavigation: diContainer<PaymentNavigation>(),
                  googlePayUtils: diContainer<GooglePayUtils>(),
                  applePayUtils: diContainer<ApplePayUtils>(),
                  depositAnalyticsEvent: diContainer<DepositAnalyticsEvent>(),
                  maxPollingAttempts: maxPollingAttempts,
                  pollingFrequencySeconds: pollingFrequencySeconds,
                  depositFlowConfig: depositFlowConfig,
                )
                ..add(const DepositAccountsAndAmountEvent.getCampaigns())
                ..add(
                  DepositAccountsAndAmountEvent.initArgs(
                    maxPollingAttempts: maxPollingAttempts,
                    pollingFrequencySeconds: pollingFrequencySeconds,
                    depositFlowConfig: depositFlowConfig,
                    leanAccountArgs: leanAccountArgs,
                  ),
                ),
      child: BlocConsumer<
        DepositAccountsAndAmountBloc,
        DepositAccountsAndAmountState
      >(
        buildWhen:
            (previous, current) =>
                previous.processState != current.processState ||
                previous.isFullPageLoadingEnabled !=
                    current.isFullPageLoadingEnabled,
        listener: (listenerContext, state) {
          if (state.processState is StartLeanPaymentState) {
            final leanState = state.processState as StartLeanPaymentState;
            _openLeanPaymentBottomSheet(
              leanState: leanState,
              context: listenerContext,
            );
          }
        },
        builder: (ctx, state) {
          final bloc = ctx.read<DepositAccountsAndAmountBloc>();
          if (state.processState is PaymentSuccessState &&
              depositFlowConfig.depositType == DepositType.first) {
            diContainer<DepositAnalyticsEvent>().firstDepositComplete();
          }
          return switch (state.processState) {
            ErrorState() => Center(
              child: DuploText(
                text: localization.trader_error,
                style: textStyle.textSm,
                color: theme.text.textPrimary,
              ),
            ),
            PaymentFailedState() => TransactionStatusScreen.error(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            PaymentRejectedState() => TransactionStatusScreen.declined(
              context: context,
              onChangePaymentMethod: () {
                bloc.add(const OnPaymentMethodChange());
              },
              onTryAgain: () {
                bloc.add(const ResetProcessStateEvent());
              },
              gatewayCode: null,
            ),
            PaymentPendingState() => TransactionStatusScreen.submitted(
              context: context,
              onContinue: () {
                bloc.add(const OnPaymentSuccessContinuePressed());
              },
              onMakeAnotherDeposit: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            PaymentSuccessState() => TransactionStatusScreen.success(
              context: context,
              onContinue: () {
                bloc.add(const OnPaymentSuccessContinuePressed());
              },
              onMakeAnotherDeposit: () {
                bloc.add(const OnPaymentMethodChange());
              },
            ),
            StartLeanPaymentState() ||
            LoadedState() => DepositAccountsAmountLoadedScreen(
              paymentMethod: paymentMethod,
              isFullScreenLoaderEnabled: state.isFullPageLoadingEnabled,
            ),
          };
        },
      ),
    );
  }

  Future<void> _openLeanPaymentBottomSheet({
    required StartLeanPaymentState leanState,
    required BuildContext context,
  }) async {
    final bloc = context.read<DepositAccountsAndAmountBloc>();

    final analytics = diContainer<DepositAnalyticsEvent>();
    await analytics.depositLeanPayStart(
      paymentIntentId: leanState.paymentIntentId,
      accountId: leanAccountArgs!.selectedLeanAccount?.id,
    );
    await analytics.depositLeanPayBottomSheetOpened(
      paymentIntentId: leanState.paymentIntentId,
    );
    if (leanAccountArgs!.selectedLeanAccount?.id != null) {
      await analytics.depositLeanAccountPrefilled(
        accountId: leanAccountArgs!.selectedLeanAccount!.id,
      );
    }
    try {
      DuploSheet.showNonScrollableModalSheet(
        context: context,
        hasTopBarLayer: false,
        useSafeArea: true,
        backgroundColor: context.duploTheme.background.bgPrimary,
        hasTrailingIc: false,
        showDragHandle: false,
        content: (sheetContext) {
          return SizedBox(
            height: MediaQuery.sizeOf(context).height * .7,
            child:
                leanState.leanMetaData?.appToken == null ||
                        leanState.leanMetaData?.customerToken == null
                    ? Center(
                      child: DuploText(
                        text: 'Something went wrong! Lean metadata is null',
                        style: context.duploTextStyles.textMd,
                        color: context.duploTheme.text.textPrimary,
                        fontWeight: DuploFontWeight.semiBold,
                      ),
                    )
                    : LeanPayment.pay(
                      context: context,
                      paymentIntentId: leanState.paymentIntentId,
                      showLogs: true,
                      accountId: leanAccountArgs!.selectedLeanAccount?.id,
                      appToken: leanState.leanMetaData!.appToken!,
                      accessToken: leanState.leanMetaData!.customerToken!,
                      onCancelled: () {
                        log('Lean connect action cancelled by user');
                        analytics.depositLeanPayCancelled(
                          paymentIntentId: leanState.paymentIntentId,
                        );
                      },
                      onCallback: (response) {
                        analytics.depositLeanPayCallback(
                          paymentIntentId: leanState.paymentIntentId,
                          status: response.status,
                          message: response.message,
                          secondaryStatus: response.secondaryStatus,
                        );
                        if (response.status == 'SUCCESS' &&
                            response.secondaryStatus == 'ACCEPTED_BY_BANK') {
                          analytics.depositLeanPaySuccess(
                            paymentIntentId: leanState.paymentIntentId,
                          );
                        } else if (response.status == 'SUCCESS' &&
                            response.secondaryStatus == 'PENDING_WITH_BANK') {
                          analytics.depositLeanPayPending(
                            paymentIntentId: leanState.paymentIntentId,
                            status: response.status,
                            secondaryStatus: response.secondaryStatus,
                          );
                        } else {
                          analytics.depositLeanPayFailure(
                            paymentIntentId: leanState.paymentIntentId,
                            status: response.status,
                            secondaryStatus: response.secondaryStatus,
                          );
                        }
                        bloc.add(
                          DepositAccountsAndAmountEvent.onLeanPaymentCallback(
                            transactionId: leanState.transactionId,
                            paymentIntentId: leanState.paymentIntentId,
                            response: response,
                          ),
                        );
                      },
                      customization: LeanPayment.returnLeanCustomization(
                        context: context,
                      ),
                    ),
          );
        },
      );
    } catch (e) {
      await analytics.depositLeanPayError(
        paymentIntentId: leanState.paymentIntentId,
        errorMessage: e.toString(),
      );
    }
  }
}
