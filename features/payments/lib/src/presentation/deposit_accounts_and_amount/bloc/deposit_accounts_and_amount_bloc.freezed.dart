// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'deposit_accounts_and_amount_bloc.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$DepositAccountsAndAmountEvent {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent()';
}


}

/// @nodoc
class $DepositAccountsAndAmountEventCopyWith<$Res>  {
$DepositAccountsAndAmountEventCopyWith(DepositAccountsAndAmountEvent _, $Res Function(DepositAccountsAndAmountEvent) __);
}


/// @nodoc


class _OnAccountChange implements DepositAccountsAndAmountEvent {
  const _OnAccountChange(this.account);
  

 final  TradingAccountModel account;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAccountChangeCopyWith<_OnAccountChange> get copyWith => __$OnAccountChangeCopyWithImpl<_OnAccountChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAccountChange&&(identical(other.account, account) || other.account == account));
}


@override
int get hashCode => Object.hash(runtimeType,account);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onAccountChange(account: $account)';
}


}

/// @nodoc
abstract mixin class _$OnAccountChangeCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnAccountChangeCopyWith(_OnAccountChange value, $Res Function(_OnAccountChange) _then) = __$OnAccountChangeCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel account
});


$TradingAccountModelCopyWith<$Res> get account;

}
/// @nodoc
class __$OnAccountChangeCopyWithImpl<$Res>
    implements _$OnAccountChangeCopyWith<$Res> {
  __$OnAccountChangeCopyWithImpl(this._self, this._then);

  final _OnAccountChange _self;
  final $Res Function(_OnAccountChange) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? account = null,}) {
  return _then(_OnAccountChange(
null == account ? _self.account : account // ignore: cast_nullable_to_non_nullable
as TradingAccountModel,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res> get account {
  
  return $TradingAccountModelCopyWith<$Res>(_self.account, (value) {
    return _then(_self.copyWith(account: value));
  });
}
}

/// @nodoc


class _NaviagteToSelectedPaymentMethod implements DepositAccountsAndAmountEvent {
  const _NaviagteToSelectedPaymentMethod(this.mop);
  

 final  DepositPaymentMethod? mop;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$NaviagteToSelectedPaymentMethodCopyWith<_NaviagteToSelectedPaymentMethod> get copyWith => __$NaviagteToSelectedPaymentMethodCopyWithImpl<_NaviagteToSelectedPaymentMethod>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _NaviagteToSelectedPaymentMethod&&(identical(other.mop, mop) || other.mop == mop));
}


@override
int get hashCode => Object.hash(runtimeType,mop);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.naviagteToSelectedPaymentMethod(mop: $mop)';
}


}

/// @nodoc
abstract mixin class _$NaviagteToSelectedPaymentMethodCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$NaviagteToSelectedPaymentMethodCopyWith(_NaviagteToSelectedPaymentMethod value, $Res Function(_NaviagteToSelectedPaymentMethod) _then) = __$NaviagteToSelectedPaymentMethodCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod? mop
});


$DepositPaymentMethodCopyWith<$Res>? get mop;

}
/// @nodoc
class __$NaviagteToSelectedPaymentMethodCopyWithImpl<$Res>
    implements _$NaviagteToSelectedPaymentMethodCopyWith<$Res> {
  __$NaviagteToSelectedPaymentMethodCopyWithImpl(this._self, this._then);

  final _NaviagteToSelectedPaymentMethod _self;
  final $Res Function(_NaviagteToSelectedPaymentMethod) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? mop = freezed,}) {
  return _then(_NaviagteToSelectedPaymentMethod(
freezed == mop ? _self.mop : mop // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod?,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res>? get mop {
    if (_self.mop == null) {
    return null;
  }

  return $DepositPaymentMethodCopyWith<$Res>(_self.mop!, (value) {
    return _then(_self.copyWith(mop: value));
  });
}
}

/// @nodoc


class _GetDepositDetails implements DepositAccountsAndAmountEvent {
  const _GetDepositDetails({required this.paymentMethod, required this.isDarkTheme});
  

 final  DepositPaymentMethod paymentMethod;
 final  bool isDarkTheme;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetDepositDetailsCopyWith<_GetDepositDetails> get copyWith => __$GetDepositDetailsCopyWithImpl<_GetDepositDetails>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetDepositDetails&&(identical(other.paymentMethod, paymentMethod) || other.paymentMethod == paymentMethod)&&(identical(other.isDarkTheme, isDarkTheme) || other.isDarkTheme == isDarkTheme));
}


@override
int get hashCode => Object.hash(runtimeType,paymentMethod,isDarkTheme);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.getDepositDetails(paymentMethod: $paymentMethod, isDarkTheme: $isDarkTheme)';
}


}

/// @nodoc
abstract mixin class _$GetDepositDetailsCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$GetDepositDetailsCopyWith(_GetDepositDetails value, $Res Function(_GetDepositDetails) _then) = __$GetDepositDetailsCopyWithImpl;
@useResult
$Res call({
 DepositPaymentMethod paymentMethod, bool isDarkTheme
});


$DepositPaymentMethodCopyWith<$Res> get paymentMethod;

}
/// @nodoc
class __$GetDepositDetailsCopyWithImpl<$Res>
    implements _$GetDepositDetailsCopyWith<$Res> {
  __$GetDepositDetailsCopyWithImpl(this._self, this._then);

  final _GetDepositDetails _self;
  final $Res Function(_GetDepositDetails) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentMethod = null,Object? isDarkTheme = null,}) {
  return _then(_GetDepositDetails(
paymentMethod: null == paymentMethod ? _self.paymentMethod : paymentMethod // ignore: cast_nullable_to_non_nullable
as DepositPaymentMethod,isDarkTheme: null == isDarkTheme ? _self.isDarkTheme : isDarkTheme // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositPaymentMethodCopyWith<$Res> get paymentMethod {
  
  return $DepositPaymentMethodCopyWith<$Res>(_self.paymentMethod, (value) {
    return _then(_self.copyWith(paymentMethod: value));
  });
}
}

/// @nodoc


class _OnAmountChange implements DepositAccountsAndAmountEvent {
  const _OnAmountChange(this.accountCurrencyAmount, this.selectedCurrencyAmount, this.ratesModel, this.conversionRateString, this.selectedCurrency);
  

 final  String accountCurrencyAmount;
 final  String selectedCurrencyAmount;
 final  RatesModel? ratesModel;
 final  String? conversionRateString;
 final  String? selectedCurrency;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnAmountChangeCopyWith<_OnAmountChange> get copyWith => __$OnAmountChangeCopyWithImpl<_OnAmountChange>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnAmountChange&&(identical(other.accountCurrencyAmount, accountCurrencyAmount) || other.accountCurrencyAmount == accountCurrencyAmount)&&(identical(other.selectedCurrencyAmount, selectedCurrencyAmount) || other.selectedCurrencyAmount == selectedCurrencyAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.selectedCurrency, selectedCurrency) || other.selectedCurrency == selectedCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,accountCurrencyAmount,selectedCurrencyAmount,ratesModel,conversionRateString,selectedCurrency);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onAmountChange(accountCurrencyAmount: $accountCurrencyAmount, selectedCurrencyAmount: $selectedCurrencyAmount, ratesModel: $ratesModel, conversionRateString: $conversionRateString, selectedCurrency: $selectedCurrency)';
}


}

/// @nodoc
abstract mixin class _$OnAmountChangeCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnAmountChangeCopyWith(_OnAmountChange value, $Res Function(_OnAmountChange) _then) = __$OnAmountChangeCopyWithImpl;
@useResult
$Res call({
 String accountCurrencyAmount, String selectedCurrencyAmount, RatesModel? ratesModel, String? conversionRateString, String? selectedCurrency
});


$RatesModelCopyWith<$Res>? get ratesModel;

}
/// @nodoc
class __$OnAmountChangeCopyWithImpl<$Res>
    implements _$OnAmountChangeCopyWith<$Res> {
  __$OnAmountChangeCopyWithImpl(this._self, this._then);

  final _OnAmountChange _self;
  final $Res Function(_OnAmountChange) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountCurrencyAmount = null,Object? selectedCurrencyAmount = null,Object? ratesModel = freezed,Object? conversionRateString = freezed,Object? selectedCurrency = freezed,}) {
  return _then(_OnAmountChange(
null == accountCurrencyAmount ? _self.accountCurrencyAmount : accountCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,null == selectedCurrencyAmount ? _self.selectedCurrencyAmount : selectedCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,freezed == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String?,freezed == selectedCurrency ? _self.selectedCurrency : selectedCurrency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}
}

/// @nodoc


class _ChangeButtonState implements DepositAccountsAndAmountEvent {
  const _ChangeButtonState(this.isValid);
  

 final  bool isValid;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeButtonStateCopyWith<_ChangeButtonState> get copyWith => __$ChangeButtonStateCopyWithImpl<_ChangeButtonState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeButtonState&&(identical(other.isValid, isValid) || other.isValid == isValid));
}


@override
int get hashCode => Object.hash(runtimeType,isValid);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.changeButtonState(isValid: $isValid)';
}


}

/// @nodoc
abstract mixin class _$ChangeButtonStateCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$ChangeButtonStateCopyWith(_ChangeButtonState value, $Res Function(_ChangeButtonState) _then) = __$ChangeButtonStateCopyWithImpl;
@useResult
$Res call({
 bool isValid
});




}
/// @nodoc
class __$ChangeButtonStateCopyWithImpl<$Res>
    implements _$ChangeButtonStateCopyWith<$Res> {
  __$ChangeButtonStateCopyWithImpl(this._self, this._then);

  final _ChangeButtonState _self;
  final $Res Function(_ChangeButtonState) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? isValid = null,}) {
  return _then(_ChangeButtonState(
null == isValid ? _self.isValid : isValid // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

/// @nodoc


class _OnApplePayResult implements DepositAccountsAndAmountEvent {
  const _OnApplePayResult({required this.paymentStatus, required this.gatewayCode, required this.applePay});
  

 final  ApplePayStatus paymentStatus;
 final  String? gatewayCode;
 final  ApplePayUtils applePay;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnApplePayResultCopyWith<_OnApplePayResult> get copyWith => __$OnApplePayResultCopyWithImpl<_OnApplePayResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnApplePayResult&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.gatewayCode, gatewayCode) || other.gatewayCode == gatewayCode)&&(identical(other.applePay, applePay) || other.applePay == applePay));
}


@override
int get hashCode => Object.hash(runtimeType,paymentStatus,gatewayCode,applePay);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onApplePayResult(paymentStatus: $paymentStatus, gatewayCode: $gatewayCode, applePay: $applePay)';
}


}

/// @nodoc
abstract mixin class _$OnApplePayResultCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnApplePayResultCopyWith(_OnApplePayResult value, $Res Function(_OnApplePayResult) _then) = __$OnApplePayResultCopyWithImpl;
@useResult
$Res call({
 ApplePayStatus paymentStatus, String? gatewayCode, ApplePayUtils applePay
});




}
/// @nodoc
class __$OnApplePayResultCopyWithImpl<$Res>
    implements _$OnApplePayResultCopyWith<$Res> {
  __$OnApplePayResultCopyWithImpl(this._self, this._then);

  final _OnApplePayResult _self;
  final $Res Function(_OnApplePayResult) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentStatus = null,Object? gatewayCode = freezed,Object? applePay = null,}) {
  return _then(_OnApplePayResult(
paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as ApplePayStatus,gatewayCode: freezed == gatewayCode ? _self.gatewayCode : gatewayCode // ignore: cast_nullable_to_non_nullable
as String?,applePay: null == applePay ? _self.applePay : applePay // ignore: cast_nullable_to_non_nullable
as ApplePayUtils,
  ));
}


}

/// @nodoc


class _OnGooglePayResult implements DepositAccountsAndAmountEvent {
  const _OnGooglePayResult({required this.paymentStatus, required this.gatewayCode, required this.googlePay});
  

 final  GooglePayStatus paymentStatus;
 final  String? gatewayCode;
 final  GooglePayUtils googlePay;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnGooglePayResultCopyWith<_OnGooglePayResult> get copyWith => __$OnGooglePayResultCopyWithImpl<_OnGooglePayResult>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnGooglePayResult&&(identical(other.paymentStatus, paymentStatus) || other.paymentStatus == paymentStatus)&&(identical(other.gatewayCode, gatewayCode) || other.gatewayCode == gatewayCode)&&(identical(other.googlePay, googlePay) || other.googlePay == googlePay));
}


@override
int get hashCode => Object.hash(runtimeType,paymentStatus,gatewayCode,googlePay);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onGooglePayResult(paymentStatus: $paymentStatus, gatewayCode: $gatewayCode, googlePay: $googlePay)';
}


}

/// @nodoc
abstract mixin class _$OnGooglePayResultCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnGooglePayResultCopyWith(_OnGooglePayResult value, $Res Function(_OnGooglePayResult) _then) = __$OnGooglePayResultCopyWithImpl;
@useResult
$Res call({
 GooglePayStatus paymentStatus, String? gatewayCode, GooglePayUtils googlePay
});




}
/// @nodoc
class __$OnGooglePayResultCopyWithImpl<$Res>
    implements _$OnGooglePayResultCopyWith<$Res> {
  __$OnGooglePayResultCopyWithImpl(this._self, this._then);

  final _OnGooglePayResult _self;
  final $Res Function(_OnGooglePayResult) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? paymentStatus = null,Object? gatewayCode = freezed,Object? googlePay = null,}) {
  return _then(_OnGooglePayResult(
paymentStatus: null == paymentStatus ? _self.paymentStatus : paymentStatus // ignore: cast_nullable_to_non_nullable
as GooglePayStatus,gatewayCode: freezed == gatewayCode ? _self.gatewayCode : gatewayCode // ignore: cast_nullable_to_non_nullable
as String?,googlePay: null == googlePay ? _self.googlePay : googlePay // ignore: cast_nullable_to_non_nullable
as GooglePayUtils,
  ));
}


}

/// @nodoc


class OnPaymentMethodChange implements DepositAccountsAndAmountEvent {
  const OnPaymentMethodChange();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnPaymentMethodChange);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onPaymentMethodChange()';
}


}




/// @nodoc


class OnPaymentSuccessContinuePressed implements DepositAccountsAndAmountEvent {
  const OnPaymentSuccessContinuePressed();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is OnPaymentSuccessContinuePressed);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onPaymentSuccessContinuePressed()';
}


}




/// @nodoc


class ResetProcessStateEvent implements DepositAccountsAndAmountEvent {
  const ResetProcessStateEvent();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ResetProcessStateEvent);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.resetProcessState()';
}


}




/// @nodoc


class _GetCampaigns implements DepositAccountsAndAmountEvent {
  const _GetCampaigns();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetCampaigns);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.getCampaigns()';
}


}




/// @nodoc


class _GetCampaignInfo implements DepositAccountsAndAmountEvent {
  const _GetCampaignInfo({required this.accountId, required this.accountCurrency, required this.campaignId});
  

 final  String accountId;
 final  String accountCurrency;
 final  String campaignId;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GetCampaignInfoCopyWith<_GetCampaignInfo> get copyWith => __$GetCampaignInfoCopyWithImpl<_GetCampaignInfo>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GetCampaignInfo&&(identical(other.accountId, accountId) || other.accountId == accountId)&&(identical(other.accountCurrency, accountCurrency) || other.accountCurrency == accountCurrency)&&(identical(other.campaignId, campaignId) || other.campaignId == campaignId));
}


@override
int get hashCode => Object.hash(runtimeType,accountId,accountCurrency,campaignId);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.getCampaignInfo(accountId: $accountId, accountCurrency: $accountCurrency, campaignId: $campaignId)';
}


}

/// @nodoc
abstract mixin class _$GetCampaignInfoCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$GetCampaignInfoCopyWith(_GetCampaignInfo value, $Res Function(_GetCampaignInfo) _then) = __$GetCampaignInfoCopyWithImpl;
@useResult
$Res call({
 String accountId, String accountCurrency, String campaignId
});




}
/// @nodoc
class __$GetCampaignInfoCopyWithImpl<$Res>
    implements _$GetCampaignInfoCopyWith<$Res> {
  __$GetCampaignInfoCopyWithImpl(this._self, this._then);

  final _GetCampaignInfo _self;
  final $Res Function(_GetCampaignInfo) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? accountId = null,Object? accountCurrency = null,Object? campaignId = null,}) {
  return _then(_GetCampaignInfo(
accountId: null == accountId ? _self.accountId : accountId // ignore: cast_nullable_to_non_nullable
as String,accountCurrency: null == accountCurrency ? _self.accountCurrency : accountCurrency // ignore: cast_nullable_to_non_nullable
as String,campaignId: null == campaignId ? _self.campaignId : campaignId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _ChangeCampaignResultState implements DepositAccountsAndAmountEvent {
  const _ChangeCampaignResultState({required this.campaignResultState, this.campaignId});
  

 final  CampaignResultState campaignResultState;
 final  String? campaignId;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ChangeCampaignResultStateCopyWith<_ChangeCampaignResultState> get copyWith => __$ChangeCampaignResultStateCopyWithImpl<_ChangeCampaignResultState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ChangeCampaignResultState&&(identical(other.campaignResultState, campaignResultState) || other.campaignResultState == campaignResultState)&&(identical(other.campaignId, campaignId) || other.campaignId == campaignId));
}


@override
int get hashCode => Object.hash(runtimeType,campaignResultState,campaignId);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.changeCampaignResultState(campaignResultState: $campaignResultState, campaignId: $campaignId)';
}


}

/// @nodoc
abstract mixin class _$ChangeCampaignResultStateCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$ChangeCampaignResultStateCopyWith(_ChangeCampaignResultState value, $Res Function(_ChangeCampaignResultState) _then) = __$ChangeCampaignResultStateCopyWithImpl;
@useResult
$Res call({
 CampaignResultState campaignResultState, String? campaignId
});




}
/// @nodoc
class __$ChangeCampaignResultStateCopyWithImpl<$Res>
    implements _$ChangeCampaignResultStateCopyWith<$Res> {
  __$ChangeCampaignResultStateCopyWithImpl(this._self, this._then);

  final _ChangeCampaignResultState _self;
  final $Res Function(_ChangeCampaignResultState) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? campaignResultState = null,Object? campaignId = freezed,}) {
  return _then(_ChangeCampaignResultState(
campaignResultState: null == campaignResultState ? _self.campaignResultState : campaignResultState // ignore: cast_nullable_to_non_nullable
as CampaignResultState,campaignId: freezed == campaignId ? _self.campaignId : campaignId // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class _InitArgs implements DepositAccountsAndAmountEvent {
  const _InitArgs({this.maxPollingAttempts, this.pollingFrequencySeconds, this.depositFlowConfig, this.leanAccountArgs});
  

 final  num? maxPollingAttempts;
 final  num? pollingFrequencySeconds;
 final  DepositFlowConfig? depositFlowConfig;
 final  LeanAccountArgs? leanAccountArgs;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$InitArgsCopyWith<_InitArgs> get copyWith => __$InitArgsCopyWithImpl<_InitArgs>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _InitArgs&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.leanAccountArgs, leanAccountArgs) || other.leanAccountArgs == leanAccountArgs));
}


@override
int get hashCode => Object.hash(runtimeType,maxPollingAttempts,pollingFrequencySeconds,depositFlowConfig,leanAccountArgs);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.initArgs(maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds, depositFlowConfig: $depositFlowConfig, leanAccountArgs: $leanAccountArgs)';
}


}

/// @nodoc
abstract mixin class _$InitArgsCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$InitArgsCopyWith(_InitArgs value, $Res Function(_InitArgs) _then) = __$InitArgsCopyWithImpl;
@useResult
$Res call({
 num? maxPollingAttempts, num? pollingFrequencySeconds, DepositFlowConfig? depositFlowConfig, LeanAccountArgs? leanAccountArgs
});


$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;$LeanAccountArgsCopyWith<$Res>? get leanAccountArgs;

}
/// @nodoc
class __$InitArgsCopyWithImpl<$Res>
    implements _$InitArgsCopyWith<$Res> {
  __$InitArgsCopyWithImpl(this._self, this._then);

  final _InitArgs _self;
  final $Res Function(_InitArgs) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,Object? depositFlowConfig = freezed,Object? leanAccountArgs = freezed,}) {
  return _then(_InitArgs(
maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,leanAccountArgs: freezed == leanAccountArgs ? _self.leanAccountArgs : leanAccountArgs // ignore: cast_nullable_to_non_nullable
as LeanAccountArgs?,
  ));
}

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountArgsCopyWith<$Res>? get leanAccountArgs {
    if (_self.leanAccountArgs == null) {
    return null;
  }

  return $LeanAccountArgsCopyWith<$Res>(_self.leanAccountArgs!, (value) {
    return _then(_self.copyWith(leanAccountArgs: value));
  });
}
}

/// @nodoc


class _OnLeanPaymentCallback implements DepositAccountsAndAmountEvent {
  const _OnLeanPaymentCallback({required this.response, required this.transactionId, required this.paymentIntentId});
  

 final  LeanResponse response;
 final  String transactionId;
 final  String paymentIntentId;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$OnLeanPaymentCallbackCopyWith<_OnLeanPaymentCallback> get copyWith => __$OnLeanPaymentCallbackCopyWithImpl<_OnLeanPaymentCallback>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _OnLeanPaymentCallback&&(identical(other.response, response) || other.response == response)&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paymentIntentId, paymentIntentId) || other.paymentIntentId == paymentIntentId));
}


@override
int get hashCode => Object.hash(runtimeType,response,transactionId,paymentIntentId);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.onLeanPaymentCallback(response: $response, transactionId: $transactionId, paymentIntentId: $paymentIntentId)';
}


}

/// @nodoc
abstract mixin class _$OnLeanPaymentCallbackCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$OnLeanPaymentCallbackCopyWith(_OnLeanPaymentCallback value, $Res Function(_OnLeanPaymentCallback) _then) = __$OnLeanPaymentCallbackCopyWithImpl;
@useResult
$Res call({
 LeanResponse response, String transactionId, String paymentIntentId
});




}
/// @nodoc
class __$OnLeanPaymentCallbackCopyWithImpl<$Res>
    implements _$OnLeanPaymentCallbackCopyWith<$Res> {
  __$OnLeanPaymentCallbackCopyWithImpl(this._self, this._then);

  final _OnLeanPaymentCallback _self;
  final $Res Function(_OnLeanPaymentCallback) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? response = null,Object? transactionId = null,Object? paymentIntentId = null,}) {
  return _then(_OnLeanPaymentCallback(
response: null == response ? _self.response : response // ignore: cast_nullable_to_non_nullable
as LeanResponse,transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,paymentIntentId: null == paymentIntentId ? _self.paymentIntentId : paymentIntentId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SetCampaignId implements DepositAccountsAndAmountEvent {
  const _SetCampaignId({required this.campaignId});
  

 final  String campaignId;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetCampaignIdCopyWith<_SetCampaignId> get copyWith => __$SetCampaignIdCopyWithImpl<_SetCampaignId>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetCampaignId&&(identical(other.campaignId, campaignId) || other.campaignId == campaignId));
}


@override
int get hashCode => Object.hash(runtimeType,campaignId);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.setCampaignId(campaignId: $campaignId)';
}


}

/// @nodoc
abstract mixin class _$SetCampaignIdCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$SetCampaignIdCopyWith(_SetCampaignId value, $Res Function(_SetCampaignId) _then) = __$SetCampaignIdCopyWithImpl;
@useResult
$Res call({
 String campaignId
});




}
/// @nodoc
class __$SetCampaignIdCopyWithImpl<$Res>
    implements _$SetCampaignIdCopyWith<$Res> {
  __$SetCampaignIdCopyWithImpl(this._self, this._then);

  final _SetCampaignId _self;
  final $Res Function(_SetCampaignId) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? campaignId = null,}) {
  return _then(_SetCampaignId(
campaignId: null == campaignId ? _self.campaignId : campaignId // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc


class _SetPreviousSelectedCurrency implements DepositAccountsAndAmountEvent {
  const _SetPreviousSelectedCurrency({required this.previousSelectedCurrency});
  

 final  String previousSelectedCurrency;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SetPreviousSelectedCurrencyCopyWith<_SetPreviousSelectedCurrency> get copyWith => __$SetPreviousSelectedCurrencyCopyWithImpl<_SetPreviousSelectedCurrency>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SetPreviousSelectedCurrency&&(identical(other.previousSelectedCurrency, previousSelectedCurrency) || other.previousSelectedCurrency == previousSelectedCurrency));
}


@override
int get hashCode => Object.hash(runtimeType,previousSelectedCurrency);

@override
String toString() {
  return 'DepositAccountsAndAmountEvent.setPreviousSelectedCurrency(previousSelectedCurrency: $previousSelectedCurrency)';
}


}

/// @nodoc
abstract mixin class _$SetPreviousSelectedCurrencyCopyWith<$Res> implements $DepositAccountsAndAmountEventCopyWith<$Res> {
  factory _$SetPreviousSelectedCurrencyCopyWith(_SetPreviousSelectedCurrency value, $Res Function(_SetPreviousSelectedCurrency) _then) = __$SetPreviousSelectedCurrencyCopyWithImpl;
@useResult
$Res call({
 String previousSelectedCurrency
});




}
/// @nodoc
class __$SetPreviousSelectedCurrencyCopyWithImpl<$Res>
    implements _$SetPreviousSelectedCurrencyCopyWith<$Res> {
  __$SetPreviousSelectedCurrencyCopyWithImpl(this._self, this._then);

  final _SetPreviousSelectedCurrency _self;
  final $Res Function(_SetPreviousSelectedCurrency) _then;

/// Create a copy of DepositAccountsAndAmountEvent
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? previousSelectedCurrency = null,}) {
  return _then(_SetPreviousSelectedCurrency(
previousSelectedCurrency: null == previousSelectedCurrency ? _self.previousSelectedCurrency : previousSelectedCurrency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$DepositAccountsAndAmountState {

 TradingAccountModel? get selectedAccount; bool get isButtonEnabled; String get accountCurrencyAmount; String get selectedCurrencyAmount; RatesModel? get ratesModel; bool get isButtonLoading; String get conversionRateString; String get selectedCurrency; DepositAccountsAndAmountProcessState get processState; String? get errorMessage; bool get isFullPageLoadingEnabled; CampaignsState get campaignsState; CampaignInfoState get campaignInfoState; String get campaignIdForApi; String get campaignId; CampaignResultState get campaignState; num? get maxPollingAttempts; num? get pollingFrequencySeconds; DepositFlowConfig? get depositFlowConfig; LeanAccountArgs? get leanAccountArgs; String get previousSelectedCurrency;
/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountStateCopyWith<DepositAccountsAndAmountState> get copyWith => _$DepositAccountsAndAmountStateCopyWithImpl<DepositAccountsAndAmountState>(this as DepositAccountsAndAmountState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.accountCurrencyAmount, accountCurrencyAmount) || other.accountCurrencyAmount == accountCurrencyAmount)&&(identical(other.selectedCurrencyAmount, selectedCurrencyAmount) || other.selectedCurrencyAmount == selectedCurrencyAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.selectedCurrency, selectedCurrency) || other.selectedCurrency == selectedCurrency)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isFullPageLoadingEnabled, isFullPageLoadingEnabled) || other.isFullPageLoadingEnabled == isFullPageLoadingEnabled)&&(identical(other.campaignsState, campaignsState) || other.campaignsState == campaignsState)&&(identical(other.campaignInfoState, campaignInfoState) || other.campaignInfoState == campaignInfoState)&&(identical(other.campaignIdForApi, campaignIdForApi) || other.campaignIdForApi == campaignIdForApi)&&(identical(other.campaignId, campaignId) || other.campaignId == campaignId)&&(identical(other.campaignState, campaignState) || other.campaignState == campaignState)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.leanAccountArgs, leanAccountArgs) || other.leanAccountArgs == leanAccountArgs)&&(identical(other.previousSelectedCurrency, previousSelectedCurrency) || other.previousSelectedCurrency == previousSelectedCurrency));
}


@override
int get hashCode => Object.hashAll([runtimeType,selectedAccount,isButtonEnabled,accountCurrencyAmount,selectedCurrencyAmount,ratesModel,isButtonLoading,conversionRateString,selectedCurrency,processState,errorMessage,isFullPageLoadingEnabled,campaignsState,campaignInfoState,campaignIdForApi,campaignId,campaignState,maxPollingAttempts,pollingFrequencySeconds,depositFlowConfig,leanAccountArgs,previousSelectedCurrency]);

@override
String toString() {
  return 'DepositAccountsAndAmountState(selectedAccount: $selectedAccount, isButtonEnabled: $isButtonEnabled, accountCurrencyAmount: $accountCurrencyAmount, selectedCurrencyAmount: $selectedCurrencyAmount, ratesModel: $ratesModel, isButtonLoading: $isButtonLoading, conversionRateString: $conversionRateString, selectedCurrency: $selectedCurrency, processState: $processState, errorMessage: $errorMessage, isFullPageLoadingEnabled: $isFullPageLoadingEnabled, campaignsState: $campaignsState, campaignInfoState: $campaignInfoState, campaignIdForApi: $campaignIdForApi, campaignId: $campaignId, campaignState: $campaignState, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds, depositFlowConfig: $depositFlowConfig, leanAccountArgs: $leanAccountArgs, previousSelectedCurrency: $previousSelectedCurrency)';
}


}

/// @nodoc
abstract mixin class $DepositAccountsAndAmountStateCopyWith<$Res>  {
  factory $DepositAccountsAndAmountStateCopyWith(DepositAccountsAndAmountState value, $Res Function(DepositAccountsAndAmountState) _then) = _$DepositAccountsAndAmountStateCopyWithImpl;
@useResult
$Res call({
 TradingAccountModel? selectedAccount, bool isButtonEnabled, String accountCurrencyAmount, String selectedCurrencyAmount, RatesModel? ratesModel, bool isButtonLoading, String conversionRateString, String selectedCurrency, DepositAccountsAndAmountProcessState processState, String? errorMessage, bool isFullPageLoadingEnabled, CampaignsState campaignsState, CampaignInfoState campaignInfoState, String campaignIdForApi, String campaignId, CampaignResultState campaignState, num? maxPollingAttempts, num? pollingFrequencySeconds, DepositFlowConfig? depositFlowConfig, LeanAccountArgs? leanAccountArgs, String previousSelectedCurrency
});


$TradingAccountModelCopyWith<$Res>? get selectedAccount;$RatesModelCopyWith<$Res>? get ratesModel;$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState;$CampaignsStateCopyWith<$Res> get campaignsState;$CampaignInfoStateCopyWith<$Res> get campaignInfoState;$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;$LeanAccountArgsCopyWith<$Res>? get leanAccountArgs;

}
/// @nodoc
class _$DepositAccountsAndAmountStateCopyWithImpl<$Res>
    implements $DepositAccountsAndAmountStateCopyWith<$Res> {
  _$DepositAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final DepositAccountsAndAmountState _self;
  final $Res Function(DepositAccountsAndAmountState) _then;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? selectedAccount = freezed,Object? isButtonEnabled = null,Object? accountCurrencyAmount = null,Object? selectedCurrencyAmount = null,Object? ratesModel = freezed,Object? isButtonLoading = null,Object? conversionRateString = null,Object? selectedCurrency = null,Object? processState = null,Object? errorMessage = freezed,Object? isFullPageLoadingEnabled = null,Object? campaignsState = null,Object? campaignInfoState = null,Object? campaignIdForApi = null,Object? campaignId = null,Object? campaignState = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,Object? depositFlowConfig = freezed,Object? leanAccountArgs = freezed,Object? previousSelectedCurrency = null,}) {
  return _then(_self.copyWith(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,accountCurrencyAmount: null == accountCurrencyAmount ? _self.accountCurrencyAmount : accountCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,selectedCurrencyAmount: null == selectedCurrencyAmount ? _self.selectedCurrencyAmount : selectedCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,selectedCurrency: null == selectedCurrency ? _self.selectedCurrency : selectedCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as DepositAccountsAndAmountProcessState,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isFullPageLoadingEnabled: null == isFullPageLoadingEnabled ? _self.isFullPageLoadingEnabled : isFullPageLoadingEnabled // ignore: cast_nullable_to_non_nullable
as bool,campaignsState: null == campaignsState ? _self.campaignsState : campaignsState // ignore: cast_nullable_to_non_nullable
as CampaignsState,campaignInfoState: null == campaignInfoState ? _self.campaignInfoState : campaignInfoState // ignore: cast_nullable_to_non_nullable
as CampaignInfoState,campaignIdForApi: null == campaignIdForApi ? _self.campaignIdForApi : campaignIdForApi // ignore: cast_nullable_to_non_nullable
as String,campaignId: null == campaignId ? _self.campaignId : campaignId // ignore: cast_nullable_to_non_nullable
as String,campaignState: null == campaignState ? _self.campaignState : campaignState // ignore: cast_nullable_to_non_nullable
as CampaignResultState,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,leanAccountArgs: freezed == leanAccountArgs ? _self.leanAccountArgs : leanAccountArgs // ignore: cast_nullable_to_non_nullable
as LeanAccountArgs?,previousSelectedCurrency: null == previousSelectedCurrency ? _self.previousSelectedCurrency : previousSelectedCurrency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}
/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState {
  
  return $DepositAccountsAndAmountProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsStateCopyWith<$Res> get campaignsState {
  
  return $CampaignsStateCopyWith<$Res>(_self.campaignsState, (value) {
    return _then(_self.copyWith(campaignsState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignInfoStateCopyWith<$Res> get campaignInfoState {
  
  return $CampaignInfoStateCopyWith<$Res>(_self.campaignInfoState, (value) {
    return _then(_self.copyWith(campaignInfoState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountArgsCopyWith<$Res>? get leanAccountArgs {
    if (_self.leanAccountArgs == null) {
    return null;
  }

  return $LeanAccountArgsCopyWith<$Res>(_self.leanAccountArgs!, (value) {
    return _then(_self.copyWith(leanAccountArgs: value));
  });
}
}


/// @nodoc


class _DepositAccountsAndAmountState implements DepositAccountsAndAmountState {
  const _DepositAccountsAndAmountState({this.selectedAccount, this.isButtonEnabled = false, this.accountCurrencyAmount = '', this.selectedCurrencyAmount = '', this.ratesModel = null, this.isButtonLoading = false, this.conversionRateString = '', this.selectedCurrency = '', this.processState = const LoadedState(), this.errorMessage = null, this.isFullPageLoadingEnabled = false, this.campaignsState = const CampaignsState.loading(), this.campaignInfoState = const CampaignInfoState.loading(), this.campaignIdForApi = "", this.campaignId = "", this.campaignState = CampaignResultState.idle, this.maxPollingAttempts = null, this.pollingFrequencySeconds = null, this.depositFlowConfig = null, this.leanAccountArgs = null, this.previousSelectedCurrency = ""});
  

@override final  TradingAccountModel? selectedAccount;
@override@JsonKey() final  bool isButtonEnabled;
@override@JsonKey() final  String accountCurrencyAmount;
@override@JsonKey() final  String selectedCurrencyAmount;
@override@JsonKey() final  RatesModel? ratesModel;
@override@JsonKey() final  bool isButtonLoading;
@override@JsonKey() final  String conversionRateString;
@override@JsonKey() final  String selectedCurrency;
@override@JsonKey() final  DepositAccountsAndAmountProcessState processState;
@override@JsonKey() final  String? errorMessage;
@override@JsonKey() final  bool isFullPageLoadingEnabled;
@override@JsonKey() final  CampaignsState campaignsState;
@override@JsonKey() final  CampaignInfoState campaignInfoState;
@override@JsonKey() final  String campaignIdForApi;
@override@JsonKey() final  String campaignId;
@override@JsonKey() final  CampaignResultState campaignState;
@override@JsonKey() final  num? maxPollingAttempts;
@override@JsonKey() final  num? pollingFrequencySeconds;
@override@JsonKey() final  DepositFlowConfig? depositFlowConfig;
@override@JsonKey() final  LeanAccountArgs? leanAccountArgs;
@override@JsonKey() final  String previousSelectedCurrency;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepositAccountsAndAmountStateCopyWith<_DepositAccountsAndAmountState> get copyWith => __$DepositAccountsAndAmountStateCopyWithImpl<_DepositAccountsAndAmountState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _DepositAccountsAndAmountState&&(identical(other.selectedAccount, selectedAccount) || other.selectedAccount == selectedAccount)&&(identical(other.isButtonEnabled, isButtonEnabled) || other.isButtonEnabled == isButtonEnabled)&&(identical(other.accountCurrencyAmount, accountCurrencyAmount) || other.accountCurrencyAmount == accountCurrencyAmount)&&(identical(other.selectedCurrencyAmount, selectedCurrencyAmount) || other.selectedCurrencyAmount == selectedCurrencyAmount)&&(identical(other.ratesModel, ratesModel) || other.ratesModel == ratesModel)&&(identical(other.isButtonLoading, isButtonLoading) || other.isButtonLoading == isButtonLoading)&&(identical(other.conversionRateString, conversionRateString) || other.conversionRateString == conversionRateString)&&(identical(other.selectedCurrency, selectedCurrency) || other.selectedCurrency == selectedCurrency)&&(identical(other.processState, processState) || other.processState == processState)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isFullPageLoadingEnabled, isFullPageLoadingEnabled) || other.isFullPageLoadingEnabled == isFullPageLoadingEnabled)&&(identical(other.campaignsState, campaignsState) || other.campaignsState == campaignsState)&&(identical(other.campaignInfoState, campaignInfoState) || other.campaignInfoState == campaignInfoState)&&(identical(other.campaignIdForApi, campaignIdForApi) || other.campaignIdForApi == campaignIdForApi)&&(identical(other.campaignId, campaignId) || other.campaignId == campaignId)&&(identical(other.campaignState, campaignState) || other.campaignState == campaignState)&&(identical(other.maxPollingAttempts, maxPollingAttempts) || other.maxPollingAttempts == maxPollingAttempts)&&(identical(other.pollingFrequencySeconds, pollingFrequencySeconds) || other.pollingFrequencySeconds == pollingFrequencySeconds)&&(identical(other.depositFlowConfig, depositFlowConfig) || other.depositFlowConfig == depositFlowConfig)&&(identical(other.leanAccountArgs, leanAccountArgs) || other.leanAccountArgs == leanAccountArgs)&&(identical(other.previousSelectedCurrency, previousSelectedCurrency) || other.previousSelectedCurrency == previousSelectedCurrency));
}


@override
int get hashCode => Object.hashAll([runtimeType,selectedAccount,isButtonEnabled,accountCurrencyAmount,selectedCurrencyAmount,ratesModel,isButtonLoading,conversionRateString,selectedCurrency,processState,errorMessage,isFullPageLoadingEnabled,campaignsState,campaignInfoState,campaignIdForApi,campaignId,campaignState,maxPollingAttempts,pollingFrequencySeconds,depositFlowConfig,leanAccountArgs,previousSelectedCurrency]);

@override
String toString() {
  return 'DepositAccountsAndAmountState(selectedAccount: $selectedAccount, isButtonEnabled: $isButtonEnabled, accountCurrencyAmount: $accountCurrencyAmount, selectedCurrencyAmount: $selectedCurrencyAmount, ratesModel: $ratesModel, isButtonLoading: $isButtonLoading, conversionRateString: $conversionRateString, selectedCurrency: $selectedCurrency, processState: $processState, errorMessage: $errorMessage, isFullPageLoadingEnabled: $isFullPageLoadingEnabled, campaignsState: $campaignsState, campaignInfoState: $campaignInfoState, campaignIdForApi: $campaignIdForApi, campaignId: $campaignId, campaignState: $campaignState, maxPollingAttempts: $maxPollingAttempts, pollingFrequencySeconds: $pollingFrequencySeconds, depositFlowConfig: $depositFlowConfig, leanAccountArgs: $leanAccountArgs, previousSelectedCurrency: $previousSelectedCurrency)';
}


}

/// @nodoc
abstract mixin class _$DepositAccountsAndAmountStateCopyWith<$Res> implements $DepositAccountsAndAmountStateCopyWith<$Res> {
  factory _$DepositAccountsAndAmountStateCopyWith(_DepositAccountsAndAmountState value, $Res Function(_DepositAccountsAndAmountState) _then) = __$DepositAccountsAndAmountStateCopyWithImpl;
@override @useResult
$Res call({
 TradingAccountModel? selectedAccount, bool isButtonEnabled, String accountCurrencyAmount, String selectedCurrencyAmount, RatesModel? ratesModel, bool isButtonLoading, String conversionRateString, String selectedCurrency, DepositAccountsAndAmountProcessState processState, String? errorMessage, bool isFullPageLoadingEnabled, CampaignsState campaignsState, CampaignInfoState campaignInfoState, String campaignIdForApi, String campaignId, CampaignResultState campaignState, num? maxPollingAttempts, num? pollingFrequencySeconds, DepositFlowConfig? depositFlowConfig, LeanAccountArgs? leanAccountArgs, String previousSelectedCurrency
});


@override $TradingAccountModelCopyWith<$Res>? get selectedAccount;@override $RatesModelCopyWith<$Res>? get ratesModel;@override $DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState;@override $CampaignsStateCopyWith<$Res> get campaignsState;@override $CampaignInfoStateCopyWith<$Res> get campaignInfoState;@override $DepositFlowConfigCopyWith<$Res>? get depositFlowConfig;@override $LeanAccountArgsCopyWith<$Res>? get leanAccountArgs;

}
/// @nodoc
class __$DepositAccountsAndAmountStateCopyWithImpl<$Res>
    implements _$DepositAccountsAndAmountStateCopyWith<$Res> {
  __$DepositAccountsAndAmountStateCopyWithImpl(this._self, this._then);

  final _DepositAccountsAndAmountState _self;
  final $Res Function(_DepositAccountsAndAmountState) _then;

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? selectedAccount = freezed,Object? isButtonEnabled = null,Object? accountCurrencyAmount = null,Object? selectedCurrencyAmount = null,Object? ratesModel = freezed,Object? isButtonLoading = null,Object? conversionRateString = null,Object? selectedCurrency = null,Object? processState = null,Object? errorMessage = freezed,Object? isFullPageLoadingEnabled = null,Object? campaignsState = null,Object? campaignInfoState = null,Object? campaignIdForApi = null,Object? campaignId = null,Object? campaignState = null,Object? maxPollingAttempts = freezed,Object? pollingFrequencySeconds = freezed,Object? depositFlowConfig = freezed,Object? leanAccountArgs = freezed,Object? previousSelectedCurrency = null,}) {
  return _then(_DepositAccountsAndAmountState(
selectedAccount: freezed == selectedAccount ? _self.selectedAccount : selectedAccount // ignore: cast_nullable_to_non_nullable
as TradingAccountModel?,isButtonEnabled: null == isButtonEnabled ? _self.isButtonEnabled : isButtonEnabled // ignore: cast_nullable_to_non_nullable
as bool,accountCurrencyAmount: null == accountCurrencyAmount ? _self.accountCurrencyAmount : accountCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,selectedCurrencyAmount: null == selectedCurrencyAmount ? _self.selectedCurrencyAmount : selectedCurrencyAmount // ignore: cast_nullable_to_non_nullable
as String,ratesModel: freezed == ratesModel ? _self.ratesModel : ratesModel // ignore: cast_nullable_to_non_nullable
as RatesModel?,isButtonLoading: null == isButtonLoading ? _self.isButtonLoading : isButtonLoading // ignore: cast_nullable_to_non_nullable
as bool,conversionRateString: null == conversionRateString ? _self.conversionRateString : conversionRateString // ignore: cast_nullable_to_non_nullable
as String,selectedCurrency: null == selectedCurrency ? _self.selectedCurrency : selectedCurrency // ignore: cast_nullable_to_non_nullable
as String,processState: null == processState ? _self.processState : processState // ignore: cast_nullable_to_non_nullable
as DepositAccountsAndAmountProcessState,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isFullPageLoadingEnabled: null == isFullPageLoadingEnabled ? _self.isFullPageLoadingEnabled : isFullPageLoadingEnabled // ignore: cast_nullable_to_non_nullable
as bool,campaignsState: null == campaignsState ? _self.campaignsState : campaignsState // ignore: cast_nullable_to_non_nullable
as CampaignsState,campaignInfoState: null == campaignInfoState ? _self.campaignInfoState : campaignInfoState // ignore: cast_nullable_to_non_nullable
as CampaignInfoState,campaignIdForApi: null == campaignIdForApi ? _self.campaignIdForApi : campaignIdForApi // ignore: cast_nullable_to_non_nullable
as String,campaignId: null == campaignId ? _self.campaignId : campaignId // ignore: cast_nullable_to_non_nullable
as String,campaignState: null == campaignState ? _self.campaignState : campaignState // ignore: cast_nullable_to_non_nullable
as CampaignResultState,maxPollingAttempts: freezed == maxPollingAttempts ? _self.maxPollingAttempts : maxPollingAttempts // ignore: cast_nullable_to_non_nullable
as num?,pollingFrequencySeconds: freezed == pollingFrequencySeconds ? _self.pollingFrequencySeconds : pollingFrequencySeconds // ignore: cast_nullable_to_non_nullable
as num?,depositFlowConfig: freezed == depositFlowConfig ? _self.depositFlowConfig : depositFlowConfig // ignore: cast_nullable_to_non_nullable
as DepositFlowConfig?,leanAccountArgs: freezed == leanAccountArgs ? _self.leanAccountArgs : leanAccountArgs // ignore: cast_nullable_to_non_nullable
as LeanAccountArgs?,previousSelectedCurrency: null == previousSelectedCurrency ? _self.previousSelectedCurrency : previousSelectedCurrency // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<$Res>? get selectedAccount {
    if (_self.selectedAccount == null) {
    return null;
  }

  return $TradingAccountModelCopyWith<$Res>(_self.selectedAccount!, (value) {
    return _then(_self.copyWith(selectedAccount: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$RatesModelCopyWith<$Res>? get ratesModel {
    if (_self.ratesModel == null) {
    return null;
  }

  return $RatesModelCopyWith<$Res>(_self.ratesModel!, (value) {
    return _then(_self.copyWith(ratesModel: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositAccountsAndAmountProcessStateCopyWith<$Res> get processState {
  
  return $DepositAccountsAndAmountProcessStateCopyWith<$Res>(_self.processState, (value) {
    return _then(_self.copyWith(processState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsStateCopyWith<$Res> get campaignsState {
  
  return $CampaignsStateCopyWith<$Res>(_self.campaignsState, (value) {
    return _then(_self.copyWith(campaignsState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignInfoStateCopyWith<$Res> get campaignInfoState {
  
  return $CampaignInfoStateCopyWith<$Res>(_self.campaignInfoState, (value) {
    return _then(_self.copyWith(campaignInfoState: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$DepositFlowConfigCopyWith<$Res>? get depositFlowConfig {
    if (_self.depositFlowConfig == null) {
    return null;
  }

  return $DepositFlowConfigCopyWith<$Res>(_self.depositFlowConfig!, (value) {
    return _then(_self.copyWith(depositFlowConfig: value));
  });
}/// Create a copy of DepositAccountsAndAmountState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanAccountArgsCopyWith<$Res>? get leanAccountArgs {
    if (_self.leanAccountArgs == null) {
    return null;
  }

  return $LeanAccountArgsCopyWith<$Res>(_self.leanAccountArgs!, (value) {
    return _then(_self.copyWith(leanAccountArgs: value));
  });
}
}

/// @nodoc
mixin _$DepositAccountsAndAmountProcessState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is DepositAccountsAndAmountProcessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState()';
}


}

/// @nodoc
class $DepositAccountsAndAmountProcessStateCopyWith<$Res>  {
$DepositAccountsAndAmountProcessStateCopyWith(DepositAccountsAndAmountProcessState _, $Res Function(DepositAccountsAndAmountProcessState) __);
}


/// @nodoc


class LoadedState implements DepositAccountsAndAmountProcessState {
  const LoadedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LoadedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.loaded()';
}


}




/// @nodoc


class ErrorState implements DepositAccountsAndAmountProcessState {
  const ErrorState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ErrorState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.error()';
}


}




/// @nodoc


class PaymentFailedState implements DepositAccountsAndAmountProcessState {
  const PaymentFailedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentFailedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentFailed()';
}


}




/// @nodoc


class PaymentRejectedState implements DepositAccountsAndAmountProcessState {
  const PaymentRejectedState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentRejectedState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentRejected()';
}


}




/// @nodoc


class PaymentSuccessState implements DepositAccountsAndAmountProcessState {
  const PaymentSuccessState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentSuccessState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentSuccess()';
}


}




/// @nodoc


class PaymentPendingState implements DepositAccountsAndAmountProcessState {
  const PaymentPendingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaymentPendingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.paymentPending()';
}


}




/// @nodoc


class StartLeanPaymentState implements DepositAccountsAndAmountProcessState {
  const StartLeanPaymentState({required this.transactionId, required this.paymentIntentId, this.leanMetaData = null});
  

 final  String transactionId;
 final  String paymentIntentId;
@JsonKey() final  LeanMetaDataDeposit? leanMetaData;

/// Create a copy of DepositAccountsAndAmountProcessState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$StartLeanPaymentStateCopyWith<StartLeanPaymentState> get copyWith => _$StartLeanPaymentStateCopyWithImpl<StartLeanPaymentState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is StartLeanPaymentState&&(identical(other.transactionId, transactionId) || other.transactionId == transactionId)&&(identical(other.paymentIntentId, paymentIntentId) || other.paymentIntentId == paymentIntentId)&&(identical(other.leanMetaData, leanMetaData) || other.leanMetaData == leanMetaData));
}


@override
int get hashCode => Object.hash(runtimeType,transactionId,paymentIntentId,leanMetaData);

@override
String toString() {
  return 'DepositAccountsAndAmountProcessState.startLeanPayment(transactionId: $transactionId, paymentIntentId: $paymentIntentId, leanMetaData: $leanMetaData)';
}


}

/// @nodoc
abstract mixin class $StartLeanPaymentStateCopyWith<$Res> implements $DepositAccountsAndAmountProcessStateCopyWith<$Res> {
  factory $StartLeanPaymentStateCopyWith(StartLeanPaymentState value, $Res Function(StartLeanPaymentState) _then) = _$StartLeanPaymentStateCopyWithImpl;
@useResult
$Res call({
 String transactionId, String paymentIntentId, LeanMetaDataDeposit? leanMetaData
});


$LeanMetaDataDepositCopyWith<$Res>? get leanMetaData;

}
/// @nodoc
class _$StartLeanPaymentStateCopyWithImpl<$Res>
    implements $StartLeanPaymentStateCopyWith<$Res> {
  _$StartLeanPaymentStateCopyWithImpl(this._self, this._then);

  final StartLeanPaymentState _self;
  final $Res Function(StartLeanPaymentState) _then;

/// Create a copy of DepositAccountsAndAmountProcessState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? transactionId = null,Object? paymentIntentId = null,Object? leanMetaData = freezed,}) {
  return _then(StartLeanPaymentState(
transactionId: null == transactionId ? _self.transactionId : transactionId // ignore: cast_nullable_to_non_nullable
as String,paymentIntentId: null == paymentIntentId ? _self.paymentIntentId : paymentIntentId // ignore: cast_nullable_to_non_nullable
as String,leanMetaData: freezed == leanMetaData ? _self.leanMetaData : leanMetaData // ignore: cast_nullable_to_non_nullable
as LeanMetaDataDeposit?,
  ));
}

/// Create a copy of DepositAccountsAndAmountProcessState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LeanMetaDataDepositCopyWith<$Res>? get leanMetaData {
    if (_self.leanMetaData == null) {
    return null;
  }

  return $LeanMetaDataDepositCopyWith<$Res>(_self.leanMetaData!, (value) {
    return _then(_self.copyWith(leanMetaData: value));
  });
}
}

/// @nodoc
mixin _$CampaignsState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState()';
}


}

/// @nodoc
class $CampaignsStateCopyWith<$Res>  {
$CampaignsStateCopyWith(CampaignsState _, $Res Function(CampaignsState) __);
}


/// @nodoc


class CampaignsLoadingState implements CampaignsState {
  const CampaignsLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState.loading()';
}


}




/// @nodoc


class CampaignsSuccessState implements CampaignsState {
  const CampaignsSuccessState(this.campaigns);
  

 final  CampaignsModel campaigns;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsSuccessStateCopyWith<CampaignsSuccessState> get copyWith => _$CampaignsSuccessStateCopyWithImpl<CampaignsSuccessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsSuccessState&&(identical(other.campaigns, campaigns) || other.campaigns == campaigns));
}


@override
int get hashCode => Object.hash(runtimeType,campaigns);

@override
String toString() {
  return 'CampaignsState.success(campaigns: $campaigns)';
}


}

/// @nodoc
abstract mixin class $CampaignsSuccessStateCopyWith<$Res> implements $CampaignsStateCopyWith<$Res> {
  factory $CampaignsSuccessStateCopyWith(CampaignsSuccessState value, $Res Function(CampaignsSuccessState) _then) = _$CampaignsSuccessStateCopyWithImpl;
@useResult
$Res call({
 CampaignsModel campaigns
});


$CampaignsModelCopyWith<$Res> get campaigns;

}
/// @nodoc
class _$CampaignsSuccessStateCopyWithImpl<$Res>
    implements $CampaignsSuccessStateCopyWith<$Res> {
  _$CampaignsSuccessStateCopyWithImpl(this._self, this._then);

  final CampaignsSuccessState _self;
  final $Res Function(CampaignsSuccessState) _then;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? campaigns = null,}) {
  return _then(CampaignsSuccessState(
null == campaigns ? _self.campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as CampaignsModel,
  ));
}

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsModelCopyWith<$Res> get campaigns {
  
  return $CampaignsModelCopyWith<$Res>(_self.campaigns, (value) {
    return _then(_self.copyWith(campaigns: value));
  });
}
}

/// @nodoc


class CampaignsEmptyState implements CampaignsState {
  const CampaignsEmptyState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsEmptyState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignsState.empty()';
}


}




/// @nodoc


class CampaignsErrorState implements CampaignsState {
  const CampaignsErrorState(this.errorMessage);
  

 final  String errorMessage;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsErrorStateCopyWith<CampaignsErrorState> get copyWith => _$CampaignsErrorStateCopyWithImpl<CampaignsErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsErrorState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'CampaignsState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $CampaignsErrorStateCopyWith<$Res> implements $CampaignsStateCopyWith<$Res> {
  factory $CampaignsErrorStateCopyWith(CampaignsErrorState value, $Res Function(CampaignsErrorState) _then) = _$CampaignsErrorStateCopyWithImpl;
@useResult
$Res call({
 String errorMessage
});




}
/// @nodoc
class _$CampaignsErrorStateCopyWithImpl<$Res>
    implements $CampaignsErrorStateCopyWith<$Res> {
  _$CampaignsErrorStateCopyWithImpl(this._self, this._then);

  final CampaignsErrorState _self;
  final $Res Function(CampaignsErrorState) _then;

/// Create a copy of CampaignsState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = null,}) {
  return _then(CampaignsErrorState(
null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

/// @nodoc
mixin _$CampaignInfoState {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignInfoState()';
}


}

/// @nodoc
class $CampaignInfoStateCopyWith<$Res>  {
$CampaignInfoStateCopyWith(CampaignInfoState _, $Res Function(CampaignInfoState) __);
}


/// @nodoc


class CampaignInfoLoadingState implements CampaignInfoState {
  const CampaignInfoLoadingState();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoLoadingState);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'CampaignInfoState.loading()';
}


}




/// @nodoc


class CampaignInfoSuccessState implements CampaignInfoState {
  const CampaignInfoSuccessState(this.campaignInfo);
  

 final  CampaignInfoModel campaignInfo;

/// Create a copy of CampaignInfoState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignInfoSuccessStateCopyWith<CampaignInfoSuccessState> get copyWith => _$CampaignInfoSuccessStateCopyWithImpl<CampaignInfoSuccessState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoSuccessState&&(identical(other.campaignInfo, campaignInfo) || other.campaignInfo == campaignInfo));
}


@override
int get hashCode => Object.hash(runtimeType,campaignInfo);

@override
String toString() {
  return 'CampaignInfoState.success(campaignInfo: $campaignInfo)';
}


}

/// @nodoc
abstract mixin class $CampaignInfoSuccessStateCopyWith<$Res> implements $CampaignInfoStateCopyWith<$Res> {
  factory $CampaignInfoSuccessStateCopyWith(CampaignInfoSuccessState value, $Res Function(CampaignInfoSuccessState) _then) = _$CampaignInfoSuccessStateCopyWithImpl;
@useResult
$Res call({
 CampaignInfoModel campaignInfo
});


$CampaignInfoModelCopyWith<$Res> get campaignInfo;

}
/// @nodoc
class _$CampaignInfoSuccessStateCopyWithImpl<$Res>
    implements $CampaignInfoSuccessStateCopyWith<$Res> {
  _$CampaignInfoSuccessStateCopyWithImpl(this._self, this._then);

  final CampaignInfoSuccessState _self;
  final $Res Function(CampaignInfoSuccessState) _then;

/// Create a copy of CampaignInfoState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? campaignInfo = null,}) {
  return _then(CampaignInfoSuccessState(
null == campaignInfo ? _self.campaignInfo : campaignInfo // ignore: cast_nullable_to_non_nullable
as CampaignInfoModel,
  ));
}

/// Create a copy of CampaignInfoState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignInfoModelCopyWith<$Res> get campaignInfo {
  
  return $CampaignInfoModelCopyWith<$Res>(_self.campaignInfo, (value) {
    return _then(_self.copyWith(campaignInfo: value));
  });
}
}

/// @nodoc


class CampaignInfoErrorState implements CampaignInfoState {
  const CampaignInfoErrorState(this.errorMessage);
  

 final  String errorMessage;

/// Create a copy of CampaignInfoState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignInfoErrorStateCopyWith<CampaignInfoErrorState> get copyWith => _$CampaignInfoErrorStateCopyWithImpl<CampaignInfoErrorState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoErrorState&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}


@override
int get hashCode => Object.hash(runtimeType,errorMessage);

@override
String toString() {
  return 'CampaignInfoState.error(errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $CampaignInfoErrorStateCopyWith<$Res> implements $CampaignInfoStateCopyWith<$Res> {
  factory $CampaignInfoErrorStateCopyWith(CampaignInfoErrorState value, $Res Function(CampaignInfoErrorState) _then) = _$CampaignInfoErrorStateCopyWithImpl;
@useResult
$Res call({
 String errorMessage
});




}
/// @nodoc
class _$CampaignInfoErrorStateCopyWithImpl<$Res>
    implements $CampaignInfoErrorStateCopyWith<$Res> {
  _$CampaignInfoErrorStateCopyWithImpl(this._self, this._then);

  final CampaignInfoErrorState _self;
  final $Res Function(CampaignInfoErrorState) _then;

/// Create a copy of CampaignInfoState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? errorMessage = null,}) {
  return _then(CampaignInfoErrorState(
null == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on
