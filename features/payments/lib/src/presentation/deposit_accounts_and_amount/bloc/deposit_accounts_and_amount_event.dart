part of 'deposit_accounts_and_amount_bloc.dart';

@freezed
sealed class DepositAccountsAndAmountEvent
    with _$DepositAccountsAndAmountEvent {
  const factory DepositAccountsAndAmountEvent.onAccountChange(
    TradingAccountModel account,
  ) = _OnAccountChange;

  // TODO (Aakash): Check if this event is required or not
  const factory DepositAccountsAndAmountEvent.naviagteToSelectedPaymentMethod(
    DepositPaymentMethod? mop,
  ) = _NaviagteToSelectedPaymentMethod;

  const factory DepositAccountsAndAmountEvent.getDepositDetails({
    required DepositPaymentMethod paymentMethod,
    required bool isDarkTheme,
  }) = _GetDepositDetails;

  const factory DepositAccountsAndAmountEvent.onAmountChange(
    String accountCurrencyAmount,
    String selectedCurrencyAmount,
    RatesModel? ratesModel,
    String? conversionRateString,
    String? selectedCurrency,
  ) = _OnAmountChange;

  const factory DepositAccountsAndAmountEvent.changeButtonState(bool isValid) =
      _ChangeButtonState;

  const factory DepositAccountsAndAmountEvent.onApplePayResult({
    required ApplePayStatus paymentStatus,
    required String? gatewayCode,
    required ApplePayUtils applePay,
  }) = _OnApplePayResult;

  const factory DepositAccountsAndAmountEvent.onGooglePayResult({
    required GooglePayStatus paymentStatus,
    required String? gatewayCode,
    required GooglePayUtils googlePay,
  }) = _OnGooglePayResult;

  const factory DepositAccountsAndAmountEvent.onPaymentMethodChange() =
      OnPaymentMethodChange;
  const factory DepositAccountsAndAmountEvent.onPaymentSuccessContinuePressed() =
      OnPaymentSuccessContinuePressed;

  const factory DepositAccountsAndAmountEvent.resetProcessState() =
      ResetProcessStateEvent;

  const factory DepositAccountsAndAmountEvent.getCampaigns() = _GetCampaigns;
  const factory DepositAccountsAndAmountEvent.getCampaignInfo({
    required String accountId,
    required String accountCurrency,
    required String campaignId,
  }) = _GetCampaignInfo;

  const factory DepositAccountsAndAmountEvent.changeCampaignResultState({
    required CampaignResultState campaignResultState,
    String? campaignId,
  }) = _ChangeCampaignResultState;
  const factory DepositAccountsAndAmountEvent.initArgs({
    num? maxPollingAttempts,
    num? pollingFrequencySeconds,
    DepositFlowConfig? depositFlowConfig,
    LeanAccountArgs? leanAccountArgs,
  }) = _InitArgs;

  const factory DepositAccountsAndAmountEvent.onLeanPaymentCallback({
    required LeanResponse response,
    required String transactionId,
    required String paymentIntentId,
  }) = _OnLeanPaymentCallback;

  const factory DepositAccountsAndAmountEvent.setCampaignId({
    required String campaignId,
  }) = _SetCampaignId;
  const factory DepositAccountsAndAmountEvent.setPreviousSelectedCurrency({
    required String previousSelectedCurrency,
  }) = _SetPreviousSelectedCurrency;
}
