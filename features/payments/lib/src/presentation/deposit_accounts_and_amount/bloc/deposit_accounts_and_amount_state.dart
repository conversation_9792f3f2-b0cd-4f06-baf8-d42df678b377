part of 'deposit_accounts_and_amount_bloc.dart';

@freezed
sealed class DepositAccountsAndAmountState
    with _$DepositAccountsAndAmountState {
  const factory DepositAccountsAndAmountState({
    TradingAccountModel? selectedAccount,
    @Default(false) bool isButtonEnabled,
    @Default('') String accountCurrencyAmount,
    @Default('') String selectedCurrencyAmount,
    @Default(null) RatesModel? ratesModel,
    @Default(false) bool isButtonLoading,
    @Default('') String conversionRateString,
    @Default('') String selectedCurrency,
    @Default(LoadedState()) DepositAccountsAndAmountProcessState processState,
    @Default(null) String? errorMessage,
    @Default(false) bool isFullPageLoadingEnabled,
    @Default(CampaignsState.loading()) CampaignsState campaignsState,
    @Default(CampaignInfoState.loading()) CampaignInfoState campaignInfoState,
    @Default("") String campaignIdForApi,
    @Default("") String campaignId,
    @Default(CampaignResultState.idle) CampaignResultState campaignState,
    @Default(null) num? maxPollingAttempts,
    @Default(null) num? pollingFrequencySeconds,
    @Default(null) DepositFlowConfig? depositFlowConfig,
    @Default(null) LeanAccountArgs? leanAccountArgs,
    @Default("") String previousSelectedCurrency,
  }) = _DepositAccountsAndAmountState;
}

@freezed
sealed class DepositAccountsAndAmountProcessState
    with _$DepositAccountsAndAmountProcessState {
  const factory DepositAccountsAndAmountProcessState.loaded() = LoadedState;
  const factory DepositAccountsAndAmountProcessState.error() = ErrorState;
  const factory DepositAccountsAndAmountProcessState.paymentFailed() =
      PaymentFailedState;
  const factory DepositAccountsAndAmountProcessState.paymentRejected() =
      PaymentRejectedState;
  const factory DepositAccountsAndAmountProcessState.paymentSuccess() =
      PaymentSuccessState;

  const factory DepositAccountsAndAmountProcessState.paymentPending() =
      PaymentPendingState;
  const factory DepositAccountsAndAmountProcessState.startLeanPayment({
    required String transactionId,
    required String paymentIntentId,
    @Default(null) LeanMetaDataDeposit? leanMetaData,
  }) = StartLeanPaymentState;
}

@freezed
sealed class CampaignsState with _$CampaignsState {
  const factory CampaignsState.loading() = CampaignsLoadingState;
  const factory CampaignsState.success(CampaignsModel campaigns) =
      CampaignsSuccessState;
  const factory CampaignsState.empty() = CampaignsEmptyState;
  const factory CampaignsState.error(String errorMessage) = CampaignsErrorState;
}

@freezed
sealed class CampaignInfoState with _$CampaignInfoState {
  const factory CampaignInfoState.loading() = CampaignInfoLoadingState;
  const factory CampaignInfoState.success(CampaignInfoModel campaignInfo) =
      CampaignInfoSuccessState;
  const factory CampaignInfoState.error(String errorMessage) =
      CampaignInfoErrorState;
}
