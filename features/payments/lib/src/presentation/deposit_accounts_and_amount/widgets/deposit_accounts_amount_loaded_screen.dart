import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:campaigns/campaigns.dart';
import 'package:payment/src/domain/model/campaign_result_state.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/widgets/campaign_alert_widget.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/widgets/campaign_page_view_widget.dart';
import 'package:prelude/prelude.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:payment/src/domain/data/deposit_mop.dart';
import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/account_list_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';

class DepositAccountsAmountLoadedScreen extends StatefulWidget {
  const DepositAccountsAmountLoadedScreen({
    super.key,
    required this.paymentMethod,
    required this.isFullScreenLoaderEnabled,
  });
  final DepositPaymentMethod paymentMethod;
  final bool isFullScreenLoaderEnabled;

  @override
  State<DepositAccountsAmountLoadedScreen> createState() =>
      _DepositAccountsAmountLoadedScreenState();
}

class _DepositAccountsAmountLoadedScreenState
    extends State<DepositAccountsAmountLoadedScreen> {
  bool _isCurrentTabEmpty =
      true; // Initialize as empty to hide button by default

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final EquitiLocalization localization = EquitiLocalization.of(context);
    return Stack(
      children: [
        Scaffold(
          backgroundColor: theme.background.bgPrimary,
          appBar: DuploAppBar(
            title:
                "${localization.payments_deposit_with} ${widget.paymentMethod.name}",
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: SingleChildScrollView(
              physics: const BouncingScrollPhysics(),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    BlocBuilder<
                      DepositAccountsAndAmountBloc,
                      DepositAccountsAndAmountState
                    >(
                      buildWhen:
                          (previous, current) =>
                              previous.campaignsState != current.campaignsState,
                      builder: (builderContext, state) {
                        switch (state.campaignsState) {
                          case CampaignsLoadingState():
                            return DuploShimmer(
                              child: Column(
                                children: [
                                  Container(
                                    height: 85,
                                    width: MediaQuery.sizeOf(context).width,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                  ),
                                  SizedBox(height: 25),
                                ],
                              ),
                            );
                          case CampaignsSuccessState(
                            :final CampaignsModel campaigns,
                          ):
                            return campaigns.data.campaigns.isNotEmpty
                                ? CampaignPageViewWidget(
                                  builderContext: builderContext,
                                  campaigns: campaigns,
                                  accountCurrency:
                                      state.selectedAccount?.homeCurrency ?? '',
                                  accountId: EquitiFormatter.formatNumber(
                                    value:
                                        state.selectedAccount?.accountIdLong ??
                                        0,
                                    locale:
                                        Localizations.localeOf(
                                          context,
                                        ).toString(),
                                  ),
                                )
                                : const SizedBox.shrink();
                          case CampaignsEmptyState():
                          case CampaignsErrorState():
                            return const SizedBox.shrink();
                        }
                      },
                    ),
                    DuploText(
                      text: localization.payments_deposit_to_account,
                      style: DuploTextStyles.of(context).textLg,
                      fontWeight: DuploFontWeight.semiBold,
                      color: theme.text.textPrimary,
                    ),
                    const SizedBox(height: 5),
                    DuploText(
                      text:
                          localization.payments_deposit_select_trading_account,
                      style: DuploTextStyles.of(context).textSm,
                      fontWeight: DuploFontWeight.regular,
                      color: theme.text.textSecondary,
                    ),
                    const SizedBox(height: 15),
                    BlocBuilder<
                      DepositAccountsAndAmountBloc,
                      DepositAccountsAndAmountState
                    >(
                      buildWhen:
                          (previous, current) =>
                              current.selectedAccount != null &&
                              current.isButtonLoading !=
                                  previous.isButtonLoading,
                      builder: (builderContext, state) {
                        return AccountListWidget(
                          args: (
                            selectByHighestBalance: false,
                            excludeAccountNumber: null,
                            onEmptyStateChanged: ({
                              required bool hasAccounts,
                              required bool hasWallets,
                              required bool isCurrentTabEmpty,
                              required int currentTabIndex,
                            }) {
                              setState(() {
                                _isCurrentTabEmpty = isCurrentTabEmpty;
                              });
                            },
                            isInputDisabled: state.isButtonLoading,
                          ),
                          onAccountSelected: (TradingAccountModel account) {
                            builderContext
                                .read<DepositAccountsAndAmountBloc>()
                                .add(
                                  DepositAccountsAndAmountEvent.onAccountChange(
                                    account,
                                  ),
                                );
                          },
                        );
                      },
                    ),
                    const SizedBox(height: 15),
                    !_isCurrentTabEmpty
                        ? BlocBuilder<
                          DepositAccountsAndAmountBloc,
                          DepositAccountsAndAmountState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.selectedAccount !=
                                      current.selectedAccount ||
                                  previous.errorMessage !=
                                      current.errorMessage ||
                                  previous.isButtonLoading !=
                                      current.isButtonLoading ||
                                  previous.previousSelectedCurrency !=
                                      current.previousSelectedCurrency,
                          builder: (builderContext, state) {
                            return state.selectedAccount != null
                                ? DepositWithdrawAmountConversionWidget(
                                  campaignAlert: CampaignAlertWidget(),
                                  args: (
                                    account: state.selectedAccount!,
                                    accountCurrency:
                                        state.selectedAccount!.homeCurrency,
                                    selectedCurrency: _getSelectedCurrency(
                                      state,
                                    ),
                                    currencyMinMaxSuggestedAmountList:
                                        widget
                                            .paymentMethod
                                            .currencyAmountDetails ??
                                        [],
                                    currencies:
                                        widget.paymentMethod.currencies ?? [],
                                    showSuggestedAmounts: true,
                                    externalErrorMessage: state.errorMessage,
                                    isStartWithConversionRate:
                                        _isStartWithConversionRate(state),
                                    paymentType: PaymentType.deposit,
                                    premierAccountMinAmountForDeposit:
                                        widget
                                            .paymentMethod
                                            .premierAccountMinAmount,
                                    isInputDisabled: state.isButtonLoading,
                                    canChangeConversionCurrency:
                                        widget.paymentMethod.mop !=
                                        DepositMop.lean,
                                  ),
                                  callback: ({
                                    required String accountCurrencyAmount,
                                    required String selectedCurrencyAmount,
                                    String? selectedCurrency,
                                    required bool isAmountValid,
                                    required RatesModel?
                                    conversionRateSelectedToAccountCurrency,
                                    String? conversionRateString,
                                    required ConversionRateModel?
                                    conversionRateData,
                                  }) {
                                    if (state
                                        .previousSelectedCurrency
                                        .isEmpty) {
                                      builderContext
                                          .read<DepositAccountsAndAmountBloc>()
                                          .add(
                                            DepositAccountsAndAmountEvent.setPreviousSelectedCurrency(
                                              previousSelectedCurrency:
                                                  selectedCurrency ?? '',
                                            ),
                                          );
                                    }

                                    builderContext
                                        .read<DepositAccountsAndAmountBloc>()
                                        .add(
                                          DepositAccountsAndAmountEvent.changeCampaignResultState(
                                            campaignResultState:
                                                CampaignResultState.idle,
                                          ),
                                        );

                                    builderContext
                                        .read<DepositAccountsAndAmountBloc>()
                                        .add(
                                          DepositAccountsAndAmountEvent.changeButtonState(
                                            isAmountValid,
                                          ),
                                        );
                                    if (isAmountValid) {
                                      builderContext
                                          .read<DepositAccountsAndAmountBloc>()
                                          .add(
                                            DepositAccountsAndAmountEvent.onAmountChange(
                                              accountCurrencyAmount,
                                              selectedCurrencyAmount,
                                              conversionRateSelectedToAccountCurrency,
                                              conversionRateString,
                                              selectedCurrency,
                                            ),
                                          );
                                    }
                                    if (state.campaignId.isNotEmpty &&
                                        state.previousSelectedCurrency !=
                                            selectedCurrency) {
                                      final locale =
                                          Localizations.localeOf(
                                            context,
                                          ).toString();
                                      builderContext
                                          .read<DepositAccountsAndAmountBloc>()
                                          .add(
                                            DepositAccountsAndAmountEvent.getCampaignInfo(
                                              accountId:
                                                  EquitiFormatter.formatNumber(
                                                    value:
                                                        state
                                                            .selectedAccount
                                                            ?.accountIdLong ??
                                                        0,
                                                    locale: locale,
                                                  ),
                                              accountCurrency:
                                                  selectedCurrency ?? '',
                                              campaignId: state.campaignId,
                                            ),
                                          );
                                    }
                                    builderContext
                                        .read<DepositAccountsAndAmountBloc>()
                                        .add(
                                          DepositAccountsAndAmountEvent.setPreviousSelectedCurrency(
                                            previousSelectedCurrency:
                                                selectedCurrency ?? '',
                                          ),
                                        );
                                  },
                                )
                                : Container();
                          },
                        )
                        : Container(),
                    const SizedBox(height: DuploSpacing.spacing_3xl_24),
                    !_isCurrentTabEmpty
                        ? BlocBuilder<
                          DepositAccountsAndAmountBloc,
                          DepositAccountsAndAmountState
                        >(
                          buildWhen:
                              (previous, current) =>
                                  previous.isButtonEnabled !=
                                      current.isButtonEnabled ||
                                  previous.isButtonLoading !=
                                      current.isButtonLoading ||
                                  previous.errorMessage != current.errorMessage,
                          builder: (builderContext, state) {
                            return switch (widget.paymentMethod.mop) {
                              DepositMop.apple_pay => DuploButton.applePay(
                                isApplePayDarkMode: true,
                                onTap: () {
                                  _getDepositDetails(builderContext);
                                },
                                isLoading: state.isButtonLoading,
                                isDisabled: !state.isButtonEnabled,
                              ),
                              DepositMop.google_pay => DuploButton.googlePay(
                                isGooglePayDarkMode: true,
                                onTap: () {
                                  _getDepositDetails(builderContext);
                                },
                                isLoading: state.isButtonLoading,
                                isDisabled: !state.isButtonEnabled,
                              ),
                              (_) => DuploButton.defaultPrimary(
                                title: localization.payments_continue,
                                onTap: () {
                                  _getDepositDetails(builderContext);
                                },
                                isDisabled:
                                    !state.isButtonEnabled ||
                                    state.errorMessage != null,
                                isLoading: state.isButtonLoading,
                                trailingIcon:
                                    Assets.images.chevronRight.keyName,

                                useFullWidth: true,
                              ),
                            };
                          },
                        )
                        : Container(),
                    const SizedBox(height: 20), // Extra space at the bottom
                  ],
                ),
              ),
            ),
          ),
        ),
        if (widget.isFullScreenLoaderEnabled) LoadingView(),
      ],
    );
  }

  void _getDepositDetails(BuildContext ctx) {
    ctx.read<DepositAccountsAndAmountBloc>().add(
      DepositAccountsAndAmountEvent.getDepositDetails(
        paymentMethod: widget.paymentMethod,
        isDarkTheme: diContainer<ThemeManager>().isDarkMode,
      ),
    );
  }

  bool _isStartWithConversionRate(DepositAccountsAndAmountState state) {
    // in lean we dont want user to change currency as we can only deposit using
    // USD and AED account so if the trading account selected is not AED/USD we
    // should show start with conversion
    if (widget.paymentMethod.mop == DepositMop.lean) {
      return state.leanAccountArgs?.selectedLeanAccount?.currency !=
          state.selectedAccount!.homeCurrency;
    }
    return AmountConversionUtils.getIsStartWithConversionRate(
      accountCurrency: state.selectedAccount!.homeCurrency,
      currencyAmountDetails: widget.paymentMethod.currencyAmountDetails,
    );
  }

  String? _getSelectedCurrency(DepositAccountsAndAmountState state) {
    // in lean we dont want user to change currency as we can only deposit using
    // USD and AED account so if the trading account selected is not AED/USD
    // we select the currency to lean account currency as thats the account we are
    // depositing from
    if (widget.paymentMethod.mop == DepositMop.lean) {
      return state.leanAccountArgs?.selectedLeanAccount?.currency;
    }
    return AmountConversionUtils.getSelectedCurrency(
      selectedCurrency: state.selectedCurrency,
      accountCurrency: state.selectedAccount!.homeCurrency,
      defaultCurrency: widget.paymentMethod.defaultCurrency,
      currencyAmountDetails: widget.paymentMethod.currencyAmountDetails,
    );
  }
}
