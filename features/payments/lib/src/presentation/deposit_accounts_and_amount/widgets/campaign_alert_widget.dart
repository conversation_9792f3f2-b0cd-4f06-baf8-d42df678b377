import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/domain/model/campaign_result_state.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import 'package:payment/src/assets/assets.gen.dart' as paymentAssets;
import 'package:url_launcher/url_launcher.dart';

class CampaignAlertWidget extends StatelessWidget {
  const CampaignAlertWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final localization = EquitiLocalization.of(context);
    return BlocBuilder<
      DepositAccountsAndAmountBloc,
      DepositAccountsAndAmountState
    >(
      buildWhen:
          (previous, current) =>
              previous.selectedAccount != current.selectedAccount ||
              previous.campaignInfoState != current.campaignInfoState ||
              current.accountCurrencyAmount != previous.accountCurrencyAmount ||
              current.campaignState != previous.campaignState,

      builder: (_, state) {
        switch (state.campaignInfoState) {
          case CampaignInfoSuccessState(campaignInfo: final info):
            switch (state.campaignState) {
              case CampaignResultState.idle:
                return Column(
                  children: [
                    (state.selectedAccount?.platformType ?? "") ==
                            PlatformType.mt5
                        ? state.accountCurrencyAmount.isEmpty ||
                                double.parse(state.accountCurrencyAmount) <
                                    info.data.minDepositAmount
                            ? state.accountCurrencyAmount.isEmpty
                                ? DuploAlertMessage.brand(
                                  subtitle:
                                      info
                                          .data
                                          .metadata
                                          .qualificationItem
                                          .description ??
                                      info
                                          .data
                                          .metadata
                                          .qualificationItem
                                          .title,
                                )
                                : DuploAlertMessage.brand(
                                  subtitle:
                                      info.data.metadata.headerItem.description,
                                )
                            : InkWell(
                              onTap: () {
                                launchUrl(
                                  Uri.parse(
                                    info.data.metadata.optionItem.tnc ?? '',
                                  ),
                                );
                              },
                              child: DuploAlertMessage.brand(
                                richSubtitle: RichText(
                                  text: TextSpan(
                                    children: [
                                      TextSpan(
                                        text:
                                            localization
                                                .payments_campaign_30_percent_bonus_question,
                                        style: TextStyle(
                                          color: theme.text.textTertiary,
                                          fontSize: 14,
                                        ),
                                      ),
                                      TextSpan(
                                        text:
                                            localization
                                                .payments_campaign_terms_and_conditions_apply,
                                        style: TextStyle(
                                          color: theme.text.textTertiary,
                                          fontSize: 14,
                                          decoration: TextDecoration.underline,
                                          decorationColor:
                                              theme.text.textTertiary,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                hasSecondaryButton: true,

                                primaryAction:
                                    info
                                        .data
                                        .metadata
                                        .optionItem
                                        .primaryCtaText,
                                onTapPrimaryAction: () {
                                  context.read<DepositAccountsAndAmountBloc>().add(
                                    DepositAccountsAndAmountEvent.changeCampaignResultState(
                                      campaignResultState:
                                          CampaignResultState.accepted,
                                      campaignId: info.data.id,
                                    ),
                                  );
                                },
                                secondaryAction:
                                    info
                                        .data
                                        .metadata
                                        .optionItem
                                        .secondaryCtaText,
                                onTapSecondaryAction: () {
                                  context.read<DepositAccountsAndAmountBloc>().add(
                                    DepositAccountsAndAmountEvent.changeCampaignResultState(
                                      campaignResultState:
                                          CampaignResultState.cancelled,
                                    ),
                                  );
                                },
                              ),
                            )
                        : DuploAlertMessage.warning(
                          subtitle:
                              localization
                                  .payments_campaign_welcome_bonus_qualification_message,
                          leading:
                              paymentAssets.Assets.images.campaignCancelled
                                  .svg(),
                        ),
                    SizedBox(height: 25),
                  ],
                );
              case CampaignResultState.accepted:
                return Column(
                  children: [
                    DuploAlertMessage.brand(
                      subtitle:
                          info.data.metadata.optionItem.primaryCtaOnClickDesc,
                      leading: paymentAssets.Assets.images.checkRound.svg(),
                    ),
                    SizedBox(height: 25),
                  ],
                );
              case CampaignResultState.cancelled:
                return Column(
                  children: [
                    DuploAlertMessage.warning(
                      subtitle:
                          info.data.metadata.optionItem.secondaryCtaOnClickDesc,
                      leading:
                          paymentAssets.Assets.images.campaignCancelled.svg(),
                      hasSecondaryButton: true,
                      primaryAction:
                          localization.payments_campaign_change_my_mind,
                      onTapPrimaryAction: () {
                        context.read<DepositAccountsAndAmountBloc>().add(
                          DepositAccountsAndAmountEvent.changeCampaignResultState(
                            campaignResultState: CampaignResultState.idle,
                            campaignId: state.campaignId,
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 25),
                  ],
                );
            }
          case CampaignInfoLoadingState():
            return Column(
              children: [
                DuploShimmer(
                  child: Container(
                    height: 70,
                    width: MediaQuery.sizeOf(context).width,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                SizedBox(height: 25),
              ],
            );
          case CampaignInfoErrorState():
            return Container();
        }
      },
    );
  }
}
