import 'package:duplo/duplo.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:campaigns/campaigns.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/bloc/deposit_accounts_and_amount_bloc.dart';
import 'package:prelude/prelude.dart';

class CampaignPageViewWidget extends StatefulWidget {
  const CampaignPageViewWidget({
    super.key,
    required this.builderContext,
    required this.campaigns,
    required this.accountCurrency,
    required this.accountId,
  });

  final CampaignsModel campaigns;
  final BuildContext builderContext;
  final String accountCurrency;
  final String accountId;

  @override
  State<CampaignPageViewWidget> createState() => _CampaignPageViewWidgetState();
}

class _CampaignPageViewWidgetState extends State<CampaignPageViewWidget> {
  late final PageController _pageController;
  bool _hasInitialized = false;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && !_hasInitialized) {
        _handlePageChange(0);
        _hasInitialized = true;
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _handlePageChange(int index) {
    final campaign = widget.campaigns.data.campaigns.elementAtOrNull(index);
    if (campaign == null) return;

    widget.builderContext.read<DepositAccountsAndAmountBloc>().add(
      DepositAccountsAndAmountEvent.setCampaignId(campaignId: campaign.id),
    );
    widget.builderContext.read<DepositAccountsAndAmountBloc>().add(
      DepositAccountsAndAmountEvent.getCampaignInfo(
        accountId: widget.accountId,
        accountCurrency: widget.accountCurrency,
        campaignId: campaign.id,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);

    return Column(
      children: [
        SizedBox(
          height: 120,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (index) => _handlePageChange(index),
            itemCount: widget.campaigns.data.campaigns.length,
            itemBuilder: (pageViewContext, index) {
              final campaign = widget.campaigns.data.campaigns.elementAtOrNull(
                index,
              );
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 4.0),
                child: DuploAlertMessage.success(
                  title: localization.payments_campaign_welcome_bonus_title,
                  subtitle: localization
                      .payments_campaign_welcome_bonus_subtitle(
                        EquitiFormatter.formatNumber(
                          value: campaign?.minDepositAmount ?? 0,
                          locale: Localizations.localeOf(context).toString(),
                        ),
                        campaign?.depositCurrency ?? "",
                      ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
