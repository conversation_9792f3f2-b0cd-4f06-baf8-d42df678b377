import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:payment/src/assets/assets.gen.dart' as paymentAssets;

class BounsCreditNoteBottomSheet extends StatelessWidget {
  const BounsCreditNoteBottomSheet({
    super.key,
    required this.note,
    required this.onContinue,
  });
  final String note;
  final VoidCallback onContinue;

  @override
  Widget build(BuildContext context) {
    final localization = EquitiLocalization.of(context);
    return Padding(
      padding: const EdgeInsets.all(18.0),
      child: Column(
        children: [
          SizedBox(height: 30),
          paymentAssets.Assets.images.bounsAlert.svg(),
          SizedBox(height: 10),
          DuploText(
            textAlign: TextAlign.center,
            text: localization.payments_campaign_bonus_credit_note_title,
            style: context.duploTextStyles.textXl,
            color: context.duploTheme.text.textPrimary,
            fontWeight: DuploFontWeight.semiBold,
          ),
          SizedBox(height: 10),
          DuploText(
            textAlign: TextAlign.center,
            text: note,
            style: context.duploTextStyles.textSm,
            color: context.duploTheme.text.textSecondary,
            fontWeight: DuploFontWeight.regular,
          ),
          SizedBox(height: 20),

          DuploButton.defaultPrimary(
            useFullWidth: true,
            title: localization.payments_continue,
            trailingIcon: Assets.images.chevronRight.keyName,
            onTap: () {
              onContinue();
            },
          ),
          SizedBox(height: 10),
          DuploButton.secondary(
            title: localization.payments_cancel,
            useFullWidth: true,
            onTap: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
}

void showBounsCreditNoteBottomSheet(
  BuildContext context,
  String note,
  VoidCallback onContinue,
) {
  DuploSheet.showModalSheet<void>(
    context: context,
    hasTopBarLayer: false,
    hideTitle: true,
    hideCloseButton: true,
    title: "",
    content:
        (contextContext) =>
            BounsCreditNoteBottomSheet(note: note, onContinue: onContinue),
  );
}
