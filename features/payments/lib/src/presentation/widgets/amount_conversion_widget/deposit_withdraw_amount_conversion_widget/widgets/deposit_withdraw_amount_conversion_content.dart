import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/data/conversion_rate/conversion_rate_model.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';

import 'package:payment/src/domain/data/payment_type.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/services/amount_validation_service.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/utils/amount_conversion_utils.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/bloc/deposit_withdraw_amount_conversion_bloc.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/deposit_withdraw_amount_conversion_widget.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/widgets/deposit_withdraw_amount_conversion_loading_view.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/widgets/deposit_withdraw_amount_fields.dart';
import 'package:payment/src/presentation/widgets/amount_conversion_widget/deposit_withdraw_amount_conversion_widget/widgets/deposit_withdraw_suggested_amount.dart';
import 'package:prelude/prelude.dart';

class DepositWithdrawAmountConversionContent extends StatelessWidget {
  const DepositWithdrawAmountConversionContent({
    super.key,
    required this.args,
    required this.onAmountChange,
    required this.accountCurrencyController,
    required this.selectedCurrencyController,
    this.campaignAlert,
  });
  final DepositWithdrawAmountConversionArgs args;
  final DepositWithdrawAmountConversionCallback onAmountChange;
  final TextEditingController accountCurrencyController;
  final TextEditingController selectedCurrencyController;
  final Widget? campaignAlert;
  @override
  Widget build(BuildContext context) {
    final theme = DuploTheme.of(context);
    final duploTextStyles = DuploTextStyles.of(context);
    final locale = Localizations.localeOf(context).toString();
    final localization = EquitiLocalization.of(context);

    return BlocConsumer<
      DepositWithdrawAmountConversionBloc,
      DepositWithdrawAmountConversionState
    >(
      buildWhen: (previous, current) => previous != current,
      listener: (listenerContext, state) {
        onAmountChange(
          selectedCurrencyAmount: selectedCurrencyController.text.replaceAll(
            ',',
            '',
          ),
          isAmountValid: state.isValidAmount ?? false,
          accountCurrencyAmount: accountCurrencyController.text.replaceAll(
            ',',
            '',
          ),
          conversionRateSelectedToAccountCurrency:
              _getConversionRateSelectedToAccountCurrency(
                conversionRateData: state.conversionRateData,
                selectedCurrency: state.selectedCurrency,
              ),
          conversionRateData: state.conversionRateData,
          conversionRateString: _getConversionString(
            conversionRateData: state.conversionRateData,
            selectedCurrency: state.selectedCurrency,
            accountCurrency: args.accountCurrency,
          ),
          selectedCurrency: state.selectedCurrency ?? args.accountCurrency,
        );
      },
      builder: (builderContext, state) {
        switch (state.processState) {
          case AmountConversionLoadingProcessState():
            return DepositWithdrawAmountConversionLoadingView(
              accountCurrency: args.accountCurrency,
              selectedCurrencyMinMaxSuggestedAmountDetail:
                  state.selectedCurrencyMinMaxSuggestedAmountDetail,
              txnLimit: _getTransactionLimitText(
                localization: localization,
                locale: locale,
                currency: state.selectedCurrency ?? args.accountCurrency,
              ),
            );
          case AmountConversionErrorProcessState(:final errorMessage):
            return SizedBox(
              height: 200,
              child: Padding(
                padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
                child: Center(
                  child: Text(errorMessage ?? 'Error', maxLines: 2),
                ),
              ),
            );
          case AmountConversionSuccessProcessState():
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DuploText(
                  text: localization.payments_amount,
                  style: duploTextStyles.textLg,
                  fontWeight: DuploFontWeight.semiBold,
                  color: theme.text.textPrimary,
                ),
                if (args.paymentType != PaymentType.transfer) ...[
                  SizedBox(height: DuploSpacing.spacing_md_8),
                  DuploText(
                    text: _getTransactionLimitText(
                      localization: localization,
                      locale: locale,
                      currency: state.selectedCurrency ?? args.accountCurrency,
                    ),
                    style: duploTextStyles.textSm,
                    fontWeight: DuploFontWeight.regular,
                    color: theme.text.textSecondary,
                  ),
                ],
                SizedBox(height: DuploSpacing.spacing_3xl_24),
                campaignAlert ?? const SizedBox.shrink(),
                DepositWithdrawAmountFields(
                  key: const Key('amount_field'),
                  selectedCurrencyController: selectedCurrencyController,
                  accountCurrencyController: accountCurrencyController,
                  args: args,
                ),
                ((!(state.isValidAmount ?? true) &&
                            state.validationError !=
                                AmountValidationError.none) ||
                        args.externalErrorMessage != null)
                    ? Padding(
                      padding: const EdgeInsets.only(
                        top: DuploSpacing.spacing_sm_6,
                        left: DuploSpacing.spacing_md_8,
                        right: DuploSpacing.spacing_md_8,
                      ),
                      child: DuploText(
                        text:
                            args.externalErrorMessage ??
                            _getValidationErrorMessage(
                              state.validationError,
                              state.selectedCurrency ?? args.accountCurrency,
                              localization,
                              locale,
                            ),
                        style: duploTextStyles.textXs,
                        fontWeight: DuploFontWeight.regular,
                        color: theme.text.textErrorPrimary,
                      ),
                    )
                    : state.doesNeedConversion
                    ? Padding(
                      padding: const EdgeInsets.only(
                        top: DuploSpacing.spacing_sm_6,
                        left: DuploSpacing.spacing_md_8,
                        right: DuploSpacing.spacing_md_8,
                      ),
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: DuploText(
                          text: _getConversionString(
                            conversionRateData: state.conversionRateData,
                            selectedCurrency: state.selectedCurrency,
                            accountCurrency: args.accountCurrency,
                          ),
                          style: duploTextStyles.textXs,
                          fontWeight: DuploFontWeight.regular,
                          color: theme.text.textTertiary,
                        ),
                      ),
                    )
                    : Container(),
                if (args.showSuggestedAmounts) ...[
                  SizedBox(height: DuploSpacing.spacing_lg_12),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      horizontal: DuploSpacing.spacing_md_8,
                    ),
                    child: DepositWithdrawSuggestedAmount(
                      selectedCurrencyController: selectedCurrencyController,
                      accountCurrencyController: accountCurrencyController,
                      args: args,
                      selectedCurrency: state.selectedCurrency,
                    ),
                  ),
                ],
              ],
            );
        }
      },
    );
  }

  String _getTransactionLimitText({
    required EquitiLocalization localization,
    required String locale,
    required String currency,
  }) {
    final txnLimitTextForPremierAccount =
        _getTransactionLimitTextForPremierAccount();
    if (txnLimitTextForPremierAccount != null) {
      return txnLimitTextForPremierAccount;
    }

    final targetCurrencyDetail =
        AmountConversionUtils.getMinAndMaxAmountOfCurrency(
          currencyMinMaxSuggestedAmountList:
              args.currencyMinMaxSuggestedAmountList,
          currency: currency,
        );
    final minAmount = targetCurrencyDetail?.minAmount ?? 0;
    final maxAmount = targetCurrencyDetail?.maxAmount ?? 0;
    final minMaxCurrency = targetCurrencyDetail?.currency ?? currency;
    final minAmountString = EquitiFormatter.formatNumber(
      value: minAmount,
      locale: locale,
    );
    final maxAmountString = EquitiFormatter.formatNumber(
      value: maxAmount,
      locale: locale,
    );
    if (maxAmount == 0) {
      return localization.payments_minimum_amount(
        minAmountString,
        minMaxCurrency,
      );
    }

    return '${localization.payments_transactionLimit}($minAmountString - $maxAmountString) $minMaxCurrency';
  }

  String _getValidationErrorMessage(
    AmountValidationError validationError,
    String currency,
    EquitiLocalization localization,
    String locale,
  ) {
    final accountOrTargetCurrencyMinMaxAmount =
        AmountConversionUtils.getMinAndMaxAmountOfCurrency(
          currencyMinMaxSuggestedAmountList:
              args.currencyMinMaxSuggestedAmountList,
          currency: currency,
        );
    switch (validationError) {
      case AmountValidationError.belowMinimum:
        final minAmount = accountOrTargetCurrencyMinMaxAmount?.minAmount ?? 0;
        return localization.payments_amount_validation_minimum(
          EquitiFormatter.formatNumber(value: minAmount, locale: locale),
          accountOrTargetCurrencyMinMaxAmount?.currency ?? currency,
        );
      case AmountValidationError.aboveMaximum:
        final maxAmount = accountOrTargetCurrencyMinMaxAmount?.maxAmount ?? 0;
        return localization.payments_amount_validation_maximum(
          EquitiFormatter.formatNumber(value: maxAmount, locale: locale),
          accountOrTargetCurrencyMinMaxAmount?.currency ?? currency,
        );
      case AmountValidationError.none:
        return localization.payments_amount_validation_exceeded_limit;
    }
  }

  RatesModel? _getConversionRateSelectedToAccountCurrency({
    ConversionRateModel? conversionRateData,
    String? selectedCurrency,
  }) {
    return AmountConversionUtils.getSelectedCurrencyRate(
      conversionRateData: conversionRateData,
      selectedCurrency: selectedCurrency,
    );
  }

  String _getConversionString({
    required ConversionRateModel? conversionRateData,
    String? selectedCurrency,
    required String accountCurrency,
  }) {
    return AmountConversionUtils.getSelectedCurrencyConversionRateString(
      conversionRateData: conversionRateData,
      selectedCurrency: selectedCurrency ?? accountCurrency,
    );
  }

  /// Gets the transaction limit text specifically for premier accounts.
  ///
  /// Returns a localized minimum deposit message for premier accounts
  /// based on the account's platform type. If the account is not a
  /// premier account, returns null.
  ///
  /// Returns: Formatted localized message with minimum amount and currency,
  /// or null if not a premier account or no minimum amount is configured.
  String? _getTransactionLimitTextForPremierAccount() {
    if (args.account?.platformAccountType == PlatformAccountType.premiere &&
        args.paymentType == PaymentType.deposit) {
      final premierAccountMinAmount = args.premierAccountMinAmountForDeposit
          .getPremierAccountMinAmountByAccountPlatformType(
            args.account?.platformType,
          );
      return premierAccountMinAmount?.minAmountMessage;
    }
    return null;
  }
}
