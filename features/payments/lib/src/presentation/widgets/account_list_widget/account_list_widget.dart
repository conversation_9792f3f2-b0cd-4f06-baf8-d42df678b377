import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_localization/l10n/gen/equiti_localization.gen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/bloc/account_list_bloc.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/account_list_loading_state.dart';
import 'package:payment/src/presentation/widgets/account_list_widget/components/account_list_content.dart';

/// Arguments for the AccountListWidget
typedef AccountListArgs =
    ({
      bool selectByHighestBalance,
      String? excludeAccountNumber,
      bool isInputDisabled,
      void Function({
        required bool hasAccounts,
        required bool hasWallets,
        required bool isCurrentTabEmpty,
        required int currentTabIndex,
      })?
      onEmptyStateChanged,
    });

/// Callback for when the empty state changes

/// Callback for account selection
typedef AccountSelectionCallback =
    void Function(TradingAccountModel selectedAccount);

/// A reusable widget for displaying and selecting accounts
class AccountListWidget extends StatefulWidget {
  const AccountListWidget({
    required this.args,
    required this.onAccountSelected,
    super.key,
  });

  final AccountListArgs args;
  final AccountSelectionCallback onAccountSelected;

  @override
  State<AccountListWidget> createState() => _AccountListWidgetState();
}

class _AccountListWidgetState extends State<AccountListWidget>
    with SingleTickerProviderStateMixin {
  late AccountListBloc _bloc;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _bloc = diContainer<AccountListBloc>();
    _tabController = TabController(length: 2, vsync: this);

    // Initialize account loading
    _bloc.add(const AccountListEvent.loadAccounts());
  }

  @override
  void didUpdateWidget(AccountListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if any of the arguments that affect filtering have changed
    if (_hasArgsChanged(oldWidget.args, widget.args)) {
      _bloc.add(const AccountListEvent.loadAccounts());
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _bloc.close();
    super.dispose();
  }

  /// Checks if any arguments that affect filtering have changed
  bool _hasArgsChanged(AccountListArgs oldArgs, AccountListArgs newArgs) {
    return oldArgs.selectByHighestBalance != newArgs.selectByHighestBalance ||
        oldArgs.excludeAccountNumber != newArgs.excludeAccountNumber;
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: _AccountListContent(
        args: widget.args,
        onAccountSelected: widget.onAccountSelected,
        tabController: _tabController,
      ),
    );
  }
}

class _AccountListContent extends StatelessWidget {
  const _AccountListContent({
    required this.args,
    required this.onAccountSelected,
    required this.tabController,
  });

  final AccountListArgs args;
  final AccountSelectionCallback onAccountSelected;
  final TabController tabController;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AccountListBloc, AccountListState>(
      listener: (listenerContext, state) {
        // Handle automatic account selection
        if (state.shouldAutoSelect && state.accountsData != null) {
          final selectedAccount = _getAccountToSelect(state);
          if (selectedAccount != null) {
            listenerContext.read<AccountListBloc>().add(
              AccountListEvent.selectAccount(selectedAccount),
            );
            onAccountSelected(selectedAccount);
          }
        }

        // Handle pre-selected account from load operation
        if (!state.shouldAutoSelect &&
            state.selectedAccount != null &&
            state.accountsData != null) {
          onAccountSelected(state.selectedAccount!);
        }

        // Notify parent about empty state changes
        if (args.onEmptyStateChanged != null && state.accountsData != null) {
          _notifyEmptyStateChanged(listenerContext, state);
        }
      },
      buildWhen: (previous, current) => previous != current,
      builder: (builderContext, state) {
        return switch (state.processState) {
          AccountListLoadingProcessState() => AccountListLoadingState(
            tabController: tabController,
          ),
          AccountListErrorProcessState(:final errorMessage) => SizedBox(
            height: 200,
            child: Padding(
              padding: const EdgeInsets.all(DuploSpacing.spacing_xl_16),
              child: Center(
                child: Text(
                  errorMessage ??
                      EquitiLocalization.of(
                        builderContext,
                      ).trader_somethingWentWrong,
                  maxLines: 2,
                ),
              ),
            ),
          ),
          AccountListSuccessProcessState() => AccountListContent(
            args: args,
            onAccountSelected: onAccountSelected,
            tabController: tabController,
          ),
        };
      },
    );
  }

  TradingAccountModel? _getAccountToSelect(AccountListState state) {
    if (state.accountsData == null) return null;

    final tradingAccounts = _filterAccounts(state.allTradingAccounts);
    final walletAccounts = _filterAccounts(state.allWalletAccounts);

    if (args.selectByHighestBalance) {
      // When selecting by highest balance, consider accounts from current tab first
      final currentTabAccounts =
          state.currentTabIndex == 0 ? tradingAccounts : walletAccounts;
      final otherTabAccounts =
          state.currentTabIndex == 0 ? walletAccounts : tradingAccounts;

      final allAccounts = [...currentTabAccounts, ...otherTabAccounts];
      if (allAccounts.isEmpty) return null;

      return allAccounts.reduce(
        (current, next) =>
            (current.effectiveBalance ?? 0) > (next.effectiveBalance ?? 0)
                ? current
                : next,
      );
    }

    // Select first account from the current tab
    if (state.currentTabIndex == 0) {
      // Trading accounts tab - select first trading account
      return tradingAccounts.firstOrNull;
    } // Wallet accounts tab - select first wallet account
    return walletAccounts.firstOrNull;
  }

  void _notifyEmptyStateChanged(BuildContext _, AccountListState state) {
    final tradingAccounts = state.filteredTradingAccounts;
    final walletAccounts = state.filteredWalletAccounts;

    final hasAccounts = tradingAccounts.isNotEmpty;
    final hasWallets = walletAccounts.isNotEmpty;
    final isCurrentTabEmpty =
        state.currentTabIndex == 0
            ? (tradingAccounts.length == 1 &&
                    args.excludeAccountNumber ==
                        tradingAccounts.firstOrNull?.accountNumber
                ? true
                : !hasAccounts)
            : (walletAccounts.length == 1 &&
                    args.excludeAccountNumber ==
                        walletAccounts.firstOrNull?.accountNumber
                ? true
                : !hasWallets);

    args.onEmptyStateChanged!(
      hasAccounts: hasAccounts,
      hasWallets: hasWallets,
      isCurrentTabEmpty: isCurrentTabEmpty,
      currentTabIndex: state.currentTabIndex,
    );
  }

  List<TradingAccountModel> _filterAccounts(
    List<TradingAccountModel> accounts,
  ) {
    if (args.excludeAccountNumber == null) {
      return accounts;
    }
    return accounts
        .where((account) => account.accountNumber != args.excludeAccountNumber)
        .toList();
  }
}
