import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:flutter/material.dart';

/// Individual account tile component
class AccountTile extends StatelessWidget {
  const AccountTile({
    required this.account,
    required this.isWallet,
    required this.isSelected,
    required this.onTap,
    super.key,
  });

  final TradingAccountModel account;
  final bool isWallet;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return DuploTap(
      radius: 12,
      key: Key(account.homeCurrency),
      onTap: onTap,
      child: DuploPaymentAccountTile(
        accountInfo: AccountInfo(
          nickName: account.nickName ?? '',
          balance: account.effectiveBalance ?? 0,
          currency: account.homeCurrency,
          accountNumber: account.accountNumber,
          platform: account.platformType.displayName,
          type: account.platformTypeName,
          currencyImage: FlagProvider.getFlagFromCurrencyCode(
            account.homeCurrency,
            width: 16,
            height: 16,
          ),
          isWallet: isWallet,
        ),
        isSelected: isSelected,
      ),
    );
  }
}
