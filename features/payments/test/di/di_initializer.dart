import 'package:api_client/api_client.dart';
import 'package:campaigns/campaigns.dart';
import 'package:domain/domain.dart';
import 'package:duplo/duplo.dart';
import 'package:equiti_analytics/equiti_analytics.dart';
import 'package:equiti_auth/equiti_auth.dart';
import 'package:flutter/material.dart';
import 'package:injectable/injectable.dart';
import 'package:monitoring/monitoring.dart';
import 'package:payment/payments.dart';
import 'package:payment/src/di/di_container.dart';
import 'package:preferences/preferences.dart';
import 'package:theme_manager/theme_manager.dart';
import 'package:user_account/user_account.dart';

import '../mocks/auth_service_mock.dart';
import '../mocks/payment_flags_mock.dart';
import '../mocks/equiti_preferences_mock.dart';
import '../mocks/payment_navigation_mock.dart';
import '../mocks/theme_manager_mock.dart';

Future<void> setupDi() async {
  final gh = GetItHelper(diContainer);
  gh.lazySingleton(() => MockApiInterceptor());
  await MonitoringPackageModule().init(gh);
  gh.lazySingleton(
    () =>
        DioBuilder()
            .setBaseUrl('http://equiti-platform.com/')
            .addInterceptor(gh<PrettyDioLogger>())
            .addInterceptor(gh<MockApiInterceptor>())
            .withNativeAdapter()
            .withReporter(),
  );
  gh.lazySingleton<ApiClientBase>(
    () => DioApiClient(gh<DioBuilder>().build()),
    instanceName: 'mobileBffApiClient',
  );
  gh.lazySingleton<AuthService>(() => AuthServiceMock());
  gh.lazySingleton<ApiClientBase>(() => DioApiClient(gh<DioBuilder>().build()));
  gh.lazySingleton<EquitiPreferences>(() => EquitiPreferencesMock());
  gh.lazySingleton<PaymentNavigation>(() => PaymentNavigationMock());
  gh.lazySingleton<ThemeManager>(() => ThemeManagerMock());
  gh.lazySingleton<PaymentFlags>(() => PaymentFlagsMock());

  gh.lazySingleton(() => GlobalKey<NavigatorState>());
  await PaymentPackageModule().init(gh);
  await UserAccountPackageModule().init(gh);
  await DomainPackageModule().init(gh);
  await DuploPackageModule().init(gh);
  await EquitiAnalyticsPackageModule().init(gh);
  await CampaignsPackageModule().init(gh);
}
