import 'scenarios/list_of_accounts_success.dart';
import 'scenarios/list_of_accounts_failure.dart';
import 'package:equiti_test/equiti_test.dart';
import 'package:payment/src/presentation/deposit_accounts_and_amount/deposit_accounts_and_amount_screen.dart';
import 'package:payment/src/data/payment_method/deposit_payment_methods_model/deposit_payment_methods_model.dart';
import 'package:domain/domain.dart';
import 'package:payment/src/data/payment_method/payment_method_mix_model.dart';



Feature: Deposit Account and Amount
  @testMethodName: testGoldens
  Scenario: Deposit Account and Amount List Of Accounts
    Given The {DepositAccountsAndAmountScreen(paymentMethod: DepositPaymentMethod(name: "Card",currencyAmountDetails: [CurrencyAmountDetail(currency: 'AED',suggestedAmounts: [500, 1000, 1500],minAmount: 1, maxAmount: 1000, ),CurrencyAmountDetail(currency: 'USD',suggestedAmounts: [50, 100, 150],minAmount: 1,maxAmount: 2000,),],currencies: ["USD", "AED"], ),depositFlowConfig: DepositFlowConfig(  origin: '',depositType: DepositType.additional,),)} app is rendered {scenarios:[allMocksSuccess]}
    Then I wait
    Then I wait
    Then I wait
    Then I wait
    Then screenshot verified {'deposit_account_and_amount'}
