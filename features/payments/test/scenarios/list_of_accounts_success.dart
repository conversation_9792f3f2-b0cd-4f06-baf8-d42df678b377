import 'package:api_client/api_client.dart';
import 'package:payment/src/di/di_container.dart';

void allMocksSuccess() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/client/accounts': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/payment_list_of_accounts/success.json',
        ),
      ],
      'api/v1/client/campaigns': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/campaigns/success.json',
        ),
      ],
      'api/v1/client/campaignInfo': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/campaign_info/success.json',
        ),
      ],
    });
}

void listOfAccountsSuccess() {
  diContainer<MockApiInterceptor>()
    ..reset()
    ..reply({
      '/api/client/accounts': [
        MockResponse(
          code: 200,
          bodyFilePath: 'resources/mocks/payment_list_of_accounts/success.json',
        ),
      ],
    });
}
