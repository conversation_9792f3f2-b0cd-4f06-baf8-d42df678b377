import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaigns_model.freezed.dart';
part 'campaigns_model.g.dart';

/// Response model for campaigns API
@freezed
sealed class CampaignsModel with _$CampaignsModel {
  const factory CampaignsModel({
    required bool success,
    required CampaignsData data,
  }) = _CampaignsModel;

  factory CampaignsModel.fromJson(Map<String, dynamic> json) =>
      _$CampaignsModelFromJson(json);
}

/// Data wrapper for campaigns
@freezed
sealed class CampaignsData with _$CampaignsData {
  const factory CampaignsData({required List<Campaign> campaigns}) =
      _CampaignsData;

  factory CampaignsData.fromJson(Map<String, dynamic> json) =>
      _$CampaignsDataFromJson(json);
}

/// Individual campaign model
@freezed
sealed class Campaign with _$Campaign {
  const factory Campaign({
    required String id,
    required String bonusType,
    required int bonusValue,
    required int minDepositAmount,
    required String? depositCurrency,
    required String? qualificationText,
    required List<TradingAccount> tradingAccounts,
  }) = _Campaign;

  factory Campaign.fromJson(Map<String, dynamic> json) =>
      _$CampaignFromJson(json);
}

/// Trading account model
@freezed
sealed class TradingAccount with _$TradingAccount {
  const factory TradingAccount({
    required String id,
    required String type,
    @JsonKey(name: 'is_swap_free') required bool swapEnabled,
  }) = _TradingAccount;

  factory TradingAccount.fromJson(Map<String, dynamic> json) =>
      _$TradingAccountFromJson(json);
}
