// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaign_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CampaignInfoModel {

 bool get success; CampaignInfoData get data;
/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignInfoModelCopyWith<CampaignInfoModel> get copyWith => _$CampaignInfoModelCopyWithImpl<CampaignInfoModel>(this as CampaignInfoModel, _$identity);

  /// Serializes this CampaignInfoModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'CampaignInfoModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class $CampaignInfoModelCopyWith<$Res>  {
  factory $CampaignInfoModelCopyWith(CampaignInfoModel value, $Res Function(CampaignInfoModel) _then) = _$CampaignInfoModelCopyWithImpl;
@useResult
$Res call({
 bool success, CampaignInfoData data
});


$CampaignInfoDataCopyWith<$Res> get data;

}
/// @nodoc
class _$CampaignInfoModelCopyWithImpl<$Res>
    implements $CampaignInfoModelCopyWith<$Res> {
  _$CampaignInfoModelCopyWithImpl(this._self, this._then);

  final CampaignInfoModel _self;
  final $Res Function(CampaignInfoModel) _then;

/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as CampaignInfoData,
  ));
}
/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignInfoDataCopyWith<$Res> get data {
  
  return $CampaignInfoDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _CampaignInfoModel implements CampaignInfoModel {
  const _CampaignInfoModel({required this.success, required this.data});
  factory _CampaignInfoModel.fromJson(Map<String, dynamic> json) => _$CampaignInfoModelFromJson(json);

@override final  bool success;
@override final  CampaignInfoData data;

/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignInfoModelCopyWith<_CampaignInfoModel> get copyWith => __$CampaignInfoModelCopyWithImpl<_CampaignInfoModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignInfoModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignInfoModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'CampaignInfoModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class _$CampaignInfoModelCopyWith<$Res> implements $CampaignInfoModelCopyWith<$Res> {
  factory _$CampaignInfoModelCopyWith(_CampaignInfoModel value, $Res Function(_CampaignInfoModel) _then) = __$CampaignInfoModelCopyWithImpl;
@override @useResult
$Res call({
 bool success, CampaignInfoData data
});


@override $CampaignInfoDataCopyWith<$Res> get data;

}
/// @nodoc
class __$CampaignInfoModelCopyWithImpl<$Res>
    implements _$CampaignInfoModelCopyWith<$Res> {
  __$CampaignInfoModelCopyWithImpl(this._self, this._then);

  final _CampaignInfoModel _self;
  final $Res Function(_CampaignInfoModel) _then;

/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = null,}) {
  return _then(_CampaignInfoModel(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as CampaignInfoData,
  ));
}

/// Create a copy of CampaignInfoModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignInfoDataCopyWith<$Res> get data {
  
  return $CampaignInfoDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$CampaignInfoData {

 String get id; String get bonusType; int get bonusValue; int get minDepositAmount; String get depositCurrency; CampaignMetadata get metadata;
/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignInfoDataCopyWith<CampaignInfoData> get copyWith => _$CampaignInfoDataCopyWithImpl<CampaignInfoData>(this as CampaignInfoData, _$identity);

  /// Serializes this CampaignInfoData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignInfoData&&(identical(other.id, id) || other.id == id)&&(identical(other.bonusType, bonusType) || other.bonusType == bonusType)&&(identical(other.bonusValue, bonusValue) || other.bonusValue == bonusValue)&&(identical(other.minDepositAmount, minDepositAmount) || other.minDepositAmount == minDepositAmount)&&(identical(other.depositCurrency, depositCurrency) || other.depositCurrency == depositCurrency)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,bonusType,bonusValue,minDepositAmount,depositCurrency,metadata);

@override
String toString() {
  return 'CampaignInfoData(id: $id, bonusType: $bonusType, bonusValue: $bonusValue, minDepositAmount: $minDepositAmount, depositCurrency: $depositCurrency, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $CampaignInfoDataCopyWith<$Res>  {
  factory $CampaignInfoDataCopyWith(CampaignInfoData value, $Res Function(CampaignInfoData) _then) = _$CampaignInfoDataCopyWithImpl;
@useResult
$Res call({
 String id, String bonusType, int bonusValue, int minDepositAmount, String depositCurrency, CampaignMetadata metadata
});


$CampaignMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class _$CampaignInfoDataCopyWithImpl<$Res>
    implements $CampaignInfoDataCopyWith<$Res> {
  _$CampaignInfoDataCopyWithImpl(this._self, this._then);

  final CampaignInfoData _self;
  final $Res Function(CampaignInfoData) _then;

/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? bonusType = null,Object? bonusValue = null,Object? minDepositAmount = null,Object? depositCurrency = null,Object? metadata = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bonusType: null == bonusType ? _self.bonusType : bonusType // ignore: cast_nullable_to_non_nullable
as String,bonusValue: null == bonusValue ? _self.bonusValue : bonusValue // ignore: cast_nullable_to_non_nullable
as int,minDepositAmount: null == minDepositAmount ? _self.minDepositAmount : minDepositAmount // ignore: cast_nullable_to_non_nullable
as int,depositCurrency: null == depositCurrency ? _self.depositCurrency : depositCurrency // ignore: cast_nullable_to_non_nullable
as String,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as CampaignMetadata,
  ));
}
/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignMetadataCopyWith<$Res> get metadata {
  
  return $CampaignMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _CampaignInfoData implements CampaignInfoData {
  const _CampaignInfoData({required this.id, required this.bonusType, required this.bonusValue, required this.minDepositAmount, required this.depositCurrency, required this.metadata});
  factory _CampaignInfoData.fromJson(Map<String, dynamic> json) => _$CampaignInfoDataFromJson(json);

@override final  String id;
@override final  String bonusType;
@override final  int bonusValue;
@override final  int minDepositAmount;
@override final  String depositCurrency;
@override final  CampaignMetadata metadata;

/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignInfoDataCopyWith<_CampaignInfoData> get copyWith => __$CampaignInfoDataCopyWithImpl<_CampaignInfoData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignInfoDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignInfoData&&(identical(other.id, id) || other.id == id)&&(identical(other.bonusType, bonusType) || other.bonusType == bonusType)&&(identical(other.bonusValue, bonusValue) || other.bonusValue == bonusValue)&&(identical(other.minDepositAmount, minDepositAmount) || other.minDepositAmount == minDepositAmount)&&(identical(other.depositCurrency, depositCurrency) || other.depositCurrency == depositCurrency)&&(identical(other.metadata, metadata) || other.metadata == metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,bonusType,bonusValue,minDepositAmount,depositCurrency,metadata);

@override
String toString() {
  return 'CampaignInfoData(id: $id, bonusType: $bonusType, bonusValue: $bonusValue, minDepositAmount: $minDepositAmount, depositCurrency: $depositCurrency, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$CampaignInfoDataCopyWith<$Res> implements $CampaignInfoDataCopyWith<$Res> {
  factory _$CampaignInfoDataCopyWith(_CampaignInfoData value, $Res Function(_CampaignInfoData) _then) = __$CampaignInfoDataCopyWithImpl;
@override @useResult
$Res call({
 String id, String bonusType, int bonusValue, int minDepositAmount, String depositCurrency, CampaignMetadata metadata
});


@override $CampaignMetadataCopyWith<$Res> get metadata;

}
/// @nodoc
class __$CampaignInfoDataCopyWithImpl<$Res>
    implements _$CampaignInfoDataCopyWith<$Res> {
  __$CampaignInfoDataCopyWithImpl(this._self, this._then);

  final _CampaignInfoData _self;
  final $Res Function(_CampaignInfoData) _then;

/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? bonusType = null,Object? bonusValue = null,Object? minDepositAmount = null,Object? depositCurrency = null,Object? metadata = null,}) {
  return _then(_CampaignInfoData(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bonusType: null == bonusType ? _self.bonusType : bonusType // ignore: cast_nullable_to_non_nullable
as String,bonusValue: null == bonusValue ? _self.bonusValue : bonusValue // ignore: cast_nullable_to_non_nullable
as int,minDepositAmount: null == minDepositAmount ? _self.minDepositAmount : minDepositAmount // ignore: cast_nullable_to_non_nullable
as int,depositCurrency: null == depositCurrency ? _self.depositCurrency : depositCurrency // ignore: cast_nullable_to_non_nullable
as String,metadata: null == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as CampaignMetadata,
  ));
}

/// Create a copy of CampaignInfoData
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignMetadataCopyWith<$Res> get metadata {
  
  return $CampaignMetadataCopyWith<$Res>(_self.metadata, (value) {
    return _then(_self.copyWith(metadata: value));
  });
}
}


/// @nodoc
mixin _$CampaignMetadata {

 CampaignItem get headerItem; CampaignItem get qualificationItem; CampaignOptionItem get optionItem; CampaignItem get qualificationDeclinedItem;
/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignMetadataCopyWith<CampaignMetadata> get copyWith => _$CampaignMetadataCopyWithImpl<CampaignMetadata>(this as CampaignMetadata, _$identity);

  /// Serializes this CampaignMetadata to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignMetadata&&(identical(other.headerItem, headerItem) || other.headerItem == headerItem)&&(identical(other.qualificationItem, qualificationItem) || other.qualificationItem == qualificationItem)&&(identical(other.optionItem, optionItem) || other.optionItem == optionItem)&&(identical(other.qualificationDeclinedItem, qualificationDeclinedItem) || other.qualificationDeclinedItem == qualificationDeclinedItem));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,headerItem,qualificationItem,optionItem,qualificationDeclinedItem);

@override
String toString() {
  return 'CampaignMetadata(headerItem: $headerItem, qualificationItem: $qualificationItem, optionItem: $optionItem, qualificationDeclinedItem: $qualificationDeclinedItem)';
}


}

/// @nodoc
abstract mixin class $CampaignMetadataCopyWith<$Res>  {
  factory $CampaignMetadataCopyWith(CampaignMetadata value, $Res Function(CampaignMetadata) _then) = _$CampaignMetadataCopyWithImpl;
@useResult
$Res call({
 CampaignItem headerItem, CampaignItem qualificationItem, CampaignOptionItem optionItem, CampaignItem qualificationDeclinedItem
});


$CampaignItemCopyWith<$Res> get headerItem;$CampaignItemCopyWith<$Res> get qualificationItem;$CampaignOptionItemCopyWith<$Res> get optionItem;$CampaignItemCopyWith<$Res> get qualificationDeclinedItem;

}
/// @nodoc
class _$CampaignMetadataCopyWithImpl<$Res>
    implements $CampaignMetadataCopyWith<$Res> {
  _$CampaignMetadataCopyWithImpl(this._self, this._then);

  final CampaignMetadata _self;
  final $Res Function(CampaignMetadata) _then;

/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? headerItem = null,Object? qualificationItem = null,Object? optionItem = null,Object? qualificationDeclinedItem = null,}) {
  return _then(_self.copyWith(
headerItem: null == headerItem ? _self.headerItem : headerItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,qualificationItem: null == qualificationItem ? _self.qualificationItem : qualificationItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,optionItem: null == optionItem ? _self.optionItem : optionItem // ignore: cast_nullable_to_non_nullable
as CampaignOptionItem,qualificationDeclinedItem: null == qualificationDeclinedItem ? _self.qualificationDeclinedItem : qualificationDeclinedItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,
  ));
}
/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get headerItem {
  
  return $CampaignItemCopyWith<$Res>(_self.headerItem, (value) {
    return _then(_self.copyWith(headerItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get qualificationItem {
  
  return $CampaignItemCopyWith<$Res>(_self.qualificationItem, (value) {
    return _then(_self.copyWith(qualificationItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignOptionItemCopyWith<$Res> get optionItem {
  
  return $CampaignOptionItemCopyWith<$Res>(_self.optionItem, (value) {
    return _then(_self.copyWith(optionItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get qualificationDeclinedItem {
  
  return $CampaignItemCopyWith<$Res>(_self.qualificationDeclinedItem, (value) {
    return _then(_self.copyWith(qualificationDeclinedItem: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _CampaignMetadata implements CampaignMetadata {
  const _CampaignMetadata({required this.headerItem, required this.qualificationItem, required this.optionItem, required this.qualificationDeclinedItem});
  factory _CampaignMetadata.fromJson(Map<String, dynamic> json) => _$CampaignMetadataFromJson(json);

@override final  CampaignItem headerItem;
@override final  CampaignItem qualificationItem;
@override final  CampaignOptionItem optionItem;
@override final  CampaignItem qualificationDeclinedItem;

/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignMetadataCopyWith<_CampaignMetadata> get copyWith => __$CampaignMetadataCopyWithImpl<_CampaignMetadata>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignMetadataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignMetadata&&(identical(other.headerItem, headerItem) || other.headerItem == headerItem)&&(identical(other.qualificationItem, qualificationItem) || other.qualificationItem == qualificationItem)&&(identical(other.optionItem, optionItem) || other.optionItem == optionItem)&&(identical(other.qualificationDeclinedItem, qualificationDeclinedItem) || other.qualificationDeclinedItem == qualificationDeclinedItem));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,headerItem,qualificationItem,optionItem,qualificationDeclinedItem);

@override
String toString() {
  return 'CampaignMetadata(headerItem: $headerItem, qualificationItem: $qualificationItem, optionItem: $optionItem, qualificationDeclinedItem: $qualificationDeclinedItem)';
}


}

/// @nodoc
abstract mixin class _$CampaignMetadataCopyWith<$Res> implements $CampaignMetadataCopyWith<$Res> {
  factory _$CampaignMetadataCopyWith(_CampaignMetadata value, $Res Function(_CampaignMetadata) _then) = __$CampaignMetadataCopyWithImpl;
@override @useResult
$Res call({
 CampaignItem headerItem, CampaignItem qualificationItem, CampaignOptionItem optionItem, CampaignItem qualificationDeclinedItem
});


@override $CampaignItemCopyWith<$Res> get headerItem;@override $CampaignItemCopyWith<$Res> get qualificationItem;@override $CampaignOptionItemCopyWith<$Res> get optionItem;@override $CampaignItemCopyWith<$Res> get qualificationDeclinedItem;

}
/// @nodoc
class __$CampaignMetadataCopyWithImpl<$Res>
    implements _$CampaignMetadataCopyWith<$Res> {
  __$CampaignMetadataCopyWithImpl(this._self, this._then);

  final _CampaignMetadata _self;
  final $Res Function(_CampaignMetadata) _then;

/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? headerItem = null,Object? qualificationItem = null,Object? optionItem = null,Object? qualificationDeclinedItem = null,}) {
  return _then(_CampaignMetadata(
headerItem: null == headerItem ? _self.headerItem : headerItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,qualificationItem: null == qualificationItem ? _self.qualificationItem : qualificationItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,optionItem: null == optionItem ? _self.optionItem : optionItem // ignore: cast_nullable_to_non_nullable
as CampaignOptionItem,qualificationDeclinedItem: null == qualificationDeclinedItem ? _self.qualificationDeclinedItem : qualificationDeclinedItem // ignore: cast_nullable_to_non_nullable
as CampaignItem,
  ));
}

/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get headerItem {
  
  return $CampaignItemCopyWith<$Res>(_self.headerItem, (value) {
    return _then(_self.copyWith(headerItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get qualificationItem {
  
  return $CampaignItemCopyWith<$Res>(_self.qualificationItem, (value) {
    return _then(_self.copyWith(qualificationItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignOptionItemCopyWith<$Res> get optionItem {
  
  return $CampaignOptionItemCopyWith<$Res>(_self.optionItem, (value) {
    return _then(_self.copyWith(optionItem: value));
  });
}/// Create a copy of CampaignMetadata
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<$Res> get qualificationDeclinedItem {
  
  return $CampaignItemCopyWith<$Res>(_self.qualificationDeclinedItem, (value) {
    return _then(_self.copyWith(qualificationDeclinedItem: value));
  });
}
}


/// @nodoc
mixin _$CampaignItem {

 String get title; String? get description;
/// Create a copy of CampaignItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignItemCopyWith<CampaignItem> get copyWith => _$CampaignItemCopyWithImpl<CampaignItem>(this as CampaignItem, _$identity);

  /// Serializes this CampaignItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignItem&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description);

@override
String toString() {
  return 'CampaignItem(title: $title, description: $description)';
}


}

/// @nodoc
abstract mixin class $CampaignItemCopyWith<$Res>  {
  factory $CampaignItemCopyWith(CampaignItem value, $Res Function(CampaignItem) _then) = _$CampaignItemCopyWithImpl;
@useResult
$Res call({
 String title, String? description
});




}
/// @nodoc
class _$CampaignItemCopyWithImpl<$Res>
    implements $CampaignItemCopyWith<$Res> {
  _$CampaignItemCopyWithImpl(this._self, this._then);

  final CampaignItem _self;
  final $Res Function(CampaignItem) _then;

/// Create a copy of CampaignItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? description = freezed,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CampaignItem implements CampaignItem {
  const _CampaignItem({required this.title, this.description});
  factory _CampaignItem.fromJson(Map<String, dynamic> json) => _$CampaignItemFromJson(json);

@override final  String title;
@override final  String? description;

/// Create a copy of CampaignItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignItemCopyWith<_CampaignItem> get copyWith => __$CampaignItemCopyWithImpl<_CampaignItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignItem&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description);

@override
String toString() {
  return 'CampaignItem(title: $title, description: $description)';
}


}

/// @nodoc
abstract mixin class _$CampaignItemCopyWith<$Res> implements $CampaignItemCopyWith<$Res> {
  factory _$CampaignItemCopyWith(_CampaignItem value, $Res Function(_CampaignItem) _then) = __$CampaignItemCopyWithImpl;
@override @useResult
$Res call({
 String title, String? description
});




}
/// @nodoc
class __$CampaignItemCopyWithImpl<$Res>
    implements _$CampaignItemCopyWith<$Res> {
  __$CampaignItemCopyWithImpl(this._self, this._then);

  final _CampaignItem _self;
  final $Res Function(_CampaignItem) _then;

/// Create a copy of CampaignItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? description = freezed,}) {
  return _then(_CampaignItem(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: freezed == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$CampaignOptionItem {

 String get title; String get description; String get primaryCtaText; String get primaryCtaOnClickDesc; String get secondaryCtaText; String get secondaryCtaOnClickDesc; String? get tnc;
/// Create a copy of CampaignOptionItem
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignOptionItemCopyWith<CampaignOptionItem> get copyWith => _$CampaignOptionItemCopyWithImpl<CampaignOptionItem>(this as CampaignOptionItem, _$identity);

  /// Serializes this CampaignOptionItem to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignOptionItem&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.primaryCtaText, primaryCtaText) || other.primaryCtaText == primaryCtaText)&&(identical(other.primaryCtaOnClickDesc, primaryCtaOnClickDesc) || other.primaryCtaOnClickDesc == primaryCtaOnClickDesc)&&(identical(other.secondaryCtaText, secondaryCtaText) || other.secondaryCtaText == secondaryCtaText)&&(identical(other.secondaryCtaOnClickDesc, secondaryCtaOnClickDesc) || other.secondaryCtaOnClickDesc == secondaryCtaOnClickDesc)&&(identical(other.tnc, tnc) || other.tnc == tnc));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description,primaryCtaText,primaryCtaOnClickDesc,secondaryCtaText,secondaryCtaOnClickDesc,tnc);

@override
String toString() {
  return 'CampaignOptionItem(title: $title, description: $description, primaryCtaText: $primaryCtaText, primaryCtaOnClickDesc: $primaryCtaOnClickDesc, secondaryCtaText: $secondaryCtaText, secondaryCtaOnClickDesc: $secondaryCtaOnClickDesc, tnc: $tnc)';
}


}

/// @nodoc
abstract mixin class $CampaignOptionItemCopyWith<$Res>  {
  factory $CampaignOptionItemCopyWith(CampaignOptionItem value, $Res Function(CampaignOptionItem) _then) = _$CampaignOptionItemCopyWithImpl;
@useResult
$Res call({
 String title, String description, String primaryCtaText, String primaryCtaOnClickDesc, String secondaryCtaText, String secondaryCtaOnClickDesc, String? tnc
});




}
/// @nodoc
class _$CampaignOptionItemCopyWithImpl<$Res>
    implements $CampaignOptionItemCopyWith<$Res> {
  _$CampaignOptionItemCopyWithImpl(this._self, this._then);

  final CampaignOptionItem _self;
  final $Res Function(CampaignOptionItem) _then;

/// Create a copy of CampaignOptionItem
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? title = null,Object? description = null,Object? primaryCtaText = null,Object? primaryCtaOnClickDesc = null,Object? secondaryCtaText = null,Object? secondaryCtaOnClickDesc = null,Object? tnc = freezed,}) {
  return _then(_self.copyWith(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,primaryCtaText: null == primaryCtaText ? _self.primaryCtaText : primaryCtaText // ignore: cast_nullable_to_non_nullable
as String,primaryCtaOnClickDesc: null == primaryCtaOnClickDesc ? _self.primaryCtaOnClickDesc : primaryCtaOnClickDesc // ignore: cast_nullable_to_non_nullable
as String,secondaryCtaText: null == secondaryCtaText ? _self.secondaryCtaText : secondaryCtaText // ignore: cast_nullable_to_non_nullable
as String,secondaryCtaOnClickDesc: null == secondaryCtaOnClickDesc ? _self.secondaryCtaOnClickDesc : secondaryCtaOnClickDesc // ignore: cast_nullable_to_non_nullable
as String,tnc: freezed == tnc ? _self.tnc : tnc // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CampaignOptionItem implements CampaignOptionItem {
  const _CampaignOptionItem({required this.title, required this.description, required this.primaryCtaText, required this.primaryCtaOnClickDesc, required this.secondaryCtaText, required this.secondaryCtaOnClickDesc, this.tnc});
  factory _CampaignOptionItem.fromJson(Map<String, dynamic> json) => _$CampaignOptionItemFromJson(json);

@override final  String title;
@override final  String description;
@override final  String primaryCtaText;
@override final  String primaryCtaOnClickDesc;
@override final  String secondaryCtaText;
@override final  String secondaryCtaOnClickDesc;
@override final  String? tnc;

/// Create a copy of CampaignOptionItem
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignOptionItemCopyWith<_CampaignOptionItem> get copyWith => __$CampaignOptionItemCopyWithImpl<_CampaignOptionItem>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignOptionItemToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignOptionItem&&(identical(other.title, title) || other.title == title)&&(identical(other.description, description) || other.description == description)&&(identical(other.primaryCtaText, primaryCtaText) || other.primaryCtaText == primaryCtaText)&&(identical(other.primaryCtaOnClickDesc, primaryCtaOnClickDesc) || other.primaryCtaOnClickDesc == primaryCtaOnClickDesc)&&(identical(other.secondaryCtaText, secondaryCtaText) || other.secondaryCtaText == secondaryCtaText)&&(identical(other.secondaryCtaOnClickDesc, secondaryCtaOnClickDesc) || other.secondaryCtaOnClickDesc == secondaryCtaOnClickDesc)&&(identical(other.tnc, tnc) || other.tnc == tnc));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,title,description,primaryCtaText,primaryCtaOnClickDesc,secondaryCtaText,secondaryCtaOnClickDesc,tnc);

@override
String toString() {
  return 'CampaignOptionItem(title: $title, description: $description, primaryCtaText: $primaryCtaText, primaryCtaOnClickDesc: $primaryCtaOnClickDesc, secondaryCtaText: $secondaryCtaText, secondaryCtaOnClickDesc: $secondaryCtaOnClickDesc, tnc: $tnc)';
}


}

/// @nodoc
abstract mixin class _$CampaignOptionItemCopyWith<$Res> implements $CampaignOptionItemCopyWith<$Res> {
  factory _$CampaignOptionItemCopyWith(_CampaignOptionItem value, $Res Function(_CampaignOptionItem) _then) = __$CampaignOptionItemCopyWithImpl;
@override @useResult
$Res call({
 String title, String description, String primaryCtaText, String primaryCtaOnClickDesc, String secondaryCtaText, String secondaryCtaOnClickDesc, String? tnc
});




}
/// @nodoc
class __$CampaignOptionItemCopyWithImpl<$Res>
    implements _$CampaignOptionItemCopyWith<$Res> {
  __$CampaignOptionItemCopyWithImpl(this._self, this._then);

  final _CampaignOptionItem _self;
  final $Res Function(_CampaignOptionItem) _then;

/// Create a copy of CampaignOptionItem
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? title = null,Object? description = null,Object? primaryCtaText = null,Object? primaryCtaOnClickDesc = null,Object? secondaryCtaText = null,Object? secondaryCtaOnClickDesc = null,Object? tnc = freezed,}) {
  return _then(_CampaignOptionItem(
title: null == title ? _self.title : title // ignore: cast_nullable_to_non_nullable
as String,description: null == description ? _self.description : description // ignore: cast_nullable_to_non_nullable
as String,primaryCtaText: null == primaryCtaText ? _self.primaryCtaText : primaryCtaText // ignore: cast_nullable_to_non_nullable
as String,primaryCtaOnClickDesc: null == primaryCtaOnClickDesc ? _self.primaryCtaOnClickDesc : primaryCtaOnClickDesc // ignore: cast_nullable_to_non_nullable
as String,secondaryCtaText: null == secondaryCtaText ? _self.secondaryCtaText : secondaryCtaText // ignore: cast_nullable_to_non_nullable
as String,secondaryCtaOnClickDesc: null == secondaryCtaOnClickDesc ? _self.secondaryCtaOnClickDesc : secondaryCtaOnClickDesc // ignore: cast_nullable_to_non_nullable
as String,tnc: freezed == tnc ? _self.tnc : tnc // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on
