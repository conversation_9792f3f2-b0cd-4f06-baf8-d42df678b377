// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaigns_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CampaignsModel _$CampaignsModelFromJson(Map<String, dynamic> json) =>
    _CampaignsModel(
      success: json['success'] as bool,
      data: CampaignsData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CampaignsModelToJson(_CampaignsModel instance) =>
    <String, dynamic>{'success': instance.success, 'data': instance.data};

_CampaignsData _$CampaignsDataFromJson(Map<String, dynamic> json) =>
    _CampaignsData(
      campaigns:
          (json['campaigns'] as List<dynamic>)
              .map((e) => Campaign.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$CampaignsDataToJson(_CampaignsData instance) =>
    <String, dynamic>{'campaigns': instance.campaigns};

_Campaign _$CampaignFromJson(Map<String, dynamic> json) => _Campaign(
  id: json['id'] as String,
  bonusType: json['bonusType'] as String,
  bonusValue: (json['bonusValue'] as num).toInt(),
  minDepositAmount: (json['minDepositAmount'] as num).toInt(),
  depositCurrency: json['depositCurrency'] as String?,
  qualificationText: json['qualificationText'] as String?,
  tradingAccounts:
      (json['tradingAccounts'] as List<dynamic>)
          .map((e) => TradingAccount.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$CampaignToJson(_Campaign instance) => <String, dynamic>{
  'id': instance.id,
  'bonusType': instance.bonusType,
  'bonusValue': instance.bonusValue,
  'minDepositAmount': instance.minDepositAmount,
  'depositCurrency': instance.depositCurrency,
  'qualificationText': instance.qualificationText,
  'tradingAccounts': instance.tradingAccounts,
};

_TradingAccount _$TradingAccountFromJson(Map<String, dynamic> json) =>
    _TradingAccount(
      id: json['id'] as String,
      type: json['type'] as String,
      swapEnabled: json['is_swap_free'] as bool,
    );

Map<String, dynamic> _$TradingAccountToJson(_TradingAccount instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'is_swap_free': instance.swapEnabled,
    };
