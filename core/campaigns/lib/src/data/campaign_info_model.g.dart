// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'campaign_info_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_CampaignInfoModel _$CampaignInfoModelFromJson(Map<String, dynamic> json) =>
    _CampaignInfoModel(
      success: json['success'] as bool,
      data: CampaignInfoData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CampaignInfoModelToJson(_CampaignInfoModel instance) =>
    <String, dynamic>{'success': instance.success, 'data': instance.data};

_CampaignInfoData _$CampaignInfoDataFromJson(Map<String, dynamic> json) =>
    _CampaignInfoData(
      id: json['id'] as String,
      bonusType: json['bonusType'] as String,
      bonusValue: (json['bonusValue'] as num).toInt(),
      minDepositAmount: (json['minDepositAmount'] as num).toInt(),
      depositCurrency: json['depositCurrency'] as String,
      metadata: CampaignMetadata.fromJson(
        json['metadata'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$CampaignInfoDataToJson(_CampaignInfoData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'bonusType': instance.bonusType,
      'bonusValue': instance.bonusValue,
      'minDepositAmount': instance.minDepositAmount,
      'depositCurrency': instance.depositCurrency,
      'metadata': instance.metadata,
    };

_CampaignMetadata _$CampaignMetadataFromJson(Map<String, dynamic> json) =>
    _CampaignMetadata(
      headerItem: CampaignItem.fromJson(
        json['headerItem'] as Map<String, dynamic>,
      ),
      qualificationItem: CampaignItem.fromJson(
        json['qualificationItem'] as Map<String, dynamic>,
      ),
      optionItem: CampaignOptionItem.fromJson(
        json['optionItem'] as Map<String, dynamic>,
      ),
      qualificationDeclinedItem: CampaignItem.fromJson(
        json['qualificationDeclinedItem'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$CampaignMetadataToJson(_CampaignMetadata instance) =>
    <String, dynamic>{
      'headerItem': instance.headerItem,
      'qualificationItem': instance.qualificationItem,
      'optionItem': instance.optionItem,
      'qualificationDeclinedItem': instance.qualificationDeclinedItem,
    };

_CampaignItem _$CampaignItemFromJson(Map<String, dynamic> json) =>
    _CampaignItem(
      title: json['title'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$CampaignItemToJson(_CampaignItem instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
    };

_CampaignOptionItem _$CampaignOptionItemFromJson(Map<String, dynamic> json) =>
    _CampaignOptionItem(
      title: json['title'] as String,
      description: json['description'] as String,
      primaryCtaText: json['primaryCtaText'] as String,
      primaryCtaOnClickDesc: json['primaryCtaOnClickDesc'] as String,
      secondaryCtaText: json['secondaryCtaText'] as String,
      secondaryCtaOnClickDesc: json['secondaryCtaOnClickDesc'] as String,
      tnc: json['tnc'] as String?,
    );

Map<String, dynamic> _$CampaignOptionItemToJson(_CampaignOptionItem instance) =>
    <String, dynamic>{
      'title': instance.title,
      'description': instance.description,
      'primaryCtaText': instance.primaryCtaText,
      'primaryCtaOnClickDesc': instance.primaryCtaOnClickDesc,
      'secondaryCtaText': instance.secondaryCtaText,
      'secondaryCtaOnClickDesc': instance.secondaryCtaOnClickDesc,
      'tnc': instance.tnc,
    };
