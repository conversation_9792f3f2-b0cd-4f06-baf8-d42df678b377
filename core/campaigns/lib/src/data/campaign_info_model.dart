import 'package:freezed_annotation/freezed_annotation.dart';

part 'campaign_info_model.freezed.dart';
part 'campaign_info_model.g.dart';

/// Response model for campaign info API
@freezed
sealed class CampaignInfoModel with _$CampaignInfoModel {
  const factory CampaignInfoModel({
    required bool success,
    required CampaignInfoData data,
  }) = _CampaignInfoModel;

  factory CampaignInfoModel.fromJson(Map<String, dynamic> json) =>
      _$CampaignInfoModelFromJson(json);
}

/// Campaign info data model
@freezed
sealed class CampaignInfoData with _$CampaignInfoData {
  const factory CampaignInfoData({
    required String id,
    required String bonusType,
    required int bonusValue,
    required int minDepositAmount,
    required String depositCurrency,
    required CampaignMetadata metadata,
  }) = _CampaignInfoData;

  factory CampaignInfoData.fromJson(Map<String, dynamic> json) =>
      _$CampaignInfoDataFromJson(json);
}

/// Campaign metadata model
@freezed
sealed class CampaignMetadata with _$CampaignMetadata {
  const factory CampaignMetadata({
    required CampaignItem headerItem,
    required CampaignItem qualificationItem,
    required CampaignOptionItem optionItem,
    required CampaignItem qualificationDeclinedItem,
  }) = _CampaignMetadata;

  factory CampaignMetadata.fromJson(Map<String, dynamic> json) =>
      _$CampaignMetadataFromJson(json);
}

/// Campaign item model
@freezed
sealed class CampaignItem with _$CampaignItem {
  const factory CampaignItem({required String title, String? description}) =
      _CampaignItem;

  factory CampaignItem.fromJson(Map<String, dynamic> json) =>
      _$CampaignItemFromJson(json);
}

/// Campaign option item model
@freezed
sealed class CampaignOptionItem with _$CampaignOptionItem {
  const factory CampaignOptionItem({
    required String title,
    required String description,
    required String primaryCtaText,
    required String primaryCtaOnClickDesc,
    required String secondaryCtaText,
    required String secondaryCtaOnClickDesc,
    String? tnc,
  }) = _CampaignOptionItem;

  factory CampaignOptionItem.fromJson(Map<String, dynamic> json) =>
      _$CampaignOptionItemFromJson(json);
}
