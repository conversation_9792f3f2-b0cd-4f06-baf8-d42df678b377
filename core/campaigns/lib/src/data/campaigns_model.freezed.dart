// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'campaigns_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CampaignsModel {

 bool get success; CampaignsData get data;
/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsModelCopyWith<CampaignsModel> get copyWith => _$CampaignsModelCopyWithImpl<CampaignsModel>(this as CampaignsModel, _$identity);

  /// Serializes this CampaignsModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'CampaignsModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class $CampaignsModelCopyWith<$Res>  {
  factory $CampaignsModelCopyWith(CampaignsModel value, $Res Function(CampaignsModel) _then) = _$CampaignsModelCopyWithImpl;
@useResult
$Res call({
 bool success, CampaignsData data
});


$CampaignsDataCopyWith<$Res> get data;

}
/// @nodoc
class _$CampaignsModelCopyWithImpl<$Res>
    implements $CampaignsModelCopyWith<$Res> {
  _$CampaignsModelCopyWithImpl(this._self, this._then);

  final CampaignsModel _self;
  final $Res Function(CampaignsModel) _then;

/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? success = null,Object? data = null,}) {
  return _then(_self.copyWith(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as CampaignsData,
  ));
}
/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsDataCopyWith<$Res> get data {
  
  return $CampaignsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _CampaignsModel implements CampaignsModel {
  const _CampaignsModel({required this.success, required this.data});
  factory _CampaignsModel.fromJson(Map<String, dynamic> json) => _$CampaignsModelFromJson(json);

@override final  bool success;
@override final  CampaignsData data;

/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignsModelCopyWith<_CampaignsModel> get copyWith => __$CampaignsModelCopyWithImpl<_CampaignsModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignsModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignsModel&&(identical(other.success, success) || other.success == success)&&(identical(other.data, data) || other.data == data));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,success,data);

@override
String toString() {
  return 'CampaignsModel(success: $success, data: $data)';
}


}

/// @nodoc
abstract mixin class _$CampaignsModelCopyWith<$Res> implements $CampaignsModelCopyWith<$Res> {
  factory _$CampaignsModelCopyWith(_CampaignsModel value, $Res Function(_CampaignsModel) _then) = __$CampaignsModelCopyWithImpl;
@override @useResult
$Res call({
 bool success, CampaignsData data
});


@override $CampaignsDataCopyWith<$Res> get data;

}
/// @nodoc
class __$CampaignsModelCopyWithImpl<$Res>
    implements _$CampaignsModelCopyWith<$Res> {
  __$CampaignsModelCopyWithImpl(this._self, this._then);

  final _CampaignsModel _self;
  final $Res Function(_CampaignsModel) _then;

/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? success = null,Object? data = null,}) {
  return _then(_CampaignsModel(
success: null == success ? _self.success : success // ignore: cast_nullable_to_non_nullable
as bool,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as CampaignsData,
  ));
}

/// Create a copy of CampaignsModel
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$CampaignsDataCopyWith<$Res> get data {
  
  return $CampaignsDataCopyWith<$Res>(_self.data, (value) {
    return _then(_self.copyWith(data: value));
  });
}
}


/// @nodoc
mixin _$CampaignsData {

 List<Campaign> get campaigns;
/// Create a copy of CampaignsData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignsDataCopyWith<CampaignsData> get copyWith => _$CampaignsDataCopyWithImpl<CampaignsData>(this as CampaignsData, _$identity);

  /// Serializes this CampaignsData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is CampaignsData&&const DeepCollectionEquality().equals(other.campaigns, campaigns));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(campaigns));

@override
String toString() {
  return 'CampaignsData(campaigns: $campaigns)';
}


}

/// @nodoc
abstract mixin class $CampaignsDataCopyWith<$Res>  {
  factory $CampaignsDataCopyWith(CampaignsData value, $Res Function(CampaignsData) _then) = _$CampaignsDataCopyWithImpl;
@useResult
$Res call({
 List<Campaign> campaigns
});




}
/// @nodoc
class _$CampaignsDataCopyWithImpl<$Res>
    implements $CampaignsDataCopyWith<$Res> {
  _$CampaignsDataCopyWithImpl(this._self, this._then);

  final CampaignsData _self;
  final $Res Function(CampaignsData) _then;

/// Create a copy of CampaignsData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? campaigns = null,}) {
  return _then(_self.copyWith(
campaigns: null == campaigns ? _self.campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as List<Campaign>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _CampaignsData implements CampaignsData {
  const _CampaignsData({required final  List<Campaign> campaigns}): _campaigns = campaigns;
  factory _CampaignsData.fromJson(Map<String, dynamic> json) => _$CampaignsDataFromJson(json);

 final  List<Campaign> _campaigns;
@override List<Campaign> get campaigns {
  if (_campaigns is EqualUnmodifiableListView) return _campaigns;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_campaigns);
}


/// Create a copy of CampaignsData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignsDataCopyWith<_CampaignsData> get copyWith => __$CampaignsDataCopyWithImpl<_CampaignsData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignsDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _CampaignsData&&const DeepCollectionEquality().equals(other._campaigns, _campaigns));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_campaigns));

@override
String toString() {
  return 'CampaignsData(campaigns: $campaigns)';
}


}

/// @nodoc
abstract mixin class _$CampaignsDataCopyWith<$Res> implements $CampaignsDataCopyWith<$Res> {
  factory _$CampaignsDataCopyWith(_CampaignsData value, $Res Function(_CampaignsData) _then) = __$CampaignsDataCopyWithImpl;
@override @useResult
$Res call({
 List<Campaign> campaigns
});




}
/// @nodoc
class __$CampaignsDataCopyWithImpl<$Res>
    implements _$CampaignsDataCopyWith<$Res> {
  __$CampaignsDataCopyWithImpl(this._self, this._then);

  final _CampaignsData _self;
  final $Res Function(_CampaignsData) _then;

/// Create a copy of CampaignsData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? campaigns = null,}) {
  return _then(_CampaignsData(
campaigns: null == campaigns ? _self._campaigns : campaigns // ignore: cast_nullable_to_non_nullable
as List<Campaign>,
  ));
}


}


/// @nodoc
mixin _$Campaign {

 String get id; String get bonusType; int get bonusValue; int get minDepositAmount; String? get depositCurrency; String? get qualificationText; List<TradingAccount> get tradingAccounts;
/// Create a copy of Campaign
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$CampaignCopyWith<Campaign> get copyWith => _$CampaignCopyWithImpl<Campaign>(this as Campaign, _$identity);

  /// Serializes this Campaign to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Campaign&&(identical(other.id, id) || other.id == id)&&(identical(other.bonusType, bonusType) || other.bonusType == bonusType)&&(identical(other.bonusValue, bonusValue) || other.bonusValue == bonusValue)&&(identical(other.minDepositAmount, minDepositAmount) || other.minDepositAmount == minDepositAmount)&&(identical(other.depositCurrency, depositCurrency) || other.depositCurrency == depositCurrency)&&(identical(other.qualificationText, qualificationText) || other.qualificationText == qualificationText)&&const DeepCollectionEquality().equals(other.tradingAccounts, tradingAccounts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,bonusType,bonusValue,minDepositAmount,depositCurrency,qualificationText,const DeepCollectionEquality().hash(tradingAccounts));

@override
String toString() {
  return 'Campaign(id: $id, bonusType: $bonusType, bonusValue: $bonusValue, minDepositAmount: $minDepositAmount, depositCurrency: $depositCurrency, qualificationText: $qualificationText, tradingAccounts: $tradingAccounts)';
}


}

/// @nodoc
abstract mixin class $CampaignCopyWith<$Res>  {
  factory $CampaignCopyWith(Campaign value, $Res Function(Campaign) _then) = _$CampaignCopyWithImpl;
@useResult
$Res call({
 String id, String bonusType, int bonusValue, int minDepositAmount, String? depositCurrency, String? qualificationText, List<TradingAccount> tradingAccounts
});




}
/// @nodoc
class _$CampaignCopyWithImpl<$Res>
    implements $CampaignCopyWith<$Res> {
  _$CampaignCopyWithImpl(this._self, this._then);

  final Campaign _self;
  final $Res Function(Campaign) _then;

/// Create a copy of Campaign
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? bonusType = null,Object? bonusValue = null,Object? minDepositAmount = null,Object? depositCurrency = freezed,Object? qualificationText = freezed,Object? tradingAccounts = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bonusType: null == bonusType ? _self.bonusType : bonusType // ignore: cast_nullable_to_non_nullable
as String,bonusValue: null == bonusValue ? _self.bonusValue : bonusValue // ignore: cast_nullable_to_non_nullable
as int,minDepositAmount: null == minDepositAmount ? _self.minDepositAmount : minDepositAmount // ignore: cast_nullable_to_non_nullable
as int,depositCurrency: freezed == depositCurrency ? _self.depositCurrency : depositCurrency // ignore: cast_nullable_to_non_nullable
as String?,qualificationText: freezed == qualificationText ? _self.qualificationText : qualificationText // ignore: cast_nullable_to_non_nullable
as String?,tradingAccounts: null == tradingAccounts ? _self.tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccount>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Campaign implements Campaign {
  const _Campaign({required this.id, required this.bonusType, required this.bonusValue, required this.minDepositAmount, required this.depositCurrency, required this.qualificationText, required final  List<TradingAccount> tradingAccounts}): _tradingAccounts = tradingAccounts;
  factory _Campaign.fromJson(Map<String, dynamic> json) => _$CampaignFromJson(json);

@override final  String id;
@override final  String bonusType;
@override final  int bonusValue;
@override final  int minDepositAmount;
@override final  String? depositCurrency;
@override final  String? qualificationText;
 final  List<TradingAccount> _tradingAccounts;
@override List<TradingAccount> get tradingAccounts {
  if (_tradingAccounts is EqualUnmodifiableListView) return _tradingAccounts;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_tradingAccounts);
}


/// Create a copy of Campaign
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$CampaignCopyWith<_Campaign> get copyWith => __$CampaignCopyWithImpl<_Campaign>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$CampaignToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Campaign&&(identical(other.id, id) || other.id == id)&&(identical(other.bonusType, bonusType) || other.bonusType == bonusType)&&(identical(other.bonusValue, bonusValue) || other.bonusValue == bonusValue)&&(identical(other.minDepositAmount, minDepositAmount) || other.minDepositAmount == minDepositAmount)&&(identical(other.depositCurrency, depositCurrency) || other.depositCurrency == depositCurrency)&&(identical(other.qualificationText, qualificationText) || other.qualificationText == qualificationText)&&const DeepCollectionEquality().equals(other._tradingAccounts, _tradingAccounts));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,bonusType,bonusValue,minDepositAmount,depositCurrency,qualificationText,const DeepCollectionEquality().hash(_tradingAccounts));

@override
String toString() {
  return 'Campaign(id: $id, bonusType: $bonusType, bonusValue: $bonusValue, minDepositAmount: $minDepositAmount, depositCurrency: $depositCurrency, qualificationText: $qualificationText, tradingAccounts: $tradingAccounts)';
}


}

/// @nodoc
abstract mixin class _$CampaignCopyWith<$Res> implements $CampaignCopyWith<$Res> {
  factory _$CampaignCopyWith(_Campaign value, $Res Function(_Campaign) _then) = __$CampaignCopyWithImpl;
@override @useResult
$Res call({
 String id, String bonusType, int bonusValue, int minDepositAmount, String? depositCurrency, String? qualificationText, List<TradingAccount> tradingAccounts
});




}
/// @nodoc
class __$CampaignCopyWithImpl<$Res>
    implements _$CampaignCopyWith<$Res> {
  __$CampaignCopyWithImpl(this._self, this._then);

  final _Campaign _self;
  final $Res Function(_Campaign) _then;

/// Create a copy of Campaign
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? bonusType = null,Object? bonusValue = null,Object? minDepositAmount = null,Object? depositCurrency = freezed,Object? qualificationText = freezed,Object? tradingAccounts = null,}) {
  return _then(_Campaign(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,bonusType: null == bonusType ? _self.bonusType : bonusType // ignore: cast_nullable_to_non_nullable
as String,bonusValue: null == bonusValue ? _self.bonusValue : bonusValue // ignore: cast_nullable_to_non_nullable
as int,minDepositAmount: null == minDepositAmount ? _self.minDepositAmount : minDepositAmount // ignore: cast_nullable_to_non_nullable
as int,depositCurrency: freezed == depositCurrency ? _self.depositCurrency : depositCurrency // ignore: cast_nullable_to_non_nullable
as String?,qualificationText: freezed == qualificationText ? _self.qualificationText : qualificationText // ignore: cast_nullable_to_non_nullable
as String?,tradingAccounts: null == tradingAccounts ? _self._tradingAccounts : tradingAccounts // ignore: cast_nullable_to_non_nullable
as List<TradingAccount>,
  ));
}


}


/// @nodoc
mixin _$TradingAccount {

 String get id; String get type;@JsonKey(name: 'is_swap_free') bool get swapEnabled;
/// Create a copy of TradingAccount
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TradingAccountCopyWith<TradingAccount> get copyWith => _$TradingAccountCopyWithImpl<TradingAccount>(this as TradingAccount, _$identity);

  /// Serializes this TradingAccount to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TradingAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.swapEnabled, swapEnabled) || other.swapEnabled == swapEnabled));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,swapEnabled);

@override
String toString() {
  return 'TradingAccount(id: $id, type: $type, swapEnabled: $swapEnabled)';
}


}

/// @nodoc
abstract mixin class $TradingAccountCopyWith<$Res>  {
  factory $TradingAccountCopyWith(TradingAccount value, $Res Function(TradingAccount) _then) = _$TradingAccountCopyWithImpl;
@useResult
$Res call({
 String id, String type,@JsonKey(name: 'is_swap_free') bool swapEnabled
});




}
/// @nodoc
class _$TradingAccountCopyWithImpl<$Res>
    implements $TradingAccountCopyWith<$Res> {
  _$TradingAccountCopyWithImpl(this._self, this._then);

  final TradingAccount _self;
  final $Res Function(TradingAccount) _then;

/// Create a copy of TradingAccount
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? type = null,Object? swapEnabled = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,swapEnabled: null == swapEnabled ? _self.swapEnabled : swapEnabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _TradingAccount implements TradingAccount {
  const _TradingAccount({required this.id, required this.type, @JsonKey(name: 'is_swap_free') required this.swapEnabled});
  factory _TradingAccount.fromJson(Map<String, dynamic> json) => _$TradingAccountFromJson(json);

@override final  String id;
@override final  String type;
@override@JsonKey(name: 'is_swap_free') final  bool swapEnabled;

/// Create a copy of TradingAccount
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TradingAccountCopyWith<_TradingAccount> get copyWith => __$TradingAccountCopyWithImpl<_TradingAccount>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TradingAccountToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TradingAccount&&(identical(other.id, id) || other.id == id)&&(identical(other.type, type) || other.type == type)&&(identical(other.swapEnabled, swapEnabled) || other.swapEnabled == swapEnabled));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,type,swapEnabled);

@override
String toString() {
  return 'TradingAccount(id: $id, type: $type, swapEnabled: $swapEnabled)';
}


}

/// @nodoc
abstract mixin class _$TradingAccountCopyWith<$Res> implements $TradingAccountCopyWith<$Res> {
  factory _$TradingAccountCopyWith(_TradingAccount value, $Res Function(_TradingAccount) _then) = __$TradingAccountCopyWithImpl;
@override @useResult
$Res call({
 String id, String type,@JsonKey(name: 'is_swap_free') bool swapEnabled
});




}
/// @nodoc
class __$TradingAccountCopyWithImpl<$Res>
    implements _$TradingAccountCopyWith<$Res> {
  __$TradingAccountCopyWithImpl(this._self, this._then);

  final _TradingAccount _self;
  final $Res Function(_TradingAccount) _then;

/// Create a copy of TradingAccount
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? type = null,Object? swapEnabled = null,}) {
  return _then(_TradingAccount(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as String,swapEnabled: null == swapEnabled ? _self.swapEnabled : swapEnabled // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
