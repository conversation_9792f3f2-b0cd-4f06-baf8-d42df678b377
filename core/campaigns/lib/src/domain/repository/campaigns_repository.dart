import 'package:api_client/api_client.dart';
import 'package:campaigns/src/data/campaign_info_model.dart';
import 'package:campaigns/src/data/campaigns_model.dart';
import 'package:prelude/prelude.dart';

class CampaignsRepository {
  final ApiClientBase apiClient;
  const CampaignsRepository({required this.apiClient});

  TaskEither<Exception, CampaignsModel> getCampaigns() {
    return apiClient
        .get<Map<String, dynamic>>('api/v1/client/campaigns')
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              return CampaignsModel.fromJson(response.data!);
            },
            (e, s) {
              print('Error getting campaigns: ${e.toString()}');
              return Exception(e.toString());
            },
          );
        });
  }

  TaskEither<Exception, CampaignInfoModel> getCampaignInfo(
    String accountId,
    String accountCurrency,
    String campaignId,
  ) {
    return apiClient
        .get<Map<String, dynamic>>(
          'api/v1/client/campaignInfo',
          queryParams: {
            'campaignId': campaignId,
            'tradingAccountId': accountId,
            'tradingAccountCurrency': accountCurrency,
          },
        )
        .flatMap((response) {
          return TaskEither.tryCatch(
            () async {
              return CampaignInfoModel.fromJson(response.data!);
            },
            (e, s) {
              print('Error parsing campaign info: ${e.toString()}');
              print('Stack trace: $s');
              return Exception(e.toString());
            },
          );
        });
  }
}
