import 'package:campaigns/src/data/campaign_info_model.dart';
import 'package:campaigns/src/domain/repository/campaigns_repository.dart';
import 'package:prelude/prelude.dart';

class GetCampaignInfoUseCase {
  final CampaignsRepository repository;

  const GetCampaignInfoUseCase({required CampaignsRepository repository})
    : repository = repository;

  TaskEither<Exception, CampaignInfoModel> call(
    String accountId,
    String accountCurrency,
    String campaignId,
  ) {
    return repository.getCampaignInfo(accountId, accountCurrency, campaignId);
  }
}
