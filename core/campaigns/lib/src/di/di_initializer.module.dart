//@GeneratedMicroModule;CampaignsPackageModule;package:campaigns/src/di/di_initializer.module.dart
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i687;

import 'package:api_client/api_client.dart' as _i633;
import 'package:campaigns/src/di/campaigns_module.dart' as _i417;
import 'package:campaigns/src/domain/repository/campaigns_repository.dart'
    as _i600;
import 'package:campaigns/src/domain/usecase/get_campaign_info_use_case.dart'
    as _i151;
import 'package:campaigns/src/domain/usecase/get_campaigns_use_case.dart'
    as _i956;
import 'package:injectable/injectable.dart' as _i526;

class CampaignsPackageModule extends _i526.MicroPackageModule {
  // initializes the registration of main-scope dependencies inside of GetIt
  @override
  _i687.FutureOr<void> init(_i526.GetItHelper gh) {
    final campaignsModule = _$CampaignsModule();
    gh.factory<_i600.CampaignsRepository>(
      () => campaignsModule.campaignsRepository(
        gh<_i633.ApiClientBase>(instanceName: 'mobileBffApiClient'),
      ),
    );
    gh.factory<_i956.GetCampaignsUseCase>(
      () =>
          campaignsModule.getCampaignsUseCase(gh<_i600.CampaignsRepository>()),
    );
    gh.factory<_i151.GetCampaignInfoUseCase>(
      () => campaignsModule.getCampaignInfoUseCase(
        gh<_i600.CampaignsRepository>(),
      ),
    );
  }
}

class _$CampaignsModule extends _i417.CampaignsModule {}
