import 'package:api_client/api_client.dart';
import 'package:campaigns/src/domain/repository/campaigns_repository.dart';
import 'package:campaigns/src/domain/usecase/get_campaign_info_use_case.dart';
import 'package:campaigns/src/domain/usecase/get_campaigns_use_case.dart';
import 'package:injectable/injectable.dart';

@module
abstract class CampaignsModule {
  @injectable
  CampaignsRepository campaignsRepository(
    @Named('mobileBffApiClient') ApiClientBase apiClient,
  ) => CampaignsRepository(apiClient: apiClient);

  @injectable
  GetCampaignsUseCase getCampaignsUseCase(CampaignsRepository repository) =>
      GetCampaignsUseCase(repository: repository);

  @injectable
  GetCampaignInfoUseCase getCampaignInfoUseCase(
    CampaignsRepository repository,
  ) => GetCampaignInfoUseCase(repository: repository);
}
