import 'package:duplo/src/assets/assets.gen.dart';
import 'package:duplo/src/components/duplo_alert/alert_type.dart';
import 'package:duplo/src/components/duplo_buttons/duplo_button.dart';
import 'package:duplo/src/components/duplo_tap/duplo_tap.dart';

import 'package:duplo/src/theming/duplo_theme.dart';
import 'package:duplo/src/typography/duplo_font_weight.dart';
import 'package:duplo/src/typography/duplo_text.dart';
import 'package:duplo/src/typography/duplo_text_styles.dart';
import 'package:flutter/material.dart';

/// A customizable alert message widget that displays information, warnings, or errors
/// with optional expandable content and action buttons.
///
/// The [DuploAlertMessage] provides three predefined alert types:
/// - [DuploAlertMessage.info] - For informational messages (blue theme)
/// - [DuploAlertMessage.warning] - For warning messages (yellow theme)
/// - [DuploAlertMessage.error] - For error messages (red theme)
///
/// ## Features
/// - **Multiple Alert Types**: Info, warning, and error variants with appropriate styling
/// - **Flexible Content**: Support for both plain text and rich text titles
/// - **Expandable Content**: Optional expandable section for additional details
/// - **Action Buttons**: Primary and secondary action buttons with callbacks
/// - **Customizable Icons**: Default icons for each type, with option to override
/// - **RTL Support**: Proper layout handling for right-to-left languages
///
/// ## Basic Usage
/// ```dart
/// // Simple info alert
/// DuploAlertMessage.info(
///   title: 'Information',
///   subtitle: 'This is an informational message',
/// )
///
/// // Warning with action
/// DuploAlertMessage.warning(
///   title: 'Warning',
///   subtitle: 'Please review your settings',
///   primaryAction: 'Review',
///   onTapPrimaryAction: () => print('Review tapped'),
/// )
///
/// // Error with expandable content
/// DuploAlertMessage.error(
///   title: 'Error occurred',
///   subtitle: 'Something went wrong',
///   children: [
///     Text('Detailed error information...'),
///   ],
/// )
/// ```
///
/// ## Advanced Usage
/// ```dart
/// // Rich text title with custom styling
/// DuploAlertMessage.info(
///   richTitle: DuploText(
///     text: 'Custom Title',
///     style: DuploTextStyles.of(context).textLg,
///     fontWeight: DuploFontWeight.bold,
///   ),
///   subtitle: 'With custom styling',
///   children: [
///     Padding(
///       padding: EdgeInsets.all(8.0),
///       child: Text('Additional content here'),
///     ),
///   ],
///   primaryAction: 'Primary',
///   secondaryAction: 'Secondary',
///   onTapPrimaryAction: () => handlePrimary(),
///   onTapSecondaryAction: () => handleSecondary(),
/// )
/// ```
///
/// ## Styling
/// The alert automatically applies appropriate colors based on the alert type:
/// - **Info**: Uses secondary background with primary border
/// - **Warning**: Uses warning-50 background with warning-200 border
/// - **Error**: Uses error-50 background with error-200 border
///
/// ## Accessibility
/// - Supports screen readers through semantic structure
/// - Proper focus management for interactive elements
/// - High contrast colors for better visibility

class DuploAlertMessage extends StatefulWidget {
  /// Private constructor for [DuploAlertMessage].
  ///
  /// Use the factory constructors [DuploAlertMessage.info], [DuploAlertMessage.warning],
  /// or [DuploAlertMessage.error] instead of this constructor.
  const DuploAlertMessage._({
    super.key,
    this.title,
    this.richTitle,
    this.titleFontWeight = DuploFontWeight.semiBold,
    this.subtitle,
    this.richSubtitle,
    required this.leading,
    required this.alertType,
    this.trailing,
    this.children = const [],
    this.primaryAction,
    this.secondaryAction,
    this.onTapPrimaryAction,
    this.onTapSecondaryAction,
    this.hasSecondaryButton = false,
  });

  /// Creates an informational alert message with blue styling.
  ///
  /// This factory constructor creates an alert with info styling (blue background and border)
  /// and a default info icon. Use this for general informational messages that don't require
  /// immediate user attention.
  ///
  /// ## Parameters
  /// - [title] - Plain text title for the alert. Either [title] or [richTitle] must be provided.
  /// - [richTitle] - Rich text title with custom styling. Either [title] or [richTitle] must be provided.
  /// - [titleFontWeight] - Font weight for the title text. Defaults to [DuploFontWeight.semiBold].
  /// - [subtitle] - Optional subtitle text displayed below the title.
  /// - [leading] - Custom leading widget. If null, uses the default info icon.
  /// - [trailing] - Optional trailing widget displayed on the right side.
  /// - [children] - List of widgets to display in the expandable section.
  /// - [primaryAction] - Text for the primary action button.
  /// - [secondaryAction] - Text for the secondary action button.
  /// - [onTapPrimaryAction] - Callback for primary action button tap.
  /// - [onTapSecondaryAction] - Callback for secondary action button tap.
  ///
  /// ## Example
  /// ```dart
  /// DuploAlertMessage.info(
  ///   title: 'New Feature Available',
  ///   subtitle: 'Check out our latest update',
  ///   primaryAction: 'Learn More',
  ///   onTapPrimaryAction: () => showFeatureDetails(),
  /// )
  /// ```
  factory DuploAlertMessage.info({
    Key? key,
    String? title,
    DuploText? richTitle,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? richSubtitle,
    Widget? leading,
    Widget? trailing,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
    bool hasSecondaryButton = false,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      richTitle: richTitle,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      leading: leading ?? Assets.images.duploAlertInfo.svg(),
      trailing: trailing,
      children: children,
      alertType: AlertType.info,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
      hasSecondaryButton: hasSecondaryButton,
      richSubtitle: richSubtitle,
    );
  }

  /// Creates a warning alert message with yellow styling.
  ///
  /// This factory constructor creates an alert with warning styling (yellow background and border)
  /// and a default warning icon. Use this for messages that require user attention but are not
  /// critical errors.
  ///
  /// ## Parameters
  /// - [title] - Plain text title for the alert. Either [title] or [richTitle] must be provided.
  /// - [richTitle] - Rich text title with custom styling. Either [title] or [richTitle] must be provided.
  /// - [titleFontWeight] - Font weight for the title text. Defaults to [DuploFontWeight.semiBold].
  /// - [subtitle] - Optional subtitle text displayed below the title.
  /// - [leading] - Custom leading widget. If null, uses the default warning icon.
  /// - [trailing] - Optional trailing widget displayed on the right side.
  /// - [children] - List of widgets to display in the expandable section.
  /// - [primaryAction] - Text for the primary action button.
  /// - [secondaryAction] - Text for the secondary action button.
  /// - [onTapPrimaryAction] - Callback for primary action button tap.
  /// - [onTapSecondaryAction] - Callback for secondary action button tap.
  ///
  /// ## Example
  /// ```dart
  /// DuploAlertMessage.warning(
  ///   title: 'Account Verification Required',
  ///   subtitle: 'Please verify your email address',
  ///   primaryAction: 'Verify Now',
  ///   onTapPrimaryAction: () => startVerification(),
  /// )
  /// ```
  factory DuploAlertMessage.warning({
    Key? key,
    String? title,
    DuploText? richTitle,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? richSubtitle,
    Widget? leading,
    Widget? trailing,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
    bool hasSecondaryButton = false,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      richTitle: richTitle,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      richSubtitle: richSubtitle,
      leading: leading ?? Assets.images.duploAlertWarning.svg(),
      trailing: trailing,
      children: children,
      alertType: AlertType.warning,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
      hasSecondaryButton: hasSecondaryButton,
    );
  }

  /// Creates an error alert message with red styling.
  ///
  /// This factory constructor creates an alert with error styling (red background and border)
  /// and a default error icon. Use this for critical error messages that require immediate
  /// user attention or action.
  ///
  /// ## Parameters
  /// - [title] - Plain text title for the alert. Either [title] or [richTitle] must be provided.
  /// - [richTitle] - Rich text title with custom styling. Either [title] or [richTitle] must be provided.
  /// - [titleFontWeight] - Font weight for the title text. Defaults to [DuploFontWeight.semiBold].
  /// - [subtitle] - Optional subtitle text displayed below the title.
  /// - [leading] - Custom leading widget. If null, uses the default error icon.
  /// - [trailing] - Optional trailing widget displayed on the right side.
  /// - [children] - List of widgets to display in the expandable section.
  /// - [primaryAction] - Text for the primary action button.
  /// - [secondaryAction] - Text for the secondary action button.
  /// - [onTapPrimaryAction] - Callback for primary action button tap.
  /// - [onTapSecondaryAction] - Callback for secondary action button tap.
  ///
  /// ## Example
  /// ```dart
  /// DuploAlertMessage.error(
  ///   title: 'Connection Failed',
  ///   subtitle: 'Unable to connect to server',
  ///   primaryAction: 'Retry',
  ///   secondaryAction: 'Cancel',
  ///   onTapPrimaryAction: () => retryConnection(),
  ///   onTapSecondaryAction: () => cancelOperation(),
  /// )
  /// ```
  factory DuploAlertMessage.error({
    Key? key,
    String? title,
    DuploText? richTitle,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? richSubtitle,
    Widget? leading,
    Widget? trailing,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
    bool hasSecondaryButton = false,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      richTitle: richTitle,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      richSubtitle: richSubtitle,
      leading: leading ?? Assets.images.duploAlertError.svg(),
      trailing: trailing,
      children: children,
      alertType: AlertType.error,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
      hasSecondaryButton: hasSecondaryButton,
    );
  }

  /// Creates a brand alert message with brand-specific styling.
  ///
  /// This factory constructor creates an alert with brand styling and a default brand icon.
  /// Use this for promotional messages, brand announcements, or special offers that align
  /// with the brand identity and require distinctive visual treatment.
  ///
  /// ## Parameters
  /// - [title] - Plain text title for the alert. Either [title] or [richTitle] must be provided.
  /// - [richTitle] - Rich text title with custom styling. Either [title] or [richTitle] must be provided.
  /// - [titleFontWeight] - Font weight for the title text. Defaults to [DuploFontWeight.semiBold].
  /// - [subtitle] - Optional subtitle text displayed below the title.
  /// - [leading] - Custom leading widget. If null, uses the default brand icon.
  /// - [trailing] - Optional trailing widget displayed on the right side.
  /// - [children] - List of widgets to display in the expandable section.
  /// - [primaryAction] - Text for the primary action button.
  /// - [secondaryAction] - Text for the secondary action button.
  /// - [onTapPrimaryAction] - Callback for primary action button tap.
  /// - [onTapSecondaryAction] - Callback for secondary action button tap.
  ///
  /// ## Example
  /// ```dart
  /// DuploAlertMessage.brand(
  ///   title: 'Welcome Bonus',
  ///   subtitle: 'Get 100% bonus on your first deposit',
  ///   primaryAction: 'Claim Now',
  ///   secondaryAction: 'Learn More',
  ///   onTapPrimaryAction: () => claimBonus(),
  ///   onTapSecondaryAction: () => showBonusDetails(),
  /// )
  /// ```
  factory DuploAlertMessage.brand({
    Key? key,
    String? title,
    DuploText? richTitle,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? richSubtitle,
    Widget? leading,
    Widget? trailing,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
    bool hasSecondaryButton = false,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      richTitle: richTitle,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      richSubtitle: richSubtitle,
      leading: leading ?? Assets.images.welcomeBonusIc.svg(),
      trailing: trailing,
      children: children,
      alertType: AlertType.brand,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
      hasSecondaryButton: hasSecondaryButton,
    );
  }

  /// Creates a success alert message with green styling.
  ///
  /// This factory constructor creates an alert with success styling (green background and border)
  /// and a default success icon. Use this for positive feedback messages, successful operations,
  /// or confirmation messages that indicate successful completion of an action.
  ///
  /// ## Parameters
  /// - [title] - Plain text title for the alert. Either [title] or [richTitle] must be provided.
  /// - [richTitle] - Rich text title with custom styling. Either [title] or [richTitle] must be provided.
  /// - [titleFontWeight] - Font weight for the title text. Defaults to [DuploFontWeight.semiBold].
  /// - [subtitle] - Optional subtitle text displayed below the title.
  /// - [leading] - Custom leading widget. If null, uses the default success icon.
  /// - [trailing] - Optional trailing widget displayed on the right side.
  /// - [children] - List of widgets to display in the expandable section.
  /// - [primaryAction] - Text for the primary action button.
  /// - [secondaryAction] - Text for the secondary action button.
  /// - [onTapPrimaryAction] - Callback for primary action button tap.
  /// - [onTapSecondaryAction] - Callback for secondary action button tap.
  ///
  /// ## Example
  /// ```dart
  /// DuploAlertMessage.success(
  ///   title: 'Payment Successful',
  ///   subtitle: 'Your payment has been processed',
  ///   primaryAction: 'View Details',
  ///   secondaryAction: 'Close',
  ///   onTapPrimaryAction: () => viewPaymentDetails(),
  ///   onTapSecondaryAction: () => closeDialog(),
  /// )
  /// ```
  factory DuploAlertMessage.success({
    Key? key,
    String? title,
    DuploText? richTitle,
    DuploFontWeight titleFontWeight = DuploFontWeight.semiBold,
    String? subtitle,
    Widget? richSubtitle,
    Widget? leading,
    Widget? trailing,
    List<Widget> children = const [],
    String? primaryAction,
    String? secondaryAction,
    VoidCallback? onTapPrimaryAction,
    VoidCallback? onTapSecondaryAction,
    bool hasSecondaryButton = false,
  }) {
    return DuploAlertMessage._(
      key: key,
      title: title,
      richTitle: richTitle,
      titleFontWeight: titleFontWeight,
      subtitle: subtitle,
      richSubtitle: richSubtitle,
      leading: leading ?? Assets.images.welcomeBonusIc.svg(),
      trailing: trailing,
      children: children,
      alertType: AlertType.success,
      primaryAction: primaryAction,
      secondaryAction: secondaryAction,
      onTapPrimaryAction: onTapPrimaryAction,
      onTapSecondaryAction: onTapSecondaryAction,
      hasSecondaryButton: hasSecondaryButton,
    );
  }

  /// Plain text title for the alert.
  ///
  /// Either [title] or [richTitle] must be provided, but not both.
  /// This is displayed as the main heading of the alert message.
  final String? title;

  /// Rich text title with custom styling.
  ///
  /// Either [title] or [richTitle] must be provided, but not both.
  /// Use this when you need custom text styling beyond the default title appearance.
  final DuploText? richTitle;

  /// Font weight for the title text.
  ///
  /// Defaults to [DuploFontWeight.semiBold]. This only applies when using [title],
  /// not [richTitle] (which has its own styling).
  final DuploFontWeight titleFontWeight;

  /// Optional subtitle text displayed below the title.
  ///
  /// Provides additional context or details about the alert message.
  /// Styled with tertiary text color for visual hierarchy.
  final String? subtitle;

  /// Optional rich subtitle text displayed below the title.
  final Widget? richSubtitle;

  /// List of widgets to display in the expandable section.
  ///
  /// When this list is not empty, the alert becomes expandable and shows
  /// a trailing icon. Users can tap to expand/collapse the additional content.
  final List<Widget> children;

  /// Leading widget displayed on the left side of the alert.
  ///
  /// Typically an icon that represents the alert type. Each factory constructor
  /// provides a default icon, but this can be overridden with a custom widget.
  final Widget leading;

  /// Optional trailing widget displayed on the right side.
  ///
  /// This is separate from the expand/collapse icon that appears when [children]
  /// is not empty. Use this for additional custom content or actions.
  final Widget? trailing;

  /// The type of alert which determines the color scheme.
  ///
  /// This affects the background color, border color, and default icon.
  /// Set automatically by the factory constructors.
  final AlertType alertType;

  /// Text for the primary action button.
  ///
  /// When provided, displays a clickable text button at the bottom of the alert.
  /// Typically used for the main action the user should take.
  final String? primaryAction;

  /// Text for the secondary action button.
  ///
  /// When provided, displays a clickable text button next to the primary action.
  /// Typically used for alternative or cancel actions.
  final String? secondaryAction;

  /// Callback function for primary action button tap.
  ///
  /// Called when the user taps the primary action button.
  /// Should handle the main action associated with the alert.
  final VoidCallback? onTapPrimaryAction;

  /// Callback function for secondary action button tap.
  ///
  /// Called when the user taps the secondary action button.
  /// Should handle the alternative or cancel action.
  final VoidCallback? onTapSecondaryAction;

  /// Whether the primary button has a container
  final bool hasSecondaryButton;

  @override
  State<DuploAlertMessage> createState() => _DuploAlertMessageState();
}

/// State class for [DuploAlertMessage].
///
/// Manages the visual appearance and color scheme based on the alert type.
class _DuploAlertMessageState extends State<DuploAlertMessage> {
  /// Returns the background color for the given alert type.
  ///
  /// - [AlertType.info]: Secondary background color
  /// - [AlertType.warning]: Warning-50 utility color
  /// - [AlertType.error]: Error-50 utility color
  Color _getColor(AlertType alertType) {
    switch (alertType) {
      case AlertType.info:
        return DuploTheme.of(context).background.bgSecondary;
      case AlertType.warning:
        return DuploTheme.of(context).utility.utilityWarning50;
      case AlertType.error:
        return DuploTheme.of(context).utility.utilityError50;
      case AlertType.brand:
        return DuploTheme.of(context).utility.utilityBrand50;
      case AlertType.success:
        return DuploTheme.of(context).utility.utilitySuccess50;
    }
  }

  /// Returns the border color for the given alert type.
  ///
  /// - [AlertType.info]: Primary border color
  /// - [AlertType.warning]: Warning-200 utility color
  /// - [AlertType.error]: Error-200 utility color
  Color _getBorderColor(AlertType alertType) {
    switch (alertType) {
      case AlertType.info:
        return DuploTheme.of(context).border.borderPrimary;
      case AlertType.warning:
        return DuploTheme.of(context).utility.utilityWarning200;
      case AlertType.error:
        return DuploTheme.of(context).utility.utilityError200;
      case AlertType.brand:
        return DuploTheme.of(context).utility.utilityBrand200;
      case AlertType.success:
        return DuploTheme.of(context).utility.utilitySuccess200;
    }
  }

  /// Builds the alert message widget.
  ///
  /// Creates a decorated container with:
  /// - Rounded corners (6px border radius)
  /// - Alert type-specific background and border colors
  /// - Expandable content area when [children] is provided
  /// - Action buttons when [primaryAction] or [secondaryAction] is provided
  /// - Proper RTL layout support
  /// - Leading icon positioned absolutely for consistent alignment
  @override
  Widget build(BuildContext context) {
    final textStyles = DuploTextStyles.of(context);
    final theme = DuploTheme.of(context);
    final isRtl = Directionality.of(context) == TextDirection.rtl;
    final showTrailing = widget.children.isNotEmpty || widget.trailing != null;
    final expandable = widget.children.isNotEmpty;
    return Theme(
      data: ThemeData(
        dividerColor: Colors.transparent,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
      ),
      child: Stack(
        children: [
          DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: _getColor(widget.alertType),
              border: Border.all(color: _getBorderColor(widget.alertType)),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(height: 4),
                IgnorePointer(
                  ignoring: !expandable,
                  child: ExpansionTile(
                    leading: SizedBox(),
                    title:
                        widget.richTitle ??
                        (widget.title != null
                            ? DuploText(
                              textAlign: TextAlign.start,
                              text: widget.title!,
                              style: textStyles.textSm,
                              fontWeight: widget.titleFontWeight,
                              color: theme.text.textSecondary,
                            )
                            : SizedBox.shrink()),

                    subtitle:
                        widget.richSubtitle ??
                        (widget.subtitle != null
                            ? Padding(
                              padding: const EdgeInsets.only(top: 0.0),
                              child: DuploText(
                                textAlign: TextAlign.start,
                                text: widget.subtitle!,
                                style: textStyles.textSm,
                                color: theme.text.textTertiary,
                              ),
                            )
                            : null),
                    showTrailingIcon: showTrailing,
                    trailing: widget.trailing,
                    children: widget.children,
                    expandedCrossAxisAlignment: CrossAxisAlignment.start,
                    dense: true,
                    visualDensity: VisualDensity.compact,
                    childrenPadding: EdgeInsets.fromLTRB(52, 0, 52, 16),
                  ),
                ),

                if (widget.primaryAction != null ||
                    widget.secondaryAction != null) ...[
                  SizedBox(height: 4),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 52,
                      right: 52,
                      bottom: 12,
                    ),
                    child: Align(
                      alignment:
                          isRtl ? Alignment.centerRight : Alignment.centerLeft,
                      child: FittedBox(
                        fit: BoxFit.none,
                        child: Row(
                          children: [
                            if (widget.primaryAction != null)
                              !widget.hasSecondaryButton
                                  ? DuploTap(
                                    onTap: widget.onTapPrimaryAction,
                                    child: DuploText(
                                      text: widget.primaryAction,
                                      style: textStyles.textMd,
                                      fontWeight: DuploFontWeight.semiBold,
                                      color: theme.button.buttonTertiaryFg,
                                    ),
                                  )
                                  : DuploButton.secondary(
                                    title: widget.primaryAction!,
                                    onTap:
                                        widget.onTapPrimaryAction ??
                                        () {
                                          widget.onTapPrimaryAction?.call();
                                        },
                                  ),
                            if (widget.primaryAction != null &&
                                widget.secondaryAction != null)
                              SizedBox(width: 12),
                            if (widget.secondaryAction != null)
                              DuploTap(
                                onTap: widget.onTapSecondaryAction,
                                child: DuploText(
                                  text: widget.secondaryAction,
                                  style: textStyles.textMd,
                                  fontWeight: DuploFontWeight.semiBold,
                                  color: theme.button.buttonTertiaryFg,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
                SizedBox(height: 4),
              ],
            ),
          ),
          Positioned(
            left: isRtl ? null : 16,
            right: isRtl ? 16 : null,
            top: 14,
            child: widget.leading,
          ),
        ],
      ),
    );
  }
}
