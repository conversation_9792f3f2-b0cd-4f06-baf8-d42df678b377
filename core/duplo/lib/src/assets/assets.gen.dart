// dart format width=80

/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: deprecated_member_use,directives_ordering,implicit_dynamic_list_literal,unnecessary_import

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $TestGen {
  const $TestGen();

  /// Directory path: test/assets
  $TestAssetsGen get assets => const $TestAssetsGen();
}

class $AssetsAnimationsGen {
  const $AssetsAnimationsGen();

  /// File path: assets/animations/loading.json
  String get loading => 'packages/duplo/assets/animations/loading.json';

  /// File path: assets/animations/risk_warning_dark.json
  String get riskWarningDark =>
      'packages/duplo/assets/animations/risk_warning_dark.json';

  /// File path: assets/animations/risk_warning_light.json
  String get riskWarningLight =>
      'packages/duplo/assets/animations/risk_warning_light.json';

  /// List of all assets
  List<String> get values => [loading, riskWarningDark, riskWarningLight];
}

class $AssetsFlagsGen {
  const $AssetsFlagsGen();

  /// File path: assets/flags/Afghanistan.svg
  SvgGenImage get afghanistan =>
      const SvgGenImage('assets/flags/Afghanistan.svg');

  /// File path: assets/flags/Aland-Islands.svg
  SvgGenImage get alandIslands =>
      const SvgGenImage('assets/flags/Aland-Islands.svg');

  /// File path: assets/flags/Albania.svg
  SvgGenImage get albania => const SvgGenImage('assets/flags/Albania.svg');

  /// File path: assets/flags/Algeria.svg
  SvgGenImage get algeria => const SvgGenImage('assets/flags/Algeria.svg');

  /// File path: assets/flags/American-Samoa.svg
  SvgGenImage get americanSamoa =>
      const SvgGenImage('assets/flags/American-Samoa.svg');

  /// File path: assets/flags/Andorra.svg
  SvgGenImage get andorra => const SvgGenImage('assets/flags/Andorra.svg');

  /// File path: assets/flags/Angola.svg
  SvgGenImage get angola => const SvgGenImage('assets/flags/Angola.svg');

  /// File path: assets/flags/Anguilla.svg
  SvgGenImage get anguilla => const SvgGenImage('assets/flags/Anguilla.svg');

  /// File path: assets/flags/Antartica.svg
  SvgGenImage get antartica => const SvgGenImage('assets/flags/Antartica.svg');

  /// File path: assets/flags/Antigua-and-Barbuda.svg
  SvgGenImage get antiguaAndBarbuda =>
      const SvgGenImage('assets/flags/Antigua-and-Barbuda.svg');

  /// File path: assets/flags/Argentina.svg
  SvgGenImage get argentina => const SvgGenImage('assets/flags/Argentina.svg');

  /// File path: assets/flags/Armenia.svg
  SvgGenImage get armenia => const SvgGenImage('assets/flags/Armenia.svg');

  /// File path: assets/flags/Aruba.svg
  SvgGenImage get aruba => const SvgGenImage('assets/flags/Aruba.svg');

  /// File path: assets/flags/Australia.svg
  SvgGenImage get australia => const SvgGenImage('assets/flags/Australia.svg');

  /// File path: assets/flags/Austria.svg
  SvgGenImage get austria => const SvgGenImage('assets/flags/Austria.svg');

  /// File path: assets/flags/Azerbaijan.svg
  SvgGenImage get azerbaijan =>
      const SvgGenImage('assets/flags/Azerbaijan.svg');

  /// File path: assets/flags/Bahamas.svg
  SvgGenImage get bahamas => const SvgGenImage('assets/flags/Bahamas.svg');

  /// File path: assets/flags/Bahrain.svg
  SvgGenImage get bahrain => const SvgGenImage('assets/flags/Bahrain.svg');

  /// File path: assets/flags/Bangladesh.svg
  SvgGenImage get bangladesh =>
      const SvgGenImage('assets/flags/Bangladesh.svg');

  /// File path: assets/flags/Barbados.svg
  SvgGenImage get barbados => const SvgGenImage('assets/flags/Barbados.svg');

  /// File path: assets/flags/Belarus.svg
  SvgGenImage get belarus => const SvgGenImage('assets/flags/Belarus.svg');

  /// File path: assets/flags/Belgium.svg
  SvgGenImage get belgium => const SvgGenImage('assets/flags/Belgium.svg');

  /// File path: assets/flags/Belize.svg
  SvgGenImage get belize => const SvgGenImage('assets/flags/Belize.svg');

  /// File path: assets/flags/Benin.svg
  SvgGenImage get benin => const SvgGenImage('assets/flags/Benin.svg');

  /// File path: assets/flags/Bermuda.svg
  SvgGenImage get bermuda => const SvgGenImage('assets/flags/Bermuda.svg');

  /// File path: assets/flags/Bhutan.svg
  SvgGenImage get bhutan => const SvgGenImage('assets/flags/Bhutan.svg');

  /// File path: assets/flags/Bolivia.svg
  SvgGenImage get bolivia => const SvgGenImage('assets/flags/Bolivia.svg');

  /// File path: assets/flags/Bosnia-&-Herzegovina.svg
  SvgGenImage get bosniaHerzegovina =>
      const SvgGenImage('assets/flags/Bosnia-&-Herzegovina.svg');

  /// File path: assets/flags/Botswana.svg
  SvgGenImage get botswana => const SvgGenImage('assets/flags/Botswana.svg');

  /// File path: assets/flags/Bouvet-Island.svg
  SvgGenImage get bouvetIsland =>
      const SvgGenImage('assets/flags/Bouvet-Island.svg');

  /// File path: assets/flags/Brazil.svg
  SvgGenImage get brazil => const SvgGenImage('assets/flags/Brazil.svg');

  /// File path: assets/flags/British-Indian-Ocean-Territory.svg
  SvgGenImage get britishIndianOceanTerritory =>
      const SvgGenImage('assets/flags/British-Indian-Ocean-Territory.svg');

  /// File path: assets/flags/British-Virgin-Islands.svg
  SvgGenImage get britishVirginIslands =>
      const SvgGenImage('assets/flags/British-Virgin-Islands.svg');

  /// File path: assets/flags/Brunei-Darussalam.svg
  SvgGenImage get bruneiDarussalam =>
      const SvgGenImage('assets/flags/Brunei-Darussalam.svg');

  /// File path: assets/flags/Bulgaria.svg
  SvgGenImage get bulgaria => const SvgGenImage('assets/flags/Bulgaria.svg');

  /// File path: assets/flags/Burkina-Faso.svg
  SvgGenImage get burkinaFaso =>
      const SvgGenImage('assets/flags/Burkina-Faso.svg');

  /// File path: assets/flags/Burundi.svg
  SvgGenImage get burundi => const SvgGenImage('assets/flags/Burundi.svg');

  /// File path: assets/flags/Cambodia.svg
  SvgGenImage get cambodia => const SvgGenImage('assets/flags/Cambodia.svg');

  /// File path: assets/flags/Cameroon.svg
  SvgGenImage get cameroon => const SvgGenImage('assets/flags/Cameroon.svg');

  /// File path: assets/flags/Canada.svg
  SvgGenImage get canada => const SvgGenImage('assets/flags/Canada.svg');

  /// File path: assets/flags/Cape-Verde.svg
  SvgGenImage get capeVerde => const SvgGenImage('assets/flags/Cape-Verde.svg');

  /// File path: assets/flags/Cayman-Islands.svg
  SvgGenImage get caymanIslands =>
      const SvgGenImage('assets/flags/Cayman-Islands.svg');

  /// File path: assets/flags/Central-African-Republic.svg
  SvgGenImage get centralAfricanRepublic =>
      const SvgGenImage('assets/flags/Central-African-Republic.svg');

  /// File path: assets/flags/Chad.svg
  SvgGenImage get chad => const SvgGenImage('assets/flags/Chad.svg');

  /// File path: assets/flags/Chile.svg
  SvgGenImage get chile => const SvgGenImage('assets/flags/Chile.svg');

  /// File path: assets/flags/China.svg
  SvgGenImage get china => const SvgGenImage('assets/flags/China.svg');

  /// File path: assets/flags/Christmas-Island.svg
  SvgGenImage get christmasIsland =>
      const SvgGenImage('assets/flags/Christmas-Island.svg');

  /// File path: assets/flags/Cocos-Islands.svg
  SvgGenImage get cocosIslands =>
      const SvgGenImage('assets/flags/Cocos-Islands.svg');

  /// File path: assets/flags/Colombia.svg
  SvgGenImage get colombia => const SvgGenImage('assets/flags/Colombia.svg');

  /// File path: assets/flags/Comoros.svg
  SvgGenImage get comoros => const SvgGenImage('assets/flags/Comoros.svg');

  /// File path: assets/flags/Congo-Democratic-Republic.svg
  SvgGenImage get congoDemocraticRepublic =>
      const SvgGenImage('assets/flags/Congo-Democratic-Republic.svg');

  /// File path: assets/flags/Congo-Republic.svg
  SvgGenImage get congoRepublic =>
      const SvgGenImage('assets/flags/Congo-Republic.svg');

  /// File path: assets/flags/Cook-Islands.svg
  SvgGenImage get cookIslands =>
      const SvgGenImage('assets/flags/Cook-Islands.svg');

  /// File path: assets/flags/Costa-Rica.svg
  SvgGenImage get costaRica => const SvgGenImage('assets/flags/Costa-Rica.svg');

  /// File path: assets/flags/Croatia.svg
  SvgGenImage get croatia => const SvgGenImage('assets/flags/Croatia.svg');

  /// File path: assets/flags/Cuba.svg
  SvgGenImage get cuba => const SvgGenImage('assets/flags/Cuba.svg');

  /// File path: assets/flags/Cyprus.svg
  SvgGenImage get cyprus => const SvgGenImage('assets/flags/Cyprus.svg');

  /// File path: assets/flags/Czech-Republic.svg
  SvgGenImage get czechRepublic =>
      const SvgGenImage('assets/flags/Czech-Republic.svg');

  /// File path: assets/flags/Denmark.svg
  SvgGenImage get denmark => const SvgGenImage('assets/flags/Denmark.svg');

  /// File path: assets/flags/Djibouti.svg
  SvgGenImage get djibouti => const SvgGenImage('assets/flags/Djibouti.svg');

  /// File path: assets/flags/Dominica.svg
  SvgGenImage get dominica => const SvgGenImage('assets/flags/Dominica.svg');

  /// File path: assets/flags/Dominican-Republic.svg
  SvgGenImage get dominicanRepublic =>
      const SvgGenImage('assets/flags/Dominican-Republic.svg');

  /// File path: assets/flags/Ecuador.svg
  SvgGenImage get ecuador => const SvgGenImage('assets/flags/Ecuador.svg');

  /// File path: assets/flags/Egypt.svg
  SvgGenImage get egypt => const SvgGenImage('assets/flags/Egypt.svg');

  /// File path: assets/flags/El-Salvador.svg
  SvgGenImage get elSalvador =>
      const SvgGenImage('assets/flags/El-Salvador.svg');

  /// File path: assets/flags/England.svg
  SvgGenImage get england => const SvgGenImage('assets/flags/England.svg');

  /// File path: assets/flags/Equatorial-Guinea.svg
  SvgGenImage get equatorialGuinea =>
      const SvgGenImage('assets/flags/Equatorial-Guinea.svg');

  /// File path: assets/flags/Eritrea.svg
  SvgGenImage get eritrea => const SvgGenImage('assets/flags/Eritrea.svg');

  /// File path: assets/flags/Estonia.svg
  SvgGenImage get estonia => const SvgGenImage('assets/flags/Estonia.svg');

  /// File path: assets/flags/Ethiopia.svg
  SvgGenImage get ethiopia => const SvgGenImage('assets/flags/Ethiopia.svg');

  /// File path: assets/flags/European-Union.svg
  SvgGenImage get europeanUnion =>
      const SvgGenImage('assets/flags/European-Union.svg');

  /// File path: assets/flags/Faroe-Islands.svg
  SvgGenImage get faroeIslands =>
      const SvgGenImage('assets/flags/Faroe-Islands.svg');

  /// File path: assets/flags/Fiji.svg
  SvgGenImage get fiji => const SvgGenImage('assets/flags/Fiji.svg');

  /// File path: assets/flags/Finland.svg
  SvgGenImage get finland => const SvgGenImage('assets/flags/Finland.svg');

  /// File path: assets/flags/France.svg
  SvgGenImage get france => const SvgGenImage('assets/flags/France.svg');

  /// File path: assets/flags/French-Guiana.svg
  SvgGenImage get frenchGuiana =>
      const SvgGenImage('assets/flags/French-Guiana.svg');

  /// File path: assets/flags/French-Polynesia.svg
  SvgGenImage get frenchPolynesia =>
      const SvgGenImage('assets/flags/French-Polynesia.svg');

  /// File path: assets/flags/Gabon.svg
  SvgGenImage get gabon => const SvgGenImage('assets/flags/Gabon.svg');

  /// File path: assets/flags/Gambia.svg
  SvgGenImage get gambia => const SvgGenImage('assets/flags/Gambia.svg');

  /// File path: assets/flags/Georgia.svg
  SvgGenImage get georgia => const SvgGenImage('assets/flags/Georgia.svg');

  /// File path: assets/flags/Germany.svg
  SvgGenImage get germany => const SvgGenImage('assets/flags/Germany.svg');

  /// File path: assets/flags/Ghana.svg
  SvgGenImage get ghana => const SvgGenImage('assets/flags/Ghana.svg');

  /// File path: assets/flags/Gibraltar.svg
  SvgGenImage get gibraltar => const SvgGenImage('assets/flags/Gibraltar.svg');

  /// File path: assets/flags/Greece.svg
  SvgGenImage get greece => const SvgGenImage('assets/flags/Greece.svg');

  /// File path: assets/flags/Greenland.svg
  SvgGenImage get greenland => const SvgGenImage('assets/flags/Greenland.svg');

  /// File path: assets/flags/Grenada.svg
  SvgGenImage get grenada => const SvgGenImage('assets/flags/Grenada.svg');

  /// File path: assets/flags/Guadeloupe.svg
  SvgGenImage get guadeloupe =>
      const SvgGenImage('assets/flags/Guadeloupe.svg');

  /// File path: assets/flags/Guam.svg
  SvgGenImage get guam => const SvgGenImage('assets/flags/Guam.svg');

  /// File path: assets/flags/Guatemala.svg
  SvgGenImage get guatemala => const SvgGenImage('assets/flags/Guatemala.svg');

  /// File path: assets/flags/Guernsey.svg
  SvgGenImage get guernsey => const SvgGenImage('assets/flags/Guernsey.svg');

  /// File path: assets/flags/Guinea-Bissau.svg
  SvgGenImage get guineaBissau =>
      const SvgGenImage('assets/flags/Guinea-Bissau.svg');

  /// File path: assets/flags/Guinea.svg
  SvgGenImage get guinea => const SvgGenImage('assets/flags/Guinea.svg');

  /// File path: assets/flags/Guyana.svg
  SvgGenImage get guyana => const SvgGenImage('assets/flags/Guyana.svg');

  /// File path: assets/flags/Haiti.svg
  SvgGenImage get haiti => const SvgGenImage('assets/flags/Haiti.svg');

  /// File path: assets/flags/Heard-Island-and-McDonald-Islands.svg
  SvgGenImage get heardIslandAndMcDonaldIslands =>
      const SvgGenImage('assets/flags/Heard-Island-and-McDonald-Islands.svg');

  /// File path: assets/flags/Honduras.svg
  SvgGenImage get honduras => const SvgGenImage('assets/flags/Honduras.svg');

  /// File path: assets/flags/Hong-Kong.svg
  SvgGenImage get hongKong => const SvgGenImage('assets/flags/Hong-Kong.svg');

  /// File path: assets/flags/Hungary.svg
  SvgGenImage get hungary => const SvgGenImage('assets/flags/Hungary.svg');

  /// File path: assets/flags/Iceland.svg
  SvgGenImage get iceland => const SvgGenImage('assets/flags/Iceland.svg');

  /// File path: assets/flags/India.svg
  SvgGenImage get india => const SvgGenImage('assets/flags/India.svg');

  /// File path: assets/flags/Indonesia.svg
  SvgGenImage get indonesia => const SvgGenImage('assets/flags/Indonesia.svg');

  /// File path: assets/flags/Iran.svg
  SvgGenImage get iran => const SvgGenImage('assets/flags/Iran.svg');

  /// File path: assets/flags/Iraq.svg
  SvgGenImage get iraq => const SvgGenImage('assets/flags/Iraq.svg');

  /// File path: assets/flags/Ireland.svg
  SvgGenImage get ireland => const SvgGenImage('assets/flags/Ireland.svg');

  /// File path: assets/flags/Isle-of-Man.svg
  SvgGenImage get isleOfMan =>
      const SvgGenImage('assets/flags/Isle-of-Man.svg');

  /// File path: assets/flags/Israel.svg
  SvgGenImage get israel => const SvgGenImage('assets/flags/Israel.svg');

  /// File path: assets/flags/Italy.svg
  SvgGenImage get italy => const SvgGenImage('assets/flags/Italy.svg');

  /// File path: assets/flags/IvoryCoast.svg
  SvgGenImage get ivoryCoast =>
      const SvgGenImage('assets/flags/IvoryCoast.svg');

  /// File path: assets/flags/Jamaica.svg
  SvgGenImage get jamaica => const SvgGenImage('assets/flags/Jamaica.svg');

  /// File path: assets/flags/Japan.svg
  SvgGenImage get japan => const SvgGenImage('assets/flags/Japan.svg');

  /// File path: assets/flags/Jersey.svg
  SvgGenImage get jersey => const SvgGenImage('assets/flags/Jersey.svg');

  /// File path: assets/flags/Jordan.svg
  SvgGenImage get jordan => const SvgGenImage('assets/flags/Jordan.svg');

  /// File path: assets/flags/Kazakhstan.svg
  SvgGenImage get kazakhstan =>
      const SvgGenImage('assets/flags/Kazakhstan.svg');

  /// File path: assets/flags/Kenya.svg
  SvgGenImage get kenya => const SvgGenImage('assets/flags/Kenya.svg');

  /// File path: assets/flags/Kiribati.svg
  SvgGenImage get kiribati => const SvgGenImage('assets/flags/Kiribati.svg');

  /// File path: assets/flags/Kosovo.svg
  SvgGenImage get kosovo => const SvgGenImage('assets/flags/Kosovo.svg');

  /// File path: assets/flags/Kuwait.svg
  SvgGenImage get kuwait => const SvgGenImage('assets/flags/Kuwait.svg');

  /// File path: assets/flags/Kyrgyzstan.svg
  SvgGenImage get kyrgyzstan =>
      const SvgGenImage('assets/flags/Kyrgyzstan.svg');

  /// File path: assets/flags/Laos.svg
  SvgGenImage get laos => const SvgGenImage('assets/flags/Laos.svg');

  /// File path: assets/flags/Latvia.svg
  SvgGenImage get latvia => const SvgGenImage('assets/flags/Latvia.svg');

  /// File path: assets/flags/Lebanon.svg
  SvgGenImage get lebanon => const SvgGenImage('assets/flags/Lebanon.svg');

  /// File path: assets/flags/Lesotho.svg
  SvgGenImage get lesotho => const SvgGenImage('assets/flags/Lesotho.svg');

  /// File path: assets/flags/Liberia.svg
  SvgGenImage get liberia => const SvgGenImage('assets/flags/Liberia.svg');

  /// File path: assets/flags/Libya.svg
  SvgGenImage get libya => const SvgGenImage('assets/flags/Libya.svg');

  /// File path: assets/flags/Liechtenstein.svg
  SvgGenImage get liechtenstein =>
      const SvgGenImage('assets/flags/Liechtenstein.svg');

  /// File path: assets/flags/Lithuania.svg
  SvgGenImage get lithuania => const SvgGenImage('assets/flags/Lithuania.svg');

  /// File path: assets/flags/Luxembourg.svg
  SvgGenImage get luxembourg =>
      const SvgGenImage('assets/flags/Luxembourg.svg');

  /// File path: assets/flags/Macau.svg
  SvgGenImage get macau => const SvgGenImage('assets/flags/Macau.svg');

  /// File path: assets/flags/Macedonia.svg
  SvgGenImage get macedonia => const SvgGenImage('assets/flags/Macedonia.svg');

  /// File path: assets/flags/Madagascar.svg
  SvgGenImage get madagascar =>
      const SvgGenImage('assets/flags/Madagascar.svg');

  /// File path: assets/flags/Malawi.svg
  SvgGenImage get malawi => const SvgGenImage('assets/flags/Malawi.svg');

  /// File path: assets/flags/Malaysia.svg
  SvgGenImage get malaysia => const SvgGenImage('assets/flags/Malaysia.svg');

  /// File path: assets/flags/Maldives.svg
  SvgGenImage get maldives => const SvgGenImage('assets/flags/Maldives.svg');

  /// File path: assets/flags/Mali.svg
  SvgGenImage get mali => const SvgGenImage('assets/flags/Mali.svg');

  /// File path: assets/flags/Malta.svg
  SvgGenImage get malta => const SvgGenImage('assets/flags/Malta.svg');

  /// File path: assets/flags/Marshall-Islands.svg
  SvgGenImage get marshallIslands =>
      const SvgGenImage('assets/flags/Marshall-Islands.svg');

  /// File path: assets/flags/Martinique.svg
  SvgGenImage get martinique =>
      const SvgGenImage('assets/flags/Martinique.svg');

  /// File path: assets/flags/Mauritania.svg
  SvgGenImage get mauritania =>
      const SvgGenImage('assets/flags/Mauritania.svg');

  /// File path: assets/flags/Mauritius.svg
  SvgGenImage get mauritius => const SvgGenImage('assets/flags/Mauritius.svg');

  /// File path: assets/flags/Mayotte.svg
  SvgGenImage get mayotte => const SvgGenImage('assets/flags/Mayotte.svg');

  /// File path: assets/flags/Mexico.svg
  SvgGenImage get mexico => const SvgGenImage('assets/flags/Mexico.svg');

  /// File path: assets/flags/Micronesia.svg
  SvgGenImage get micronesia =>
      const SvgGenImage('assets/flags/Micronesia.svg');

  /// File path: assets/flags/Moldova.svg
  SvgGenImage get moldova => const SvgGenImage('assets/flags/Moldova.svg');

  /// File path: assets/flags/Monaco.svg
  SvgGenImage get monaco => const SvgGenImage('assets/flags/Monaco.svg');

  /// File path: assets/flags/Mongolia.svg
  SvgGenImage get mongolia => const SvgGenImage('assets/flags/Mongolia.svg');

  /// File path: assets/flags/Montenegro.svg
  SvgGenImage get montenegro =>
      const SvgGenImage('assets/flags/Montenegro.svg');

  /// File path: assets/flags/Montserrat.svg
  SvgGenImage get montserrat =>
      const SvgGenImage('assets/flags/Montserrat.svg');

  /// File path: assets/flags/Morocco.svg
  SvgGenImage get morocco => const SvgGenImage('assets/flags/Morocco.svg');

  /// File path: assets/flags/Mozambique.svg
  SvgGenImage get mozambique =>
      const SvgGenImage('assets/flags/Mozambique.svg');

  /// File path: assets/flags/Myanmar.svg
  SvgGenImage get myanmar => const SvgGenImage('assets/flags/Myanmar.svg');

  /// File path: assets/flags/Namibia.svg
  SvgGenImage get namibia => const SvgGenImage('assets/flags/Namibia.svg');

  /// File path: assets/flags/Nauru.svg
  SvgGenImage get nauru => const SvgGenImage('assets/flags/Nauru.svg');

  /// File path: assets/flags/Nepal.svg
  SvgGenImage get nepal => const SvgGenImage('assets/flags/Nepal.svg');

  /// File path: assets/flags/Netherlands-Antilles.svg
  SvgGenImage get netherlandsAntilles =>
      const SvgGenImage('assets/flags/Netherlands-Antilles.svg');

  /// File path: assets/flags/Netherlands.svg
  SvgGenImage get netherlands =>
      const SvgGenImage('assets/flags/Netherlands.svg');

  /// File path: assets/flags/New-Caledonia.svg
  SvgGenImage get newCaledonia =>
      const SvgGenImage('assets/flags/New-Caledonia.svg');

  /// File path: assets/flags/New-Zealand.svg
  SvgGenImage get newZealand =>
      const SvgGenImage('assets/flags/New-Zealand.svg');

  /// File path: assets/flags/Nicaragua.svg
  SvgGenImage get nicaragua => const SvgGenImage('assets/flags/Nicaragua.svg');

  /// File path: assets/flags/Niger.svg
  SvgGenImage get niger => const SvgGenImage('assets/flags/Niger.svg');

  /// File path: assets/flags/Nigeria.svg
  SvgGenImage get nigeria => const SvgGenImage('assets/flags/Nigeria.svg');

  /// File path: assets/flags/Niue.svg
  SvgGenImage get niue => const SvgGenImage('assets/flags/Niue.svg');

  /// File path: assets/flags/Norfolk-Island.svg
  SvgGenImage get norfolkIsland =>
      const SvgGenImage('assets/flags/Norfolk-Island.svg');

  /// File path: assets/flags/North-Korea.svg
  SvgGenImage get northKorea =>
      const SvgGenImage('assets/flags/North-Korea.svg');

  /// File path: assets/flags/Northern-Ireland.svg
  SvgGenImage get northernIreland =>
      const SvgGenImage('assets/flags/Northern-Ireland.svg');

  /// File path: assets/flags/Northern-Mariana-Islands.svg
  SvgGenImage get northernMarianaIslands =>
      const SvgGenImage('assets/flags/Northern-Mariana-Islands.svg');

  /// File path: assets/flags/Norway.svg
  SvgGenImage get norway => const SvgGenImage('assets/flags/Norway.svg');

  /// File path: assets/flags/Oman.svg
  SvgGenImage get oman => const SvgGenImage('assets/flags/Oman.svg');

  /// File path: assets/flags/Pakistan.svg
  SvgGenImage get pakistan => const SvgGenImage('assets/flags/Pakistan.svg');

  /// File path: assets/flags/Palau.svg
  SvgGenImage get palau => const SvgGenImage('assets/flags/Palau.svg');

  /// File path: assets/flags/Palestine.svg
  SvgGenImage get palestine => const SvgGenImage('assets/flags/Palestine.svg');

  /// File path: assets/flags/Panama.svg
  SvgGenImage get panama => const SvgGenImage('assets/flags/Panama.svg');

  /// File path: assets/flags/Papua-New-Guinea.svg
  SvgGenImage get papuaNewGuinea =>
      const SvgGenImage('assets/flags/Papua-New-Guinea.svg');

  /// File path: assets/flags/Paraguay.svg
  SvgGenImage get paraguay => const SvgGenImage('assets/flags/Paraguay.svg');

  /// File path: assets/flags/Peru.svg
  SvgGenImage get peru => const SvgGenImage('assets/flags/Peru.svg');

  /// File path: assets/flags/Philippines.svg
  SvgGenImage get philippines =>
      const SvgGenImage('assets/flags/Philippines.svg');

  /// File path: assets/flags/Pitcairn.svg
  SvgGenImage get pitcairn => const SvgGenImage('assets/flags/Pitcairn.svg');

  /// File path: assets/flags/Placeholder.svg
  SvgGenImage get placeholder =>
      const SvgGenImage('assets/flags/Placeholder.svg');

  /// File path: assets/flags/Poland.svg
  SvgGenImage get poland => const SvgGenImage('assets/flags/Poland.svg');

  /// File path: assets/flags/Portugal.svg
  SvgGenImage get portugal => const SvgGenImage('assets/flags/Portugal.svg');

  /// File path: assets/flags/Puerto-Rico.svg
  SvgGenImage get puertoRico =>
      const SvgGenImage('assets/flags/Puerto-Rico.svg');

  /// File path: assets/flags/Qatar.svg
  SvgGenImage get qatar => const SvgGenImage('assets/flags/Qatar.svg');

  /// File path: assets/flags/Romania.svg
  SvgGenImage get romania => const SvgGenImage('assets/flags/Romania.svg');

  /// File path: assets/flags/Russia.svg
  SvgGenImage get russia => const SvgGenImage('assets/flags/Russia.svg');

  /// File path: assets/flags/Rwanda.svg
  SvgGenImage get rwanda => const SvgGenImage('assets/flags/Rwanda.svg');

  /// File path: assets/flags/Réunion.svg
  SvgGenImage get rUnion => const SvgGenImage('assets/flags/Réunion.svg');

  /// File path: assets/flags/Saint-Helena.svg
  SvgGenImage get saintHelena =>
      const SvgGenImage('assets/flags/Saint-Helena.svg');

  /// File path: assets/flags/Saint-Kitts-and-Nevis.svg
  SvgGenImage get saintKittsAndNevis =>
      const SvgGenImage('assets/flags/Saint-Kitts-and-Nevis.svg');

  /// File path: assets/flags/Saint-Lucia.svg
  SvgGenImage get saintLucia =>
      const SvgGenImage('assets/flags/Saint-Lucia.svg');

  /// File path: assets/flags/Saint-Pierre-and-Miquelon.svg
  SvgGenImage get saintPierreAndMiquelon =>
      const SvgGenImage('assets/flags/Saint-Pierre-and-Miquelon.svg');

  /// File path: assets/flags/Saint-Vincent-and-the-Grenadines.svg
  SvgGenImage get saintVincentAndTheGrenadines =>
      const SvgGenImage('assets/flags/Saint-Vincent-and-the-Grenadines.svg');

  /// File path: assets/flags/Samoa.svg
  SvgGenImage get samoa => const SvgGenImage('assets/flags/Samoa.svg');

  /// File path: assets/flags/San-Marino.svg
  SvgGenImage get sanMarino => const SvgGenImage('assets/flags/San-Marino.svg');

  /// File path: assets/flags/Sao-Tome-and-Principe.svg
  SvgGenImage get saoTomeAndPrincipe =>
      const SvgGenImage('assets/flags/Sao-Tome-and-Principe.svg');

  /// File path: assets/flags/Saudi-Arabia.svg
  SvgGenImage get saudiArabia =>
      const SvgGenImage('assets/flags/Saudi-Arabia.svg');

  /// File path: assets/flags/Scotland.svg
  SvgGenImage get scotland => const SvgGenImage('assets/flags/Scotland.svg');

  /// File path: assets/flags/Senegal.svg
  SvgGenImage get senegal => const SvgGenImage('assets/flags/Senegal.svg');

  /// File path: assets/flags/Serbia.svg
  SvgGenImage get serbia => const SvgGenImage('assets/flags/Serbia.svg');

  /// File path: assets/flags/Seychelles.svg
  SvgGenImage get seychelles =>
      const SvgGenImage('assets/flags/Seychelles.svg');

  /// File path: assets/flags/Sierra-Leone.svg
  SvgGenImage get sierraLeone =>
      const SvgGenImage('assets/flags/Sierra-Leone.svg');

  /// File path: assets/flags/Singapore.svg
  SvgGenImage get singapore => const SvgGenImage('assets/flags/Singapore.svg');

  /// File path: assets/flags/Slovakia.svg
  SvgGenImage get slovakia => const SvgGenImage('assets/flags/Slovakia.svg');

  /// File path: assets/flags/Slovenia.svg
  SvgGenImage get slovenia => const SvgGenImage('assets/flags/Slovenia.svg');

  /// File path: assets/flags/Solomon-Islands.svg
  SvgGenImage get solomonIslands =>
      const SvgGenImage('assets/flags/Solomon-Islands.svg');

  /// File path: assets/flags/Somalia.svg
  SvgGenImage get somalia => const SvgGenImage('assets/flags/Somalia.svg');

  /// File path: assets/flags/South-Africa.svg
  SvgGenImage get southAfrica =>
      const SvgGenImage('assets/flags/South-Africa.svg');

  /// File path: assets/flags/South-Korea.svg
  SvgGenImage get southKorea =>
      const SvgGenImage('assets/flags/South-Korea.svg');

  /// File path: assets/flags/South-Sudan.svg
  SvgGenImage get southSudan =>
      const SvgGenImage('assets/flags/South-Sudan.svg');

  /// File path: assets/flags/Spain.svg
  SvgGenImage get spain => const SvgGenImage('assets/flags/Spain.svg');

  /// File path: assets/flags/Sri-Lanka.svg
  SvgGenImage get sriLanka => const SvgGenImage('assets/flags/Sri-Lanka.svg');

  /// File path: assets/flags/Sudan.svg
  SvgGenImage get sudan => const SvgGenImage('assets/flags/Sudan.svg');

  /// File path: assets/flags/Suriname.svg
  SvgGenImage get suriname => const SvgGenImage('assets/flags/Suriname.svg');

  /// File path: assets/flags/Svalbard-and-Jan-Mayen.svg
  SvgGenImage get svalbardAndJanMayen =>
      const SvgGenImage('assets/flags/Svalbard-and-Jan-Mayen.svg');

  /// File path: assets/flags/Swaziland.svg
  SvgGenImage get swaziland => const SvgGenImage('assets/flags/Swaziland.svg');

  /// File path: assets/flags/Sweden.svg
  SvgGenImage get sweden => const SvgGenImage('assets/flags/Sweden.svg');

  /// File path: assets/flags/Switzerland.svg
  SvgGenImage get switzerland =>
      const SvgGenImage('assets/flags/Switzerland.svg');

  /// File path: assets/flags/Syria.svg
  SvgGenImage get syria => const SvgGenImage('assets/flags/Syria.svg');

  /// File path: assets/flags/Taiwan.svg
  SvgGenImage get taiwan => const SvgGenImage('assets/flags/Taiwan.svg');

  /// File path: assets/flags/Tajikistan.svg
  SvgGenImage get tajikistan =>
      const SvgGenImage('assets/flags/Tajikistan.svg');

  /// File path: assets/flags/Tanzania.svg
  SvgGenImage get tanzania => const SvgGenImage('assets/flags/Tanzania.svg');

  /// File path: assets/flags/Thailand.svg
  SvgGenImage get thailand => const SvgGenImage('assets/flags/Thailand.svg');

  /// File path: assets/flags/Tibet.svg
  SvgGenImage get tibet => const SvgGenImage('assets/flags/Tibet.svg');

  /// File path: assets/flags/Timor-Leste.svg
  SvgGenImage get timorLeste =>
      const SvgGenImage('assets/flags/Timor-Leste.svg');

  /// File path: assets/flags/Togo.svg
  SvgGenImage get togo => const SvgGenImage('assets/flags/Togo.svg');

  /// File path: assets/flags/Tonga.svg
  SvgGenImage get tonga => const SvgGenImage('assets/flags/Tonga.svg');

  /// File path: assets/flags/Trinidad-and-Tobago.svg
  SvgGenImage get trinidadAndTobago =>
      const SvgGenImage('assets/flags/Trinidad-and-Tobago.svg');

  /// File path: assets/flags/Tunisia.svg
  SvgGenImage get tunisia => const SvgGenImage('assets/flags/Tunisia.svg');

  /// File path: assets/flags/Turkey.svg
  SvgGenImage get turkey => const SvgGenImage('assets/flags/Turkey.svg');

  /// File path: assets/flags/Turkmenistan.svg
  SvgGenImage get turkmenistan =>
      const SvgGenImage('assets/flags/Turkmenistan.svg');

  /// File path: assets/flags/Tuvalu.svg
  SvgGenImage get tuvalu => const SvgGenImage('assets/flags/Tuvalu.svg');

  /// File path: assets/flags/U.S.-Virgin-Islands.svg
  SvgGenImage get uSVirginIslands =>
      const SvgGenImage('assets/flags/U.S.-Virgin-Islands.svg');

  /// File path: assets/flags/Uganda.svg
  SvgGenImage get uganda => const SvgGenImage('assets/flags/Uganda.svg');

  /// File path: assets/flags/Ukraine.svg
  SvgGenImage get ukraine => const SvgGenImage('assets/flags/Ukraine.svg');

  /// File path: assets/flags/United-Arab-Emirates.svg
  SvgGenImage get unitedArabEmirates =>
      const SvgGenImage('assets/flags/United-Arab-Emirates.svg');

  /// File path: assets/flags/United-Kingdom.svg
  SvgGenImage get unitedKingdom =>
      const SvgGenImage('assets/flags/United-Kingdom.svg');

  /// File path: assets/flags/United-States-Minor-Outlying-Islands.svg
  SvgGenImage get unitedStatesMinorOutlyingIslands => const SvgGenImage(
    'assets/flags/United-States-Minor-Outlying-Islands.svg',
  );

  /// File path: assets/flags/United-States.svg
  SvgGenImage get unitedStates =>
      const SvgGenImage('assets/flags/United-States.svg');

  /// File path: assets/flags/Uruguay.svg
  SvgGenImage get uruguay => const SvgGenImage('assets/flags/Uruguay.svg');

  /// File path: assets/flags/Uzbekistan.svg
  SvgGenImage get uzbekistan =>
      const SvgGenImage('assets/flags/Uzbekistan.svg');

  /// File path: assets/flags/Vanuatu.svg
  SvgGenImage get vanuatu => const SvgGenImage('assets/flags/Vanuatu.svg');

  /// File path: assets/flags/Vatican-City-State.svg
  SvgGenImage get vaticanCityState =>
      const SvgGenImage('assets/flags/Vatican-City-State.svg');

  /// File path: assets/flags/Venezuela.svg
  SvgGenImage get venezuela => const SvgGenImage('assets/flags/Venezuela.svg');

  /// File path: assets/flags/Vietnam.svg
  SvgGenImage get vietnam => const SvgGenImage('assets/flags/Vietnam.svg');

  /// File path: assets/flags/Wales.svg
  SvgGenImage get wales => const SvgGenImage('assets/flags/Wales.svg');

  /// File path: assets/flags/Western-Sahara.svg
  SvgGenImage get westernSahara =>
      const SvgGenImage('assets/flags/Western-Sahara.svg');

  /// File path: assets/flags/Yemen.svg
  SvgGenImage get yemen => const SvgGenImage('assets/flags/Yemen.svg');

  /// File path: assets/flags/Zambia.svg
  SvgGenImage get zambia => const SvgGenImage('assets/flags/Zambia.svg');

  /// File path: assets/flags/Zimbabwe.svg
  SvgGenImage get zimbabwe => const SvgGenImage('assets/flags/Zimbabwe.svg');

  /// File path: assets/flags/flag-placeholder.svg
  SvgGenImage get flagPlaceholder =>
      const SvgGenImage('assets/flags/flag-placeholder.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    afghanistan,
    alandIslands,
    albania,
    algeria,
    americanSamoa,
    andorra,
    angola,
    anguilla,
    antartica,
    antiguaAndBarbuda,
    argentina,
    armenia,
    aruba,
    australia,
    austria,
    azerbaijan,
    bahamas,
    bahrain,
    bangladesh,
    barbados,
    belarus,
    belgium,
    belize,
    benin,
    bermuda,
    bhutan,
    bolivia,
    bosniaHerzegovina,
    botswana,
    bouvetIsland,
    brazil,
    britishIndianOceanTerritory,
    britishVirginIslands,
    bruneiDarussalam,
    bulgaria,
    burkinaFaso,
    burundi,
    cambodia,
    cameroon,
    canada,
    capeVerde,
    caymanIslands,
    centralAfricanRepublic,
    chad,
    chile,
    china,
    christmasIsland,
    cocosIslands,
    colombia,
    comoros,
    congoDemocraticRepublic,
    congoRepublic,
    cookIslands,
    costaRica,
    croatia,
    cuba,
    cyprus,
    czechRepublic,
    denmark,
    djibouti,
    dominica,
    dominicanRepublic,
    ecuador,
    egypt,
    elSalvador,
    england,
    equatorialGuinea,
    eritrea,
    estonia,
    ethiopia,
    europeanUnion,
    faroeIslands,
    fiji,
    finland,
    france,
    frenchGuiana,
    frenchPolynesia,
    gabon,
    gambia,
    georgia,
    germany,
    ghana,
    gibraltar,
    greece,
    greenland,
    grenada,
    guadeloupe,
    guam,
    guatemala,
    guernsey,
    guineaBissau,
    guinea,
    guyana,
    haiti,
    heardIslandAndMcDonaldIslands,
    honduras,
    hongKong,
    hungary,
    iceland,
    india,
    indonesia,
    iran,
    iraq,
    ireland,
    isleOfMan,
    israel,
    italy,
    ivoryCoast,
    jamaica,
    japan,
    jersey,
    jordan,
    kazakhstan,
    kenya,
    kiribati,
    kosovo,
    kuwait,
    kyrgyzstan,
    laos,
    latvia,
    lebanon,
    lesotho,
    liberia,
    libya,
    liechtenstein,
    lithuania,
    luxembourg,
    macau,
    macedonia,
    madagascar,
    malawi,
    malaysia,
    maldives,
    mali,
    malta,
    marshallIslands,
    martinique,
    mauritania,
    mauritius,
    mayotte,
    mexico,
    micronesia,
    moldova,
    monaco,
    mongolia,
    montenegro,
    montserrat,
    morocco,
    mozambique,
    myanmar,
    namibia,
    nauru,
    nepal,
    netherlandsAntilles,
    netherlands,
    newCaledonia,
    newZealand,
    nicaragua,
    niger,
    nigeria,
    niue,
    norfolkIsland,
    northKorea,
    northernIreland,
    northernMarianaIslands,
    norway,
    oman,
    pakistan,
    palau,
    palestine,
    panama,
    papuaNewGuinea,
    paraguay,
    peru,
    philippines,
    pitcairn,
    placeholder,
    poland,
    portugal,
    puertoRico,
    qatar,
    romania,
    russia,
    rwanda,
    rUnion,
    saintHelena,
    saintKittsAndNevis,
    saintLucia,
    saintPierreAndMiquelon,
    saintVincentAndTheGrenadines,
    samoa,
    sanMarino,
    saoTomeAndPrincipe,
    saudiArabia,
    scotland,
    senegal,
    serbia,
    seychelles,
    sierraLeone,
    singapore,
    slovakia,
    slovenia,
    solomonIslands,
    somalia,
    southAfrica,
    southKorea,
    southSudan,
    spain,
    sriLanka,
    sudan,
    suriname,
    svalbardAndJanMayen,
    swaziland,
    sweden,
    switzerland,
    syria,
    taiwan,
    tajikistan,
    tanzania,
    thailand,
    tibet,
    timorLeste,
    togo,
    tonga,
    trinidadAndTobago,
    tunisia,
    turkey,
    turkmenistan,
    tuvalu,
    uSVirginIslands,
    uganda,
    ukraine,
    unitedArabEmirates,
    unitedKingdom,
    unitedStatesMinorOutlyingIslands,
    unitedStates,
    uruguay,
    uzbekistan,
    vanuatu,
    vaticanCityState,
    venezuela,
    vietnam,
    wales,
    westernSahara,
    yemen,
    zambia,
    zimbabwe,
    flagPlaceholder,
  ];
}

class $AssetsGifsGen {
  const $AssetsGifsGen();

  /// File path: assets/gifs/loading.gif
  AssetGenImage get loading => const AssetGenImage('assets/gifs/loading.gif');

  /// List of all assets
  List<AssetGenImage> get values => [loading];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/a_pay_dark_disabled.svg
  SvgGenImage get aPayDarkDisabled =>
      const SvgGenImage.vec('assets/images/a_pay_dark_disabled.svg');

  /// File path: assets/images/a_pay_dark_enabled.svg
  SvgGenImage get aPayDarkEnabled =>
      const SvgGenImage.vec('assets/images/a_pay_dark_enabled.svg');

  /// File path: assets/images/a_pay_light_disabled.svg
  SvgGenImage get aPayLightDisabled =>
      const SvgGenImage.vec('assets/images/a_pay_light_disabled.svg');

  /// File path: assets/images/a_pay_light_enabled.svg
  SvgGenImage get aPayLightEnabled =>
      const SvgGenImage.vec('assets/images/a_pay_light_enabled.svg');

  /// File path: assets/images/accounts.svg
  SvgGenImage get accounts =>
      const SvgGenImage.vec('assets/images/accounts.svg');

  /// File path: assets/images/active_program.svg
  SvgGenImage get activeProgram =>
      const SvgGenImage.vec('assets/images/active_program.svg');

  /// File path: assets/images/app_bug.svg
  SvgGenImage get appBug => const SvgGenImage.vec('assets/images/app_bug.svg');

  /// File path: assets/images/ar_lan_ic.svg
  SvgGenImage get arLanIc =>
      const SvgGenImage.vec('assets/images/ar_lan_ic.svg');

  /// File path: assets/images/arrow-left.svg
  SvgGenImage get arrowLeft =>
      const SvgGenImage.vec('assets/images/arrow-left.svg');

  /// File path: assets/images/arrow-right.svg
  SvgGenImage get arrowRight =>
      const SvgGenImage.vec('assets/images/arrow-right.svg');

  /// File path: assets/images/arrowUpRight.svg
  SvgGenImage get arrowUpRight =>
      const SvgGenImage.vec('assets/images/arrowUpRight.svg');

  /// File path: assets/images/auth_background_header.svg
  SvgGenImage get authBackgroundHeader =>
      const SvgGenImage.vec('assets/images/auth_background_header.svg');

  /// File path: assets/images/auth_background_header_dark.svg
  SvgGenImage get authBackgroundHeaderDark =>
      const SvgGenImage.vec('assets/images/auth_background_header_dark.svg');

  /// File path: assets/images/banner_gold.svg
  SvgGenImage get bannerGold =>
      const SvgGenImage.vec('assets/images/banner_gold.svg');

  /// File path: assets/images/calendar.svg
  SvgGenImage get calendar =>
      const SvgGenImage.vec('assets/images/calendar.svg');

  /// File path: assets/images/check_circle.svg
  SvgGenImage get checkCircle =>
      const SvgGenImage.vec('assets/images/check_circle.svg');

  /// File path: assets/images/checkbox_base.svg
  SvgGenImage get checkboxBase =>
      const SvgGenImage.vec('assets/images/checkbox_base.svg');

  /// File path: assets/images/chevron-right.svg
  SvgGenImage get chevronRight =>
      const SvgGenImage.vec('assets/images/chevron-right.svg');

  /// File path: assets/images/chevron_down.svg
  SvgGenImage get chevronDown =>
      const SvgGenImage.vec('assets/images/chevron_down.svg');

  /// File path: assets/images/chevron_left.svg
  SvgGenImage get chevronLeft =>
      const SvgGenImage.vec('assets/images/chevron_left.svg');

  /// File path: assets/images/chevron_selector_vertical.svg
  SvgGenImage get chevronSelectorVertical =>
      const SvgGenImage.vec('assets/images/chevron_selector_vertical.svg');

  /// File path: assets/images/clock-fast-forward.svg
  SvgGenImage get clockFastForward =>
      const SvgGenImage.vec('assets/images/clock-fast-forward.svg');

  /// File path: assets/images/close_ic.svg
  SvgGenImage get closeIc =>
      const SvgGenImage.vec('assets/images/close_ic.svg');

  /// File path: assets/images/copy.svg
  SvgGenImage get copy => const SvgGenImage.vec('assets/images/copy.svg');

  /// File path: assets/images/credit_card_download.svg
  SvgGenImage get creditCardDownload =>
      const SvgGenImage.vec('assets/images/credit_card_download.svg');

  /// File path: assets/images/credit_card_refresh.svg
  SvgGenImage get creditCardRefresh =>
      const SvgGenImage.vec('assets/images/credit_card_refresh.svg');

  /// File path: assets/images/credit_card_upload.svg
  SvgGenImage get creditCardUpload =>
      const SvgGenImage.vec('assets/images/credit_card_upload.svg');

  /// File path: assets/images/deposit.svg
  SvgGenImage get deposit => const SvgGenImage.vec('assets/images/deposit.svg');

  /// File path: assets/images/dot_grid.svg
  SvgGenImage get dotGrid =>
      const SvgGenImage.vec('assets/images/dot_grid.svg');

  /// File path: assets/images/dots-vertical.svg
  SvgGenImage get dotsVertical =>
      const SvgGenImage.vec('assets/images/dots-vertical.svg');

  /// File path: assets/images/drop_down_ic.svg
  SvgGenImage get dropDownIc =>
      const SvgGenImage.vec('assets/images/drop_down_ic.svg');

  /// File path: assets/images/duplo_alert_error.svg
  SvgGenImage get duploAlertError =>
      const SvgGenImage.vec('assets/images/duplo_alert_error.svg');

  /// File path: assets/images/duplo_alert_info.svg
  SvgGenImage get duploAlertInfo =>
      const SvgGenImage.vec('assets/images/duplo_alert_info.svg');

  /// File path: assets/images/duplo_alert_warning.svg
  SvgGenImage get duploAlertWarning =>
      const SvgGenImage.vec('assets/images/duplo_alert_warning.svg');

  /// File path: assets/images/empty_radio_base.svg
  SvgGenImage get emptyRadioBase =>
      const SvgGenImage.vec('assets/images/empty_radio_base.svg');

  /// File path: assets/images/en_lan_ic.svg
  SvgGenImage get enLanIc =>
      const SvgGenImage.vec('assets/images/en_lan_ic.svg');

  /// File path: assets/images/error_ic.svg
  SvgGenImage get errorIc =>
      const SvgGenImage.vec('assets/images/error_ic.svg');

  /// File path: assets/images/error_icon.svg
  SvgGenImage get errorIcon =>
      const SvgGenImage.vec('assets/images/error_icon.svg');

  /// File path: assets/images/eye_close.svg
  SvgGenImage get eyeClose =>
      const SvgGenImage.vec('assets/images/eye_close.svg');

  /// File path: assets/images/eye_open.svg
  SvgGenImage get eyeOpen =>
      const SvgGenImage.vec('assets/images/eye_open.svg');

  /// File path: assets/images/file_type.svg
  SvgGenImage get fileType =>
      const SvgGenImage.vec('assets/images/file_type.svg');

  /// File path: assets/images/g_par_dark_enabled.svg
  SvgGenImage get gParDarkEnabled =>
      const SvgGenImage.vec('assets/images/g_par_dark_enabled.svg');

  /// File path: assets/images/g_pay_dark_disabled.svg
  SvgGenImage get gPayDarkDisabled =>
      const SvgGenImage.vec('assets/images/g_pay_dark_disabled.svg');

  /// File path: assets/images/g_pay_light_disabled.svg
  SvgGenImage get gPayLightDisabled =>
      const SvgGenImage.vec('assets/images/g_pay_light_disabled.svg');

  /// File path: assets/images/g_pay_light_enabled.svg
  SvgGenImage get gPayLightEnabled =>
      const SvgGenImage.vec('assets/images/g_pay_light_enabled.svg');

  /// File path: assets/images/gold.svg
  SvgGenImage get gold => const SvgGenImage.vec('assets/images/gold.svg');

  /// File path: assets/images/google_logo.svg
  SvgGenImage get googleLogo =>
      const SvgGenImage.vec('assets/images/google_logo.svg');

  /// File path: assets/images/help.svg
  SvgGenImage get help => const SvgGenImage.vec('assets/images/help.svg');

  /// File path: assets/images/hub.svg
  SvgGenImage get hub => const SvgGenImage.vec('assets/images/hub.svg');

  /// File path: assets/images/info.svg
  SvgGenImage get info => const SvgGenImage.vec('assets/images/info.svg');

  /// File path: assets/images/info_icon.svg
  SvgGenImage get infoIcon =>
      const SvgGenImage.vec('assets/images/info_icon.svg');

  /// File path: assets/images/invalid_password_rule_ic.svg
  SvgGenImage get invalidPasswordRuleIc =>
      const SvgGenImage.vec('assets/images/invalid_password_rule_ic.svg');

  /// File path: assets/images/jpeg.svg
  SvgGenImage get jpeg => const SvgGenImage.vec('assets/images/jpeg.svg');

  /// File path: assets/images/language_globe.svg
  SvgGenImage get languageGlobe =>
      const SvgGenImage.vec('assets/images/language_globe.svg');

  /// File path: assets/images/learn.svg
  SvgGenImage get learn => const SvgGenImage.vec('assets/images/learn.svg');

  /// File path: assets/images/legal.svg
  SvgGenImage get legal => const SvgGenImage.vec('assets/images/legal.svg');

  /// File path: assets/images/lock.svg
  SvgGenImage get lock => const SvgGenImage.vec('assets/images/lock.svg');

  /// File path: assets/images/logout.svg
  SvgGenImage get logout => const SvgGenImage.vec('assets/images/logout.svg');

  /// File path: assets/images/logout_warning.svg
  SvgGenImage get logoutWarning =>
      const SvgGenImage.vec('assets/images/logout_warning.svg');

  /// File path: assets/images/minus-square.svg
  SvgGenImage get minusSquare =>
      const SvgGenImage.vec('assets/images/minus-square.svg');

  /// File path: assets/images/not_applied_program.svg
  SvgGenImage get notAppliedProgram =>
      const SvgGenImage.vec('assets/images/not_applied_program.svg');

  /// File path: assets/images/outlined_radio_base.svg
  SvgGenImage get outlinedRadioBase =>
      const SvgGenImage.vec('assets/images/outlined_radio_base.svg');

  /// File path: assets/images/pdf.svg
  SvgGenImage get pdf => const SvgGenImage.vec('assets/images/pdf.svg');

  /// File path: assets/images/performance.svg
  SvgGenImage get performance =>
      const SvgGenImage.vec('assets/images/performance.svg');

  /// File path: assets/images/plus-square.svg
  SvgGenImage get plusSquare =>
      const SvgGenImage.vec('assets/images/plus-square.svg');

  /// File path: assets/images/plus.svg
  SvgGenImage get plus => const SvgGenImage.vec('assets/images/plus.svg');

  /// File path: assets/images/profile.svg
  SvgGenImage get profile => const SvgGenImage.vec('assets/images/profile.svg');

  /// File path: assets/images/radio_base.svg
  SvgGenImage get radioBase =>
      const SvgGenImage.vec('assets/images/radio_base.svg');

  /// File path: assets/images/redirection_image.svg
  SvgGenImage get redirectionImage =>
      const SvgGenImage.vec('assets/images/redirection_image.svg');

  /// File path: assets/images/search-md.svg
  SvgGenImage get searchMd =>
      const SvgGenImage.vec('assets/images/search-md.svg');

  /// File path: assets/images/security.svg
  SvgGenImage get security =>
      const SvgGenImage.vec('assets/images/security.svg');

  /// File path: assets/images/settings.svg
  SvgGenImage get settings =>
      const SvgGenImage.vec('assets/images/settings.svg');

  /// File path: assets/images/show_password_ic.svg
  SvgGenImage get showPasswordIc =>
      const SvgGenImage.vec('assets/images/show_password_ic.svg');

  /// File path: assets/images/star_filled.svg
  SvgGenImage get starFilled =>
      const SvgGenImage.vec('assets/images/star_filled.svg');

  /// File path: assets/images/star_outline.svg
  SvgGenImage get starOutline =>
      const SvgGenImage.vec('assets/images/star_outline.svg');

  /// File path: assets/images/success_without_ripple.svg
  SvgGenImage get successWithoutRipple =>
      const SvgGenImage.vec('assets/images/success_without_ripple.svg');

  /// File path: assets/images/support.svg
  SvgGenImage get support => const SvgGenImage.vec('assets/images/support.svg');

  /// File path: assets/images/ticket.svg
  SvgGenImage get ticket => const SvgGenImage.vec('assets/images/ticket.svg');

  /// File path: assets/images/toast_error.svg
  SvgGenImage get toastError =>
      const SvgGenImage.vec('assets/images/toast_error.svg');

  /// File path: assets/images/trading.svg
  SvgGenImage get trading => const SvgGenImage.vec('assets/images/trading.svg');

  /// File path: assets/images/transfer.svg
  SvgGenImage get transfer =>
      const SvgGenImage.vec('assets/images/transfer.svg');

  /// File path: assets/images/trash.svg
  SvgGenImage get trash => const SvgGenImage.vec('assets/images/trash.svg');

  /// File path: assets/images/uae_button_ar.svg
  SvgGenImage get uaeButtonAr =>
      const SvgGenImage.vec('assets/images/uae_button_ar.svg');

  /// File path: assets/images/uae_button_ar_dark.svg
  SvgGenImage get uaeButtonArDark =>
      const SvgGenImage.vec('assets/images/uae_button_ar_dark.svg');

  /// File path: assets/images/uae_button_dark.svg
  SvgGenImage get uaeButtonDark =>
      const SvgGenImage.vec('assets/images/uae_button_dark.svg');

  /// File path: assets/images/uae_button_login.svg
  SvgGenImage get uaeButtonLogin =>
      const SvgGenImage.vec('assets/images/uae_button_login.svg');

  /// File path: assets/images/uae_button_login_ar.svg
  SvgGenImage get uaeButtonLoginAr =>
      const SvgGenImage.vec('assets/images/uae_button_login_ar.svg');

  /// File path: assets/images/uae_button_login_ar_dark.svg
  SvgGenImage get uaeButtonLoginArDark =>
      const SvgGenImage.vec('assets/images/uae_button_login_ar_dark.svg');

  /// File path: assets/images/uae_button_login_dark.svg
  SvgGenImage get uaeButtonLoginDark =>
      const SvgGenImage.vec('assets/images/uae_button_login_dark.svg');

  /// File path: assets/images/uae_pass_button.svg
  SvgGenImage get uaePassButton =>
      const SvgGenImage.vec('assets/images/uae_pass_button.svg');

  /// File path: assets/images/upload_cloud.svg
  SvgGenImage get uploadCloud =>
      const SvgGenImage.vec('assets/images/upload_cloud.svg');

  /// File path: assets/images/valid_password_rule_ic.svg
  SvgGenImage get validPasswordRuleIc =>
      const SvgGenImage.vec('assets/images/valid_password_rule_ic.svg');

  /// File path: assets/images/wallet.svg
  SvgGenImage get wallet => const SvgGenImage.vec('assets/images/wallet.svg');

  /// File path: assets/images/wealth.svg
  SvgGenImage get wealth => const SvgGenImage.vec('assets/images/wealth.svg');

  /// File path: assets/images/welcome_bonus_ic.svg
  SvgGenImage get welcomeBonusIc =>
      const SvgGenImage.vec('assets/images/welcome_bonus_ic.svg');

  /// File path: assets/images/withdraw.svg
  SvgGenImage get withdraw =>
      const SvgGenImage.vec('assets/images/withdraw.svg');

  /// File path: assets/images/x-circle.svg
  SvgGenImage get xCircle =>
      const SvgGenImage.vec('assets/images/x-circle.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    aPayDarkDisabled,
    aPayDarkEnabled,
    aPayLightDisabled,
    aPayLightEnabled,
    accounts,
    activeProgram,
    appBug,
    arLanIc,
    arrowLeft,
    arrowRight,
    arrowUpRight,
    authBackgroundHeader,
    authBackgroundHeaderDark,
    bannerGold,
    calendar,
    checkCircle,
    checkboxBase,
    chevronRight,
    chevronDown,
    chevronLeft,
    chevronSelectorVertical,
    clockFastForward,
    closeIc,
    copy,
    creditCardDownload,
    creditCardRefresh,
    creditCardUpload,
    deposit,
    dotGrid,
    dotsVertical,
    dropDownIc,
    duploAlertError,
    duploAlertInfo,
    duploAlertWarning,
    emptyRadioBase,
    enLanIc,
    errorIc,
    errorIcon,
    eyeClose,
    eyeOpen,
    fileType,
    gParDarkEnabled,
    gPayDarkDisabled,
    gPayLightDisabled,
    gPayLightEnabled,
    gold,
    googleLogo,
    help,
    hub,
    info,
    infoIcon,
    invalidPasswordRuleIc,
    jpeg,
    languageGlobe,
    learn,
    legal,
    lock,
    logout,
    logoutWarning,
    minusSquare,
    notAppliedProgram,
    outlinedRadioBase,
    pdf,
    performance,
    plusSquare,
    plus,
    profile,
    radioBase,
    redirectionImage,
    searchMd,
    security,
    settings,
    showPasswordIc,
    starFilled,
    starOutline,
    successWithoutRipple,
    support,
    ticket,
    toastError,
    trading,
    transfer,
    trash,
    uaeButtonAr,
    uaeButtonArDark,
    uaeButtonDark,
    uaeButtonLogin,
    uaeButtonLoginAr,
    uaeButtonLoginArDark,
    uaeButtonLoginDark,
    uaePassButton,
    uploadCloud,
    validPasswordRuleIc,
    wallet,
    wealth,
    welcomeBonusIc,
    withdraw,
    xCircle,
  ];
}

class $TestAssetsGen {
  const $TestAssetsGen();

  /// File path: test/assets/check_circle.svg
  SvgGenImage get checkCircle =>
      const SvgGenImage.vec('test/assets/check_circle.svg');

  /// File path: test/assets/clock.svg
  SvgGenImage get clock => const SvgGenImage.vec('test/assets/clock.svg');

  /// File path: test/assets/close_circle.svg
  SvgGenImage get closeCircle =>
      const SvgGenImage.vec('test/assets/close_circle.svg');

  /// Directory path: test/assets/complex
  $TestAssetsComplexGen get complex => const $TestAssetsComplexGen();

  /// File path: test/assets/discover_ic.svg
  SvgGenImage get discoverIc =>
      const SvgGenImage.vec('test/assets/discover_ic.svg');

  /// Directory path: test/assets/json
  $TestAssetsJsonGen get json => const $TestAssetsJsonGen();

  /// File path: test/assets/view.svg
  SvgGenImage get view => const SvgGenImage.vec('test/assets/view.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    checkCircle,
    clock,
    closeCircle,
    discoverIc,
    view,
  ];
}

class $TestAssetsComplexGen {
  const $TestAssetsComplexGen();

  /// File path: test/assets/complex/empty_watch_list.svg
  SvgGenImage get emptyWatchList =>
      const SvgGenImage('test/assets/complex/empty_watch_list.svg');

  /// File path: test/assets/complex/markets_ic.svg
  SvgGenImage get marketsIc =>
      const SvgGenImage('test/assets/complex/markets_ic.svg');

  /// File path: test/assets/complex/more_ic.svg
  SvgGenImage get moreIc =>
      const SvgGenImage('test/assets/complex/more_ic.svg');

  /// File path: test/assets/complex/performance_ic.svg
  SvgGenImage get performanceIc =>
      const SvgGenImage('test/assets/complex/performance_ic.svg');

  /// File path: test/assets/complex/portfolio_ic.svg
  SvgGenImage get portfolioIc =>
      const SvgGenImage('test/assets/complex/portfolio_ic.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    emptyWatchList,
    marketsIc,
    moreIc,
    performanceIc,
    portfolioIc,
  ];
}

class $TestAssetsJsonGen {
  const $TestAssetsJsonGen();

  /// File path: test/assets/json/risk_warning_dark.json
  String get riskWarningDark =>
      'packages/duplo/test/assets/json/risk_warning_dark.json';

  /// File path: test/assets/json/risk_warning_light.json
  String get riskWarningLight =>
      'packages/duplo/test/assets/json/risk_warning_light.json';

  /// List of all assets
  List<String> get values => [riskWarningDark, riskWarningLight];
}

class Assets {
  const Assets._();

  static const String package = 'duplo';

  static const $AssetsAnimationsGen animations = $AssetsAnimationsGen();
  static const $AssetsFlagsGen flags = $AssetsFlagsGen();
  static const $AssetsGifsGen gifs = $AssetsGifsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $TestGen test = $TestGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
    this.animation,
  });

  final String _assetName;

  static const String package = 'duplo';

  final Size? size;
  final Set<String> flavors;
  final AssetGenImageAnimation? animation;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
  }) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => 'packages/duplo/$_assetName';
}

class AssetGenImageAnimation {
  const AssetGenImageAnimation({
    required this.isAnimation,
    required this.duration,
    required this.frames,
  });

  final bool isAnimation;
  final Duration duration;
  final int frames;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  static const String package = 'duplo';

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    @Deprecated('Do not specify package for a generated library asset')
    String? package = package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    _svg.ColorMapper? colorMapper,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
        colorMapper: colorMapper,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => 'packages/duplo/$_assetName';
}
