import 'package:domain/src/data/enums/account_type.dart';
import 'package:domain/src/data/enums/platform_account_type.dart';
import 'package:domain/src/data/enums/platform_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'trading_account_model.freezed.dart';
part 'trading_account_model.g.dart';

@freezed
abstract class TradingAccountModel with _$TradingAccountModel {
  const TradingAccountModel._();

  const factory TradingAccountModel({
    @JsonKey(name: 'accountId') required String recordId,
    @JsonKey(name: 'platformAccountNumber') required String accountNumber,
    required int accountIdLong,
    String? dateCreated,
    @Default(PlatformAccountType.unknown)
    @JsonKey(
      name: 'platformAccountType',
      unknownEnumValue: PlatformAccountType.unknown,
    )
    PlatformAccountType platformAccountType,
    @JsonKey(name: 'accountCurrency') required String homeCurrency,
    int? leverage,

    /// The balance including credit (actualBalance + credit).
    ///
    /// **Usage:** Use [currentBalance] for **Wallet** accounts,
    /// use [actualBalance] for **Trading** accounts.
    ///
    /// Example: If actualBalance = 150 and credit = 100, then currentBalance = 250.
    ///
    /// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
    @JsonKey(name: 'currentBalance') double? currentBalance,

    /// The actual balance without credit (real deposited funds).
    ///
    /// **Usage:** Use [actualBalance] for **Trading** accounts,
    /// use [currentBalance] for **Wallet** accounts.
    ///
    /// Example: If currentBalance = 250 and credit = 100, then actualBalance = 150.
    ///
    /// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
    @JsonKey(name: 'actualBalance') double? actualBalance,
    double? credit,
    double? equity,
    double? margin,
    @JsonKey(name: 'serverCode') String? server,
    @Default(AccountType.unknown)
    @JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown)
    AccountType accountType,
    String? brokerId,
    String? name,
    @JsonKey(
      name: 'platformType',
      unknownEnumValue: PlatformType.unknown,
      fromJson: _platformTypeFromJson,
    )
    @Default(PlatformType.unknown)
    PlatformType platformType,
    String? clientId,
    @Default(false) bool isDemo,
    double? profit,
    String? accountStatus,
    String? leadSource,
    double? grossProfit,
    double? marginLevel,
    required String primaryEmail,
    String? accountGroup,
    String? classification,
    double? freeMargin,
    @Default(false) bool isSwapFree,
    double? balanceAlternateCurrency,
    double? marginAlternateCurrency,
    double? equityAlternateCurrency,
    double? profitAlternateCurrency,
    double? grossProfitAlternateCurrency,
    double? creditAlternateCurrency,
    String? accountCurrencyUsdPair,
    @Default("") String serverName,
    String? nickName,
    @Default("") String platformTypeName,
    @Default(false) bool hasOpenPositions,
  }) = _TradingAccountModel;

  factory TradingAccountModel.fromJson(Map<String, dynamic> json) =>
      _$TradingAccountModelFromJson(json);

  /// Returns the correct balance based on [accountType].
  ///
  /// - For **Trading** accounts: returns [actualBalance]
  /// - For **Wallet** accounts: returns [currentBalance]
  double? get effectiveBalance => switch (accountType) {
    AccountType.trading => actualBalance,
    AccountType.landingWallet || AccountType.unknown => currentBalance,
  };
}

// Add this helper function outside the class
PlatformType _platformTypeFromJson(Object? value) {
  if (value == null) return PlatformType.unknown;

  // Try to match the string value directly
  if (value is String) {
    for (final type in PlatformType.values) {
      if (type.name.toLowerCase() == value.toLowerCase()) {
        return type;
      }
    }

    switch (value) {
      case 'Equiti':
        return PlatformType.equiti;
      case 'MT4':
        return PlatformType.mt4;
      case 'MT5':
        return PlatformType.mt5;
      case 'EquitiTrader':
        return PlatformType.equitiTrader;
      default:
        return PlatformType.unknown;
    }
  }

  return PlatformType.unknown;
}
