// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trading_account_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_TradingAccountModel _$TradingAccountModelFromJson(
  Map<String, dynamic> json,
) => $checkedCreate(
  '_TradingAccountModel',
  json,
  ($checkedConvert) {
    final val = _TradingAccountModel(
      recordId: $checkedConvert('accountId', (v) => v as String),
      accountNumber: $checkedConvert(
        'platformAccountNumber',
        (v) => v as String,
      ),
      accountIdLong: $checkedConvert(
        'accountIdLong',
        (v) => (v as num).toInt(),
      ),
      dateCreated: $checkedConvert('dateCreated', (v) => v as String?),
      platformAccountType: $checkedConvert(
        'platformAccountType',
        (v) =>
            $enumDecodeNullable(
              _$PlatformAccountTypeEnumMap,
              v,
              unknownValue: PlatformAccountType.unknown,
            ) ??
            PlatformAccountType.unknown,
      ),
      homeCurrency: $checkedConvert('accountCurrency', (v) => v as String),
      leverage: $checkedConvert('leverage', (v) => (v as num?)?.toInt()),
      currentBalance: $checkedConvert(
        'currentBalance',
        (v) => (v as num?)?.toDouble(),
      ),
      actualBalance: $checkedConvert(
        'actualBalance',
        (v) => (v as num?)?.toDouble(),
      ),
      credit: $checkedConvert('credit', (v) => (v as num?)?.toDouble()),
      equity: $checkedConvert('equity', (v) => (v as num?)?.toDouble()),
      margin: $checkedConvert('margin', (v) => (v as num?)?.toDouble()),
      server: $checkedConvert('serverCode', (v) => v as String?),
      accountType: $checkedConvert(
        'accountType',
        (v) =>
            $enumDecodeNullable(
              _$AccountTypeEnumMap,
              v,
              unknownValue: AccountType.unknown,
            ) ??
            AccountType.unknown,
      ),
      brokerId: $checkedConvert('brokerId', (v) => v as String?),
      name: $checkedConvert('name', (v) => v as String?),
      platformType: $checkedConvert(
        'platformType',
        (v) => v == null ? PlatformType.unknown : _platformTypeFromJson(v),
      ),
      clientId: $checkedConvert('clientId', (v) => v as String?),
      isDemo: $checkedConvert('isDemo', (v) => v as bool? ?? false),
      profit: $checkedConvert('profit', (v) => (v as num?)?.toDouble()),
      accountStatus: $checkedConvert('accountStatus', (v) => v as String?),
      leadSource: $checkedConvert('leadSource', (v) => v as String?),
      grossProfit: $checkedConvert(
        'grossProfit',
        (v) => (v as num?)?.toDouble(),
      ),
      marginLevel: $checkedConvert(
        'marginLevel',
        (v) => (v as num?)?.toDouble(),
      ),
      primaryEmail: $checkedConvert('primaryEmail', (v) => v as String),
      accountGroup: $checkedConvert('accountGroup', (v) => v as String?),
      classification: $checkedConvert('classification', (v) => v as String?),
      freeMargin: $checkedConvert('freeMargin', (v) => (v as num?)?.toDouble()),
      isSwapFree: $checkedConvert('isSwapFree', (v) => v as bool? ?? false),
      balanceAlternateCurrency: $checkedConvert(
        'balanceAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      marginAlternateCurrency: $checkedConvert(
        'marginAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      equityAlternateCurrency: $checkedConvert(
        'equityAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      profitAlternateCurrency: $checkedConvert(
        'profitAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      grossProfitAlternateCurrency: $checkedConvert(
        'grossProfitAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      creditAlternateCurrency: $checkedConvert(
        'creditAlternateCurrency',
        (v) => (v as num?)?.toDouble(),
      ),
      accountCurrencyUsdPair: $checkedConvert(
        'accountCurrencyUsdPair',
        (v) => v as String?,
      ),
      serverName: $checkedConvert('serverName', (v) => v as String? ?? ""),
      nickName: $checkedConvert('nickName', (v) => v as String?),
      platformTypeName: $checkedConvert(
        'platformTypeName',
        (v) => v as String? ?? "",
      ),
      hasOpenPositions: $checkedConvert(
        'hasOpenPositions',
        (v) => v as bool? ?? false,
      ),
    );
    return val;
  },
  fieldKeyMap: const {
    'recordId': 'accountId',
    'accountNumber': 'platformAccountNumber',
    'homeCurrency': 'accountCurrency',
    'server': 'serverCode',
  },
);

Map<String, dynamic> _$TradingAccountModelToJson(
  _TradingAccountModel instance,
) => <String, dynamic>{
  'accountId': instance.recordId,
  'platformAccountNumber': instance.accountNumber,
  'accountIdLong': instance.accountIdLong,
  if (instance.dateCreated case final value?) 'dateCreated': value,
  'platformAccountType':
      _$PlatformAccountTypeEnumMap[instance.platformAccountType]!,
  'accountCurrency': instance.homeCurrency,
  if (instance.leverage case final value?) 'leverage': value,
  if (instance.currentBalance case final value?) 'currentBalance': value,
  if (instance.actualBalance case final value?) 'actualBalance': value,
  if (instance.credit case final value?) 'credit': value,
  if (instance.equity case final value?) 'equity': value,
  if (instance.margin case final value?) 'margin': value,
  if (instance.server case final value?) 'serverCode': value,
  'accountType': _$AccountTypeEnumMap[instance.accountType]!,
  if (instance.brokerId case final value?) 'brokerId': value,
  if (instance.name case final value?) 'name': value,
  'platformType': _$PlatformTypeEnumMap[instance.platformType]!,
  if (instance.clientId case final value?) 'clientId': value,
  'isDemo': instance.isDemo,
  if (instance.profit case final value?) 'profit': value,
  if (instance.accountStatus case final value?) 'accountStatus': value,
  if (instance.leadSource case final value?) 'leadSource': value,
  if (instance.grossProfit case final value?) 'grossProfit': value,
  if (instance.marginLevel case final value?) 'marginLevel': value,
  'primaryEmail': instance.primaryEmail,
  if (instance.accountGroup case final value?) 'accountGroup': value,
  if (instance.classification case final value?) 'classification': value,
  if (instance.freeMargin case final value?) 'freeMargin': value,
  'isSwapFree': instance.isSwapFree,
  if (instance.balanceAlternateCurrency case final value?)
    'balanceAlternateCurrency': value,
  if (instance.marginAlternateCurrency case final value?)
    'marginAlternateCurrency': value,
  if (instance.equityAlternateCurrency case final value?)
    'equityAlternateCurrency': value,
  if (instance.profitAlternateCurrency case final value?)
    'profitAlternateCurrency': value,
  if (instance.grossProfitAlternateCurrency case final value?)
    'grossProfitAlternateCurrency': value,
  if (instance.creditAlternateCurrency case final value?)
    'creditAlternateCurrency': value,
  if (instance.accountCurrencyUsdPair case final value?)
    'accountCurrencyUsdPair': value,
  'serverName': instance.serverName,
  if (instance.nickName case final value?) 'nickName': value,
  'platformTypeName': instance.platformTypeName,
  'hasOpenPositions': instance.hasOpenPositions,
};

const _$PlatformAccountTypeEnumMap = {
  PlatformAccountType.premiere: 'Premiere',
  PlatformAccountType.standard: 'Standard',
  PlatformAccountType.classic: 'Classic',
  PlatformAccountType.micro: 'Micro',
  PlatformAccountType.unknown: 'unknown',
};

const _$AccountTypeEnumMap = {
  AccountType.trading: 'Trading',
  AccountType.landingWallet: 'LandingWallet',
  AccountType.unknown: 'unknown',
};

const _$PlatformTypeEnumMap = {
  PlatformType.equitiTrader: 'EquitiTrader',
  PlatformType.mt4: 'MT4',
  PlatformType.mt5: 'MT5',
  PlatformType.equiti: 'Equiti',
  PlatformType.unknown: 'unknown',
};
