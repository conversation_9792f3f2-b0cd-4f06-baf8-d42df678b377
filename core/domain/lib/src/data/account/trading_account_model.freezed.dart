// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'trading_account_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$TradingAccountModel {

@JsonKey(name: 'accountId') String get recordId;@JsonKey(name: 'platformAccountNumber') String get accountNumber; int get accountIdLong; String? get dateCreated;@JsonKey(name: 'platformAccountType', unknownEnumValue: PlatformAccountType.unknown) PlatformAccountType get platformAccountType;@JsonKey(name: 'accountCurrency') String get homeCurrency; int? get leverage;/// The balance including credit (actualBalance + credit).
///
/// **Usage:** Use [currentBalance] for **Wallet** accounts,
/// use [actualBalance] for **Trading** accounts.
///
/// Example: If actualBalance = 150 and credit = 100, then currentBalance = 250.
///
/// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
@JsonKey(name: 'currentBalance') double? get currentBalance;/// The actual balance without credit (real deposited funds).
///
/// **Usage:** Use [actualBalance] for **Trading** accounts,
/// use [currentBalance] for **Wallet** accounts.
///
/// Example: If currentBalance = 250 and credit = 100, then actualBalance = 150.
///
/// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
@JsonKey(name: 'actualBalance') double? get actualBalance; double? get credit; double? get equity; double? get margin;@JsonKey(name: 'serverCode') String? get server;@JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown) AccountType get accountType; String? get brokerId; String? get name;@JsonKey(name: 'platformType', unknownEnumValue: PlatformType.unknown, fromJson: _platformTypeFromJson) PlatformType get platformType; String? get clientId; bool get isDemo; double? get profit; String? get accountStatus; String? get leadSource; double? get grossProfit; double? get marginLevel; String get primaryEmail; String? get accountGroup; String? get classification; double? get freeMargin; bool get isSwapFree; double? get balanceAlternateCurrency; double? get marginAlternateCurrency; double? get equityAlternateCurrency; double? get profitAlternateCurrency; double? get grossProfitAlternateCurrency; double? get creditAlternateCurrency; String? get accountCurrencyUsdPair; String get serverName; String? get nickName; String get platformTypeName; bool get hasOpenPositions;
/// Create a copy of TradingAccountModel
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TradingAccountModelCopyWith<TradingAccountModel> get copyWith => _$TradingAccountModelCopyWithImpl<TradingAccountModel>(this as TradingAccountModel, _$identity);

  /// Serializes this TradingAccountModel to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TradingAccountModel&&(identical(other.recordId, recordId) || other.recordId == recordId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.accountIdLong, accountIdLong) || other.accountIdLong == accountIdLong)&&(identical(other.dateCreated, dateCreated) || other.dateCreated == dateCreated)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.currentBalance, currentBalance) || other.currentBalance == currentBalance)&&(identical(other.actualBalance, actualBalance) || other.actualBalance == actualBalance)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.server, server) || other.server == server)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.name, name) || other.name == name)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.isDemo, isDemo) || other.isDemo == isDemo)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.leadSource, leadSource) || other.leadSource == leadSource)&&(identical(other.grossProfit, grossProfit) || other.grossProfit == grossProfit)&&(identical(other.marginLevel, marginLevel) || other.marginLevel == marginLevel)&&(identical(other.primaryEmail, primaryEmail) || other.primaryEmail == primaryEmail)&&(identical(other.accountGroup, accountGroup) || other.accountGroup == accountGroup)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.freeMargin, freeMargin) || other.freeMargin == freeMargin)&&(identical(other.isSwapFree, isSwapFree) || other.isSwapFree == isSwapFree)&&(identical(other.balanceAlternateCurrency, balanceAlternateCurrency) || other.balanceAlternateCurrency == balanceAlternateCurrency)&&(identical(other.marginAlternateCurrency, marginAlternateCurrency) || other.marginAlternateCurrency == marginAlternateCurrency)&&(identical(other.equityAlternateCurrency, equityAlternateCurrency) || other.equityAlternateCurrency == equityAlternateCurrency)&&(identical(other.profitAlternateCurrency, profitAlternateCurrency) || other.profitAlternateCurrency == profitAlternateCurrency)&&(identical(other.grossProfitAlternateCurrency, grossProfitAlternateCurrency) || other.grossProfitAlternateCurrency == grossProfitAlternateCurrency)&&(identical(other.creditAlternateCurrency, creditAlternateCurrency) || other.creditAlternateCurrency == creditAlternateCurrency)&&(identical(other.accountCurrencyUsdPair, accountCurrencyUsdPair) || other.accountCurrencyUsdPair == accountCurrencyUsdPair)&&(identical(other.serverName, serverName) || other.serverName == serverName)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.platformTypeName, platformTypeName) || other.platformTypeName == platformTypeName)&&(identical(other.hasOpenPositions, hasOpenPositions) || other.hasOpenPositions == hasOpenPositions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,recordId,accountNumber,accountIdLong,dateCreated,platformAccountType,homeCurrency,leverage,currentBalance,actualBalance,credit,equity,margin,server,accountType,brokerId,name,platformType,clientId,isDemo,profit,accountStatus,leadSource,grossProfit,marginLevel,primaryEmail,accountGroup,classification,freeMargin,isSwapFree,balanceAlternateCurrency,marginAlternateCurrency,equityAlternateCurrency,profitAlternateCurrency,grossProfitAlternateCurrency,creditAlternateCurrency,accountCurrencyUsdPair,serverName,nickName,platformTypeName,hasOpenPositions]);

@override
String toString() {
  return 'TradingAccountModel(recordId: $recordId, accountNumber: $accountNumber, accountIdLong: $accountIdLong, dateCreated: $dateCreated, platformAccountType: $platformAccountType, homeCurrency: $homeCurrency, leverage: $leverage, currentBalance: $currentBalance, actualBalance: $actualBalance, credit: $credit, equity: $equity, margin: $margin, server: $server, accountType: $accountType, brokerId: $brokerId, name: $name, platformType: $platformType, clientId: $clientId, isDemo: $isDemo, profit: $profit, accountStatus: $accountStatus, leadSource: $leadSource, grossProfit: $grossProfit, marginLevel: $marginLevel, primaryEmail: $primaryEmail, accountGroup: $accountGroup, classification: $classification, freeMargin: $freeMargin, isSwapFree: $isSwapFree, balanceAlternateCurrency: $balanceAlternateCurrency, marginAlternateCurrency: $marginAlternateCurrency, equityAlternateCurrency: $equityAlternateCurrency, profitAlternateCurrency: $profitAlternateCurrency, grossProfitAlternateCurrency: $grossProfitAlternateCurrency, creditAlternateCurrency: $creditAlternateCurrency, accountCurrencyUsdPair: $accountCurrencyUsdPair, serverName: $serverName, nickName: $nickName, platformTypeName: $platformTypeName, hasOpenPositions: $hasOpenPositions)';
}


}

/// @nodoc
abstract mixin class $TradingAccountModelCopyWith<$Res>  {
  factory $TradingAccountModelCopyWith(TradingAccountModel value, $Res Function(TradingAccountModel) _then) = _$TradingAccountModelCopyWithImpl;
@useResult
$Res call({
@JsonKey(name: 'accountId') String recordId,@JsonKey(name: 'platformAccountNumber') String accountNumber, int accountIdLong, String? dateCreated,@JsonKey(name: 'platformAccountType', unknownEnumValue: PlatformAccountType.unknown) PlatformAccountType platformAccountType,@JsonKey(name: 'accountCurrency') String homeCurrency, int? leverage,@JsonKey(name: 'currentBalance') double? currentBalance,@JsonKey(name: 'actualBalance') double? actualBalance, double? credit, double? equity, double? margin,@JsonKey(name: 'serverCode') String? server,@JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown) AccountType accountType, String? brokerId, String? name,@JsonKey(name: 'platformType', unknownEnumValue: PlatformType.unknown, fromJson: _platformTypeFromJson) PlatformType platformType, String? clientId, bool isDemo, double? profit, String? accountStatus, String? leadSource, double? grossProfit, double? marginLevel, String primaryEmail, String? accountGroup, String? classification, double? freeMargin, bool isSwapFree, double? balanceAlternateCurrency, double? marginAlternateCurrency, double? equityAlternateCurrency, double? profitAlternateCurrency, double? grossProfitAlternateCurrency, double? creditAlternateCurrency, String? accountCurrencyUsdPair, String serverName, String? nickName, String platformTypeName, bool hasOpenPositions
});




}
/// @nodoc
class _$TradingAccountModelCopyWithImpl<$Res>
    implements $TradingAccountModelCopyWith<$Res> {
  _$TradingAccountModelCopyWithImpl(this._self, this._then);

  final TradingAccountModel _self;
  final $Res Function(TradingAccountModel) _then;

/// Create a copy of TradingAccountModel
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? recordId = null,Object? accountNumber = null,Object? accountIdLong = null,Object? dateCreated = freezed,Object? platformAccountType = null,Object? homeCurrency = null,Object? leverage = freezed,Object? currentBalance = freezed,Object? actualBalance = freezed,Object? credit = freezed,Object? equity = freezed,Object? margin = freezed,Object? server = freezed,Object? accountType = null,Object? brokerId = freezed,Object? name = freezed,Object? platformType = null,Object? clientId = freezed,Object? isDemo = null,Object? profit = freezed,Object? accountStatus = freezed,Object? leadSource = freezed,Object? grossProfit = freezed,Object? marginLevel = freezed,Object? primaryEmail = null,Object? accountGroup = freezed,Object? classification = freezed,Object? freeMargin = freezed,Object? isSwapFree = null,Object? balanceAlternateCurrency = freezed,Object? marginAlternateCurrency = freezed,Object? equityAlternateCurrency = freezed,Object? profitAlternateCurrency = freezed,Object? grossProfitAlternateCurrency = freezed,Object? creditAlternateCurrency = freezed,Object? accountCurrencyUsdPair = freezed,Object? serverName = null,Object? nickName = freezed,Object? platformTypeName = null,Object? hasOpenPositions = null,}) {
  return _then(_self.copyWith(
recordId: null == recordId ? _self.recordId : recordId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,accountIdLong: null == accountIdLong ? _self.accountIdLong : accountIdLong // ignore: cast_nullable_to_non_nullable
as int,dateCreated: freezed == dateCreated ? _self.dateCreated : dateCreated // ignore: cast_nullable_to_non_nullable
as String?,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as PlatformAccountType,homeCurrency: null == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as String,leverage: freezed == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int?,currentBalance: freezed == currentBalance ? _self.currentBalance : currentBalance // ignore: cast_nullable_to_non_nullable
as double?,actualBalance: freezed == actualBalance ? _self.actualBalance : actualBalance // ignore: cast_nullable_to_non_nullable
as double?,credit: freezed == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double?,equity: freezed == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as double?,margin: freezed == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double?,server: freezed == server ? _self.server : server // ignore: cast_nullable_to_non_nullable
as String?,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,brokerId: freezed == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as PlatformType,clientId: freezed == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as String?,isDemo: null == isDemo ? _self.isDemo : isDemo // ignore: cast_nullable_to_non_nullable
as bool,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,accountStatus: freezed == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as String?,leadSource: freezed == leadSource ? _self.leadSource : leadSource // ignore: cast_nullable_to_non_nullable
as String?,grossProfit: freezed == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as double?,marginLevel: freezed == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double?,primaryEmail: null == primaryEmail ? _self.primaryEmail : primaryEmail // ignore: cast_nullable_to_non_nullable
as String,accountGroup: freezed == accountGroup ? _self.accountGroup : accountGroup // ignore: cast_nullable_to_non_nullable
as String?,classification: freezed == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as String?,freeMargin: freezed == freeMargin ? _self.freeMargin : freeMargin // ignore: cast_nullable_to_non_nullable
as double?,isSwapFree: null == isSwapFree ? _self.isSwapFree : isSwapFree // ignore: cast_nullable_to_non_nullable
as bool,balanceAlternateCurrency: freezed == balanceAlternateCurrency ? _self.balanceAlternateCurrency : balanceAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,marginAlternateCurrency: freezed == marginAlternateCurrency ? _self.marginAlternateCurrency : marginAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,equityAlternateCurrency: freezed == equityAlternateCurrency ? _self.equityAlternateCurrency : equityAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,profitAlternateCurrency: freezed == profitAlternateCurrency ? _self.profitAlternateCurrency : profitAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,grossProfitAlternateCurrency: freezed == grossProfitAlternateCurrency ? _self.grossProfitAlternateCurrency : grossProfitAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,creditAlternateCurrency: freezed == creditAlternateCurrency ? _self.creditAlternateCurrency : creditAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,accountCurrencyUsdPair: freezed == accountCurrencyUsdPair ? _self.accountCurrencyUsdPair : accountCurrencyUsdPair // ignore: cast_nullable_to_non_nullable
as String?,serverName: null == serverName ? _self.serverName : serverName // ignore: cast_nullable_to_non_nullable
as String,nickName: freezed == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String?,platformTypeName: null == platformTypeName ? _self.platformTypeName : platformTypeName // ignore: cast_nullable_to_non_nullable
as String,hasOpenPositions: null == hasOpenPositions ? _self.hasOpenPositions : hasOpenPositions // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _TradingAccountModel extends TradingAccountModel {
  const _TradingAccountModel({@JsonKey(name: 'accountId') required this.recordId, @JsonKey(name: 'platformAccountNumber') required this.accountNumber, required this.accountIdLong, this.dateCreated, @JsonKey(name: 'platformAccountType', unknownEnumValue: PlatformAccountType.unknown) this.platformAccountType = PlatformAccountType.unknown, @JsonKey(name: 'accountCurrency') required this.homeCurrency, this.leverage, @JsonKey(name: 'currentBalance') this.currentBalance, @JsonKey(name: 'actualBalance') this.actualBalance, this.credit, this.equity, this.margin, @JsonKey(name: 'serverCode') this.server, @JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown) this.accountType = AccountType.unknown, this.brokerId, this.name, @JsonKey(name: 'platformType', unknownEnumValue: PlatformType.unknown, fromJson: _platformTypeFromJson) this.platformType = PlatformType.unknown, this.clientId, this.isDemo = false, this.profit, this.accountStatus, this.leadSource, this.grossProfit, this.marginLevel, required this.primaryEmail, this.accountGroup, this.classification, this.freeMargin, this.isSwapFree = false, this.balanceAlternateCurrency, this.marginAlternateCurrency, this.equityAlternateCurrency, this.profitAlternateCurrency, this.grossProfitAlternateCurrency, this.creditAlternateCurrency, this.accountCurrencyUsdPair, this.serverName = "", this.nickName, this.platformTypeName = "", this.hasOpenPositions = false}): super._();
  factory _TradingAccountModel.fromJson(Map<String, dynamic> json) => _$TradingAccountModelFromJson(json);

@override@JsonKey(name: 'accountId') final  String recordId;
@override@JsonKey(name: 'platformAccountNumber') final  String accountNumber;
@override final  int accountIdLong;
@override final  String? dateCreated;
@override@JsonKey(name: 'platformAccountType', unknownEnumValue: PlatformAccountType.unknown) final  PlatformAccountType platformAccountType;
@override@JsonKey(name: 'accountCurrency') final  String homeCurrency;
@override final  int? leverage;
/// The balance including credit (actualBalance + credit).
///
/// **Usage:** Use [currentBalance] for **Wallet** accounts,
/// use [actualBalance] for **Trading** accounts.
///
/// Example: If actualBalance = 150 and credit = 100, then currentBalance = 250.
///
/// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
@override@JsonKey(name: 'currentBalance') final  double? currentBalance;
/// The actual balance without credit (real deposited funds).
///
/// **Usage:** Use [actualBalance] for **Trading** accounts,
/// use [currentBalance] for **Wallet** accounts.
///
/// Example: If currentBalance = 250 and credit = 100, then actualBalance = 150.
///
/// See: https://equitiglobalmarkets.atlassian.net/browse/CAQ-2556
@override@JsonKey(name: 'actualBalance') final  double? actualBalance;
@override final  double? credit;
@override final  double? equity;
@override final  double? margin;
@override@JsonKey(name: 'serverCode') final  String? server;
@override@JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown) final  AccountType accountType;
@override final  String? brokerId;
@override final  String? name;
@override@JsonKey(name: 'platformType', unknownEnumValue: PlatformType.unknown, fromJson: _platformTypeFromJson) final  PlatformType platformType;
@override final  String? clientId;
@override@JsonKey() final  bool isDemo;
@override final  double? profit;
@override final  String? accountStatus;
@override final  String? leadSource;
@override final  double? grossProfit;
@override final  double? marginLevel;
@override final  String primaryEmail;
@override final  String? accountGroup;
@override final  String? classification;
@override final  double? freeMargin;
@override@JsonKey() final  bool isSwapFree;
@override final  double? balanceAlternateCurrency;
@override final  double? marginAlternateCurrency;
@override final  double? equityAlternateCurrency;
@override final  double? profitAlternateCurrency;
@override final  double? grossProfitAlternateCurrency;
@override final  double? creditAlternateCurrency;
@override final  String? accountCurrencyUsdPair;
@override@JsonKey() final  String serverName;
@override final  String? nickName;
@override@JsonKey() final  String platformTypeName;
@override@JsonKey() final  bool hasOpenPositions;

/// Create a copy of TradingAccountModel
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$TradingAccountModelCopyWith<_TradingAccountModel> get copyWith => __$TradingAccountModelCopyWithImpl<_TradingAccountModel>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$TradingAccountModelToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _TradingAccountModel&&(identical(other.recordId, recordId) || other.recordId == recordId)&&(identical(other.accountNumber, accountNumber) || other.accountNumber == accountNumber)&&(identical(other.accountIdLong, accountIdLong) || other.accountIdLong == accountIdLong)&&(identical(other.dateCreated, dateCreated) || other.dateCreated == dateCreated)&&(identical(other.platformAccountType, platformAccountType) || other.platformAccountType == platformAccountType)&&(identical(other.homeCurrency, homeCurrency) || other.homeCurrency == homeCurrency)&&(identical(other.leverage, leverage) || other.leverage == leverage)&&(identical(other.currentBalance, currentBalance) || other.currentBalance == currentBalance)&&(identical(other.actualBalance, actualBalance) || other.actualBalance == actualBalance)&&(identical(other.credit, credit) || other.credit == credit)&&(identical(other.equity, equity) || other.equity == equity)&&(identical(other.margin, margin) || other.margin == margin)&&(identical(other.server, server) || other.server == server)&&(identical(other.accountType, accountType) || other.accountType == accountType)&&(identical(other.brokerId, brokerId) || other.brokerId == brokerId)&&(identical(other.name, name) || other.name == name)&&(identical(other.platformType, platformType) || other.platformType == platformType)&&(identical(other.clientId, clientId) || other.clientId == clientId)&&(identical(other.isDemo, isDemo) || other.isDemo == isDemo)&&(identical(other.profit, profit) || other.profit == profit)&&(identical(other.accountStatus, accountStatus) || other.accountStatus == accountStatus)&&(identical(other.leadSource, leadSource) || other.leadSource == leadSource)&&(identical(other.grossProfit, grossProfit) || other.grossProfit == grossProfit)&&(identical(other.marginLevel, marginLevel) || other.marginLevel == marginLevel)&&(identical(other.primaryEmail, primaryEmail) || other.primaryEmail == primaryEmail)&&(identical(other.accountGroup, accountGroup) || other.accountGroup == accountGroup)&&(identical(other.classification, classification) || other.classification == classification)&&(identical(other.freeMargin, freeMargin) || other.freeMargin == freeMargin)&&(identical(other.isSwapFree, isSwapFree) || other.isSwapFree == isSwapFree)&&(identical(other.balanceAlternateCurrency, balanceAlternateCurrency) || other.balanceAlternateCurrency == balanceAlternateCurrency)&&(identical(other.marginAlternateCurrency, marginAlternateCurrency) || other.marginAlternateCurrency == marginAlternateCurrency)&&(identical(other.equityAlternateCurrency, equityAlternateCurrency) || other.equityAlternateCurrency == equityAlternateCurrency)&&(identical(other.profitAlternateCurrency, profitAlternateCurrency) || other.profitAlternateCurrency == profitAlternateCurrency)&&(identical(other.grossProfitAlternateCurrency, grossProfitAlternateCurrency) || other.grossProfitAlternateCurrency == grossProfitAlternateCurrency)&&(identical(other.creditAlternateCurrency, creditAlternateCurrency) || other.creditAlternateCurrency == creditAlternateCurrency)&&(identical(other.accountCurrencyUsdPair, accountCurrencyUsdPair) || other.accountCurrencyUsdPair == accountCurrencyUsdPair)&&(identical(other.serverName, serverName) || other.serverName == serverName)&&(identical(other.nickName, nickName) || other.nickName == nickName)&&(identical(other.platformTypeName, platformTypeName) || other.platformTypeName == platformTypeName)&&(identical(other.hasOpenPositions, hasOpenPositions) || other.hasOpenPositions == hasOpenPositions));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,recordId,accountNumber,accountIdLong,dateCreated,platformAccountType,homeCurrency,leverage,currentBalance,actualBalance,credit,equity,margin,server,accountType,brokerId,name,platformType,clientId,isDemo,profit,accountStatus,leadSource,grossProfit,marginLevel,primaryEmail,accountGroup,classification,freeMargin,isSwapFree,balanceAlternateCurrency,marginAlternateCurrency,equityAlternateCurrency,profitAlternateCurrency,grossProfitAlternateCurrency,creditAlternateCurrency,accountCurrencyUsdPair,serverName,nickName,platformTypeName,hasOpenPositions]);

@override
String toString() {
  return 'TradingAccountModel(recordId: $recordId, accountNumber: $accountNumber, accountIdLong: $accountIdLong, dateCreated: $dateCreated, platformAccountType: $platformAccountType, homeCurrency: $homeCurrency, leverage: $leverage, currentBalance: $currentBalance, actualBalance: $actualBalance, credit: $credit, equity: $equity, margin: $margin, server: $server, accountType: $accountType, brokerId: $brokerId, name: $name, platformType: $platformType, clientId: $clientId, isDemo: $isDemo, profit: $profit, accountStatus: $accountStatus, leadSource: $leadSource, grossProfit: $grossProfit, marginLevel: $marginLevel, primaryEmail: $primaryEmail, accountGroup: $accountGroup, classification: $classification, freeMargin: $freeMargin, isSwapFree: $isSwapFree, balanceAlternateCurrency: $balanceAlternateCurrency, marginAlternateCurrency: $marginAlternateCurrency, equityAlternateCurrency: $equityAlternateCurrency, profitAlternateCurrency: $profitAlternateCurrency, grossProfitAlternateCurrency: $grossProfitAlternateCurrency, creditAlternateCurrency: $creditAlternateCurrency, accountCurrencyUsdPair: $accountCurrencyUsdPair, serverName: $serverName, nickName: $nickName, platformTypeName: $platformTypeName, hasOpenPositions: $hasOpenPositions)';
}


}

/// @nodoc
abstract mixin class _$TradingAccountModelCopyWith<$Res> implements $TradingAccountModelCopyWith<$Res> {
  factory _$TradingAccountModelCopyWith(_TradingAccountModel value, $Res Function(_TradingAccountModel) _then) = __$TradingAccountModelCopyWithImpl;
@override @useResult
$Res call({
@JsonKey(name: 'accountId') String recordId,@JsonKey(name: 'platformAccountNumber') String accountNumber, int accountIdLong, String? dateCreated,@JsonKey(name: 'platformAccountType', unknownEnumValue: PlatformAccountType.unknown) PlatformAccountType platformAccountType,@JsonKey(name: 'accountCurrency') String homeCurrency, int? leverage,@JsonKey(name: 'currentBalance') double? currentBalance,@JsonKey(name: 'actualBalance') double? actualBalance, double? credit, double? equity, double? margin,@JsonKey(name: 'serverCode') String? server,@JsonKey(name: 'accountType', unknownEnumValue: AccountType.unknown) AccountType accountType, String? brokerId, String? name,@JsonKey(name: 'platformType', unknownEnumValue: PlatformType.unknown, fromJson: _platformTypeFromJson) PlatformType platformType, String? clientId, bool isDemo, double? profit, String? accountStatus, String? leadSource, double? grossProfit, double? marginLevel, String primaryEmail, String? accountGroup, String? classification, double? freeMargin, bool isSwapFree, double? balanceAlternateCurrency, double? marginAlternateCurrency, double? equityAlternateCurrency, double? profitAlternateCurrency, double? grossProfitAlternateCurrency, double? creditAlternateCurrency, String? accountCurrencyUsdPair, String serverName, String? nickName, String platformTypeName, bool hasOpenPositions
});




}
/// @nodoc
class __$TradingAccountModelCopyWithImpl<$Res>
    implements _$TradingAccountModelCopyWith<$Res> {
  __$TradingAccountModelCopyWithImpl(this._self, this._then);

  final _TradingAccountModel _self;
  final $Res Function(_TradingAccountModel) _then;

/// Create a copy of TradingAccountModel
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? recordId = null,Object? accountNumber = null,Object? accountIdLong = null,Object? dateCreated = freezed,Object? platformAccountType = null,Object? homeCurrency = null,Object? leverage = freezed,Object? currentBalance = freezed,Object? actualBalance = freezed,Object? credit = freezed,Object? equity = freezed,Object? margin = freezed,Object? server = freezed,Object? accountType = null,Object? brokerId = freezed,Object? name = freezed,Object? platformType = null,Object? clientId = freezed,Object? isDemo = null,Object? profit = freezed,Object? accountStatus = freezed,Object? leadSource = freezed,Object? grossProfit = freezed,Object? marginLevel = freezed,Object? primaryEmail = null,Object? accountGroup = freezed,Object? classification = freezed,Object? freeMargin = freezed,Object? isSwapFree = null,Object? balanceAlternateCurrency = freezed,Object? marginAlternateCurrency = freezed,Object? equityAlternateCurrency = freezed,Object? profitAlternateCurrency = freezed,Object? grossProfitAlternateCurrency = freezed,Object? creditAlternateCurrency = freezed,Object? accountCurrencyUsdPair = freezed,Object? serverName = null,Object? nickName = freezed,Object? platformTypeName = null,Object? hasOpenPositions = null,}) {
  return _then(_TradingAccountModel(
recordId: null == recordId ? _self.recordId : recordId // ignore: cast_nullable_to_non_nullable
as String,accountNumber: null == accountNumber ? _self.accountNumber : accountNumber // ignore: cast_nullable_to_non_nullable
as String,accountIdLong: null == accountIdLong ? _self.accountIdLong : accountIdLong // ignore: cast_nullable_to_non_nullable
as int,dateCreated: freezed == dateCreated ? _self.dateCreated : dateCreated // ignore: cast_nullable_to_non_nullable
as String?,platformAccountType: null == platformAccountType ? _self.platformAccountType : platformAccountType // ignore: cast_nullable_to_non_nullable
as PlatformAccountType,homeCurrency: null == homeCurrency ? _self.homeCurrency : homeCurrency // ignore: cast_nullable_to_non_nullable
as String,leverage: freezed == leverage ? _self.leverage : leverage // ignore: cast_nullable_to_non_nullable
as int?,currentBalance: freezed == currentBalance ? _self.currentBalance : currentBalance // ignore: cast_nullable_to_non_nullable
as double?,actualBalance: freezed == actualBalance ? _self.actualBalance : actualBalance // ignore: cast_nullable_to_non_nullable
as double?,credit: freezed == credit ? _self.credit : credit // ignore: cast_nullable_to_non_nullable
as double?,equity: freezed == equity ? _self.equity : equity // ignore: cast_nullable_to_non_nullable
as double?,margin: freezed == margin ? _self.margin : margin // ignore: cast_nullable_to_non_nullable
as double?,server: freezed == server ? _self.server : server // ignore: cast_nullable_to_non_nullable
as String?,accountType: null == accountType ? _self.accountType : accountType // ignore: cast_nullable_to_non_nullable
as AccountType,brokerId: freezed == brokerId ? _self.brokerId : brokerId // ignore: cast_nullable_to_non_nullable
as String?,name: freezed == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String?,platformType: null == platformType ? _self.platformType : platformType // ignore: cast_nullable_to_non_nullable
as PlatformType,clientId: freezed == clientId ? _self.clientId : clientId // ignore: cast_nullable_to_non_nullable
as String?,isDemo: null == isDemo ? _self.isDemo : isDemo // ignore: cast_nullable_to_non_nullable
as bool,profit: freezed == profit ? _self.profit : profit // ignore: cast_nullable_to_non_nullable
as double?,accountStatus: freezed == accountStatus ? _self.accountStatus : accountStatus // ignore: cast_nullable_to_non_nullable
as String?,leadSource: freezed == leadSource ? _self.leadSource : leadSource // ignore: cast_nullable_to_non_nullable
as String?,grossProfit: freezed == grossProfit ? _self.grossProfit : grossProfit // ignore: cast_nullable_to_non_nullable
as double?,marginLevel: freezed == marginLevel ? _self.marginLevel : marginLevel // ignore: cast_nullable_to_non_nullable
as double?,primaryEmail: null == primaryEmail ? _self.primaryEmail : primaryEmail // ignore: cast_nullable_to_non_nullable
as String,accountGroup: freezed == accountGroup ? _self.accountGroup : accountGroup // ignore: cast_nullable_to_non_nullable
as String?,classification: freezed == classification ? _self.classification : classification // ignore: cast_nullable_to_non_nullable
as String?,freeMargin: freezed == freeMargin ? _self.freeMargin : freeMargin // ignore: cast_nullable_to_non_nullable
as double?,isSwapFree: null == isSwapFree ? _self.isSwapFree : isSwapFree // ignore: cast_nullable_to_non_nullable
as bool,balanceAlternateCurrency: freezed == balanceAlternateCurrency ? _self.balanceAlternateCurrency : balanceAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,marginAlternateCurrency: freezed == marginAlternateCurrency ? _self.marginAlternateCurrency : marginAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,equityAlternateCurrency: freezed == equityAlternateCurrency ? _self.equityAlternateCurrency : equityAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,profitAlternateCurrency: freezed == profitAlternateCurrency ? _self.profitAlternateCurrency : profitAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,grossProfitAlternateCurrency: freezed == grossProfitAlternateCurrency ? _self.grossProfitAlternateCurrency : grossProfitAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,creditAlternateCurrency: freezed == creditAlternateCurrency ? _self.creditAlternateCurrency : creditAlternateCurrency // ignore: cast_nullable_to_non_nullable
as double?,accountCurrencyUsdPair: freezed == accountCurrencyUsdPair ? _self.accountCurrencyUsdPair : accountCurrencyUsdPair // ignore: cast_nullable_to_non_nullable
as String?,serverName: null == serverName ? _self.serverName : serverName // ignore: cast_nullable_to_non_nullable
as String,nickName: freezed == nickName ? _self.nickName : nickName // ignore: cast_nullable_to_non_nullable
as String?,platformTypeName: null == platformTypeName ? _self.platformTypeName : platformTypeName // ignore: cast_nullable_to_non_nullable
as String,hasOpenPositions: null == hasOpenPositions ? _self.hasOpenPositions : hasOpenPositions // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}

// dart format on
